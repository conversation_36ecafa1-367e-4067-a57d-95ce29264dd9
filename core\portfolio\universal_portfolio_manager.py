import asyncio
import logging
from typing import Dict, List, Optional, Any, Tuple, Set
from dataclasses import dataclass, field
from datetime import datetime, timedelta
from decimal import Decimal, ROUND_HALF_UP
from enum import Enum
import json
from pathlib import Path

from core.interfaces.broker_interface import (
    UniversalBrokerInterface, Position, UniversalOrder, OrderResult,
    OrderSide, OrderType, OrderStatus, AssetType, AccountInfo
)
from core.config.config_manager import ConfigManager

logger = logging.getLogger(__name__)

class PositionStatus(Enum):
    OPEN = "open"
    CLOSED = "closed"
    PARTIALLY_CLOSED = "partially_closed"

class RiskLevel(Enum):
    LOW = "low"
    MEDIUM = "medium"
    HIGH = "high"
    CRITICAL = "critical"

@dataclass
class UniversalPosition:
    """Universal position representation across all brokers"""
    position_id: str
    broker_name: str
    symbol: str
    asset_type: AssetType
    side: OrderSide
    quantity: Decimal
    entry_price: Decimal
    current_price: Decimal
    unrealized_pnl: Decimal
    realized_pnl: Decimal = Decimal('0')
    status: PositionStatus = PositionStatus.OPEN
    entry_time: datetime = field(default_factory=datetime.utcnow)
    last_update: datetime = field(default_factory=datetime.utcnow)
    stop_loss: Optional[Decimal] = None
    take_profit: Optional[Decimal] = None
    commission: Decimal = Decimal('0')
    swap: Decimal = Decimal('0')
    margin_used: Decimal = Decimal('0')
    leverage: Decimal = Decimal('1')
    metadata: Dict[str, Any] = field(default_factory=dict)
    
    @property
    def market_value(self) -> Decimal:
        """Current market value of the position"""
        return self.quantity * self.current_price
    
    @property
    def total_pnl(self) -> Decimal:
        """Total P&L including realized and unrealized"""
        return self.realized_pnl + self.unrealized_pnl
    
    @property
    def pnl_percentage(self) -> Decimal:
        """P&L as percentage of entry value"""
        entry_value = self.quantity * self.entry_price
        if entry_value == 0:
            return Decimal('0')
        return (self.total_pnl / entry_value) * Decimal('100')
    
    @property
    def duration(self) -> timedelta:
        """How long the position has been open"""
        return self.last_update - self.entry_time

@dataclass
class PortfolioMetrics:
    """Portfolio performance metrics"""
    total_equity: Decimal
    total_margin_used: Decimal
    free_margin: Decimal
    unrealized_pnl: Decimal
    realized_pnl: Decimal
    total_pnl: Decimal
    margin_level: Decimal
    open_positions: int
    winning_positions: int
    losing_positions: int
    win_rate: Decimal
    largest_win: Decimal
    largest_loss: Decimal
    average_win: Decimal
    average_loss: Decimal
    profit_factor: Decimal
    sharpe_ratio: Optional[Decimal] = None
    max_drawdown: Optional[Decimal] = None
    risk_level: RiskLevel = RiskLevel.LOW
    last_updated: datetime = field(default_factory=datetime.utcnow)

@dataclass
class OrderRouting:
    """Smart order routing configuration"""
    symbol: str
    preferred_brokers: List[str]
    routing_criteria: Dict[str, Any]
    fallback_brokers: List[str]
    max_slippage: Decimal
    execution_timeout: int  # seconds

class UniversalPortfolioManager:
    """Universal portfolio manager for cross-broker trading"""
    
    def __init__(self, config_manager: ConfigManager):
        self.config_manager = config_manager
        self.brokers: Dict[str, UniversalBrokerInterface] = {}
        self.positions: Dict[str, UniversalPosition] = {}
        self.order_history: List[UniversalOrder] = []
        self.account_info: Dict[str, AccountInfo] = {}
        
        # Portfolio state
        self.portfolio_metrics: Optional[PortfolioMetrics] = None
        self.risk_limits = self._load_risk_limits()
        self.order_routing_rules: Dict[str, OrderRouting] = {}
        
        # Performance tracking
        self.daily_pnl_history: List[Tuple[datetime, Decimal]] = []
        self.equity_curve: List[Tuple[datetime, Decimal]] = []
        
        # Locks for thread safety
        self._position_lock = asyncio.Lock()
        self._order_lock = asyncio.Lock()
        
        logger.info("Universal Portfolio Manager initialized")
    
    def register_broker(self, broker_name: str, broker: UniversalBrokerInterface):
        """Register a broker with the portfolio manager"""
        self.brokers[broker_name] = broker
        logger.info(f"Registered broker: {broker_name}")
    
    def unregister_broker(self, broker_name: str):
        """Unregister a broker"""
        if broker_name in self.brokers:
            del self.brokers[broker_name]
            logger.info(f"Unregistered broker: {broker_name}")
    
    async def initialize(self):
        """Initialize the portfolio manager"""
        try:
            # Load existing positions
            await self._load_positions()
            
            # Sync with all brokers
            await self._sync_all_brokers()
            
            # Calculate initial metrics
            await self.update_portfolio_metrics()
            
            logger.info("Portfolio manager initialized successfully")
        
        except Exception as e:
            logger.error(f"Failed to initialize portfolio manager: {e}")
            raise
    
    async def _sync_all_brokers(self):
        """Sync positions and account info from all brokers"""
        sync_tasks = []
        
        for broker_name, broker in self.brokers.items():
            if broker.is_connected():
                sync_tasks.append(self._sync_broker(broker_name, broker))
        
        if sync_tasks:
            await asyncio.gather(*sync_tasks, return_exceptions=True)
    
    async def _sync_broker(self, broker_name: str, broker: UniversalBrokerInterface):
        """Sync positions from a specific broker"""
        try:
            # Get account info
            account_info = await broker.get_account_info()
            self.account_info[broker_name] = account_info
            
            # Get positions
            broker_positions = await broker.get_positions()
            
            # Update universal positions
            async with self._position_lock:
                for position in broker_positions:
                    universal_pos = self._convert_to_universal_position(
                        broker_name, position
                    )
                    self.positions[universal_pos.position_id] = universal_pos
            
            logger.info(f"Synced {len(broker_positions)} positions from {broker_name}")
        
        except Exception as e:
            logger.error(f"Failed to sync broker {broker_name}: {e}")
    
    def _convert_to_universal_position(self, broker_name: str, position: Position) -> UniversalPosition:
        """Convert broker-specific position to universal position"""
        position_id = f"{broker_name}_{position.symbol}_{position.side.value}"
        
        return UniversalPosition(
            position_id=position_id,
            broker_name=broker_name,
            symbol=position.symbol,
            asset_type=position.asset_type,
            side=position.side,
            quantity=Decimal(str(position.quantity)),
            entry_price=Decimal(str(position.entry_price)),
            current_price=Decimal(str(position.current_price)),
            unrealized_pnl=Decimal(str(position.unrealized_pnl)),
            margin_used=Decimal(str(position.margin_used)),
            leverage=Decimal(str(position.leverage))
        )
    
    async def place_order(self, 
                         symbol: str, 
                         side: OrderSide, 
                         quantity: float, 
                         order_type: OrderType = OrderType.MARKET,
                         price: Optional[float] = None,
                         stop_loss: Optional[float] = None,
                         take_profit: Optional[float] = None,
                         preferred_broker: Optional[str] = None) -> OrderResult:
        """Place an order using smart routing"""
        
        async with self._order_lock:
            try:
                # Risk checks
                if not await self._validate_order_risk(symbol, side, quantity):
                    return OrderResult(
                        success=False,
                        order_id="",
                        message="Order rejected by risk management"
                    )
                
                # Determine best broker for execution
                broker_name = await self._route_order(
                    symbol, side, quantity, order_type, preferred_broker
                )
                
                if not broker_name:
                    return OrderResult(
                        success=False,
                        order_id="",
                        message="No suitable broker found for order execution"
                    )
                
                broker = self.brokers[broker_name]
                
                # Place the order
                order = UniversalOrder(
                    symbol=symbol,
                    side=side,
                    quantity=quantity,
                    order_type=order_type,
                    price=price,
                    stop_loss=stop_loss,
                    take_profit=take_profit
                )
                
                result = await broker.place_order(order)
                
                # Track the order
                if result.success:
                    order.order_id = result.order_id
                    order.status = OrderStatus.PENDING
                    self.order_history.append(order)
                    
                    logger.info(f"Order placed successfully: {result.order_id} on {broker_name}")
                
                return result
            
            except Exception as e:
                logger.error(f"Failed to place order: {e}")
                return OrderResult(
                    success=False,
                    order_id="",
                    message=f"Order execution failed: {str(e)}"
                )
    
    async def _route_order(self, 
                          symbol: str, 
                          side: OrderSide, 
                          quantity: float, 
                          order_type: OrderType,
                          preferred_broker: Optional[str] = None) -> Optional[str]:
        """Smart order routing to find the best broker"""
        
        # If preferred broker is specified and available
        if preferred_broker and preferred_broker in self.brokers:
            broker = self.brokers[preferred_broker]
            if broker.is_connected() and await self._broker_can_execute(broker, symbol, quantity):
                return preferred_broker
        
        # Check routing rules
        if symbol in self.order_routing_rules:
            routing = self.order_routing_rules[symbol]
            
            # Try preferred brokers first
            for broker_name in routing.preferred_brokers:
                if broker_name in self.brokers:
                    broker = self.brokers[broker_name]
                    if broker.is_connected() and await self._broker_can_execute(broker, symbol, quantity):
                        return broker_name
            
            # Try fallback brokers
            for broker_name in routing.fallback_brokers:
                if broker_name in self.brokers:
                    broker = self.brokers[broker_name]
                    if broker.is_connected() and await self._broker_can_execute(broker, symbol, quantity):
                        return broker_name
        
        # Default routing: find any available broker
        for broker_name, broker in self.brokers.items():
            if broker.is_connected() and await self._broker_can_execute(broker, symbol, quantity):
                return broker_name
        
        return None
    
    async def _broker_can_execute(self, broker: UniversalBrokerInterface, symbol: str, quantity: float) -> bool:
        """Check if broker can execute the order"""
        try:
            # Check if broker supports the symbol
            # This would typically involve checking broker capabilities
            # For now, we'll assume all brokers can handle all symbols
            
            # Check account balance/margin
            account_info = await broker.get_account_info()
            if account_info.free_margin < quantity * 100:  # Simple margin check
                return False
            
            return True
        
        except Exception as e:
            logger.error(f"Error checking broker capabilities: {e}")
            return False
    
    async def _validate_order_risk(self, symbol: str, side: OrderSide, quantity: float) -> bool:
        """Validate order against risk limits"""
        try:
            # Check position size limits
            current_exposure = await self._get_symbol_exposure(symbol)
            new_exposure = current_exposure + (quantity if side == OrderSide.BUY else -quantity)
            
            max_position_size = self.risk_limits.get('max_position_size', 0.02)
            total_equity = await self._get_total_equity()
            
            if abs(new_exposure) * 100 > total_equity * max_position_size:  # Simplified calculation
                logger.warning(f"Order rejected: exceeds position size limit")
                return False
            
            # Check maximum open positions
            max_positions = self.risk_limits.get('max_open_positions', 10)
            if len(self.positions) >= max_positions and abs(current_exposure) < abs(new_exposure):
                logger.warning(f"Order rejected: maximum open positions reached")
                return False
            
            # Check daily loss limit
            daily_pnl = await self._get_daily_pnl()
            max_daily_loss = self.risk_limits.get('max_daily_loss', 0.05)
            
            if daily_pnl < -total_equity * max_daily_loss:
                logger.warning(f"Order rejected: daily loss limit reached")
                return False
            
            return True
        
        except Exception as e:
            logger.error(f"Risk validation error: {e}")
            return False
    
    async def _get_symbol_exposure(self, symbol: str) -> float:
        """Get current exposure for a symbol across all brokers"""
        exposure = 0.0
        
        for position in self.positions.values():
            if position.symbol == symbol and position.status == PositionStatus.OPEN:
                if position.side == OrderSide.BUY:
                    exposure += float(position.quantity)
                else:
                    exposure -= float(position.quantity)
        
        return exposure
    
    async def _get_total_equity(self) -> Decimal:
        """Get total equity across all brokers"""
        total_equity = Decimal('0')
        
        for account_info in self.account_info.values():
            total_equity += Decimal(str(account_info.equity))
        
        return total_equity
    
    async def _get_daily_pnl(self) -> Decimal:
        """Get today's P&L"""
        today = datetime.utcnow().date()
        daily_pnl = Decimal('0')
        
        for position in self.positions.values():
            if position.entry_time.date() == today:
                daily_pnl += position.total_pnl
        
        return daily_pnl
    
    async def close_position(self, position_id: str, quantity: Optional[float] = None) -> OrderResult:
        """Close a position (partially or fully)"""
        
        if position_id not in self.positions:
            return OrderResult(
                success=False,
                order_id="",
                message="Position not found"
            )
        
        position = self.positions[position_id]
        broker = self.brokers[position.broker_name]
        
        # Determine quantity to close
        close_quantity = quantity or float(position.quantity)
        
        # Determine opposite side
        close_side = OrderSide.SELL if position.side == OrderSide.BUY else OrderSide.BUY
        
        # Place closing order
        result = await self.place_order(
            symbol=position.symbol,
            side=close_side,
            quantity=close_quantity,
            order_type=OrderType.MARKET,
            preferred_broker=position.broker_name
        )
        
        if result.success:
            # Update position status
            async with self._position_lock:
                if close_quantity >= float(position.quantity):
                    position.status = PositionStatus.CLOSED
                else:
                    position.status = PositionStatus.PARTIALLY_CLOSED
                    position.quantity -= Decimal(str(close_quantity))
                
                position.last_update = datetime.utcnow()
        
        return result
    
    async def close_all_positions(self, symbol: Optional[str] = None) -> List[OrderResult]:
        """Close all positions or all positions for a specific symbol"""
        results = []
        
        positions_to_close = [
            pos for pos in self.positions.values()
            if pos.status == PositionStatus.OPEN and (symbol is None or pos.symbol == symbol)
        ]
        
        for position in positions_to_close:
            result = await self.close_position(position.position_id)
            results.append(result)
        
        return results
    
    async def update_portfolio_metrics(self):
        """Update portfolio performance metrics"""
        try:
            # Calculate basic metrics
            total_equity = await self._get_total_equity()
            total_margin_used = Decimal('0')
            unrealized_pnl = Decimal('0')
            realized_pnl = Decimal('0')
            
            open_positions = 0
            winning_positions = 0
            losing_positions = 0
            
            wins = []
            losses = []
            
            for position in self.positions.values():
                if position.status == PositionStatus.OPEN:
                    open_positions += 1
                    total_margin_used += position.margin_used
                    unrealized_pnl += position.unrealized_pnl
                
                realized_pnl += position.realized_pnl
                
                if position.total_pnl > 0:
                    winning_positions += 1
                    wins.append(position.total_pnl)
                elif position.total_pnl < 0:
                    losing_positions += 1
                    losses.append(abs(position.total_pnl))
            
            # Calculate derived metrics
            total_pnl = unrealized_pnl + realized_pnl
            free_margin = total_equity - total_margin_used
            margin_level = (total_equity / total_margin_used * Decimal('100')) if total_margin_used > 0 else Decimal('0')
            
            total_positions = winning_positions + losing_positions
            win_rate = (Decimal(winning_positions) / Decimal(total_positions) * Decimal('100')) if total_positions > 0 else Decimal('0')
            
            largest_win = max(wins) if wins else Decimal('0')
            largest_loss = max(losses) if losses else Decimal('0')
            average_win = sum(wins) / len(wins) if wins else Decimal('0')
            average_loss = sum(losses) / len(losses) if losses else Decimal('0')
            
            profit_factor = (sum(wins) / sum(losses)) if losses and sum(losses) > 0 else Decimal('0')
            
            # Determine risk level
            risk_level = self._calculate_risk_level(margin_level, total_pnl, total_equity)
            
            self.portfolio_metrics = PortfolioMetrics(
                total_equity=total_equity,
                total_margin_used=total_margin_used,
                free_margin=free_margin,
                unrealized_pnl=unrealized_pnl,
                realized_pnl=realized_pnl,
                total_pnl=total_pnl,
                margin_level=margin_level,
                open_positions=open_positions,
                winning_positions=winning_positions,
                losing_positions=losing_positions,
                win_rate=win_rate,
                largest_win=largest_win,
                largest_loss=largest_loss,
                average_win=average_win,
                average_loss=average_loss,
                profit_factor=profit_factor,
                risk_level=risk_level
            )
            
            # Update equity curve
            self.equity_curve.append((datetime.utcnow(), total_equity))
            
            # Keep only last 1000 points
            if len(self.equity_curve) > 1000:
                self.equity_curve = self.equity_curve[-1000:]
        
        except Exception as e:
            logger.error(f"Failed to update portfolio metrics: {e}")
    
    def _calculate_risk_level(self, margin_level: Decimal, total_pnl: Decimal, total_equity: Decimal) -> RiskLevel:
        """Calculate current risk level"""
        # Risk based on margin level
        if margin_level < Decimal('100'):
            return RiskLevel.CRITICAL
        elif margin_level < Decimal('200'):
            return RiskLevel.HIGH
        
        # Risk based on drawdown
        if total_equity > 0:
            drawdown_pct = abs(total_pnl) / total_equity * Decimal('100')
            if drawdown_pct > Decimal('20'):
                return RiskLevel.CRITICAL
            elif drawdown_pct > Decimal('10'):
                return RiskLevel.HIGH
            elif drawdown_pct > Decimal('5'):
                return RiskLevel.MEDIUM
        
        return RiskLevel.LOW
    
    def _load_risk_limits(self) -> Dict[str, float]:
        """Load risk limits from configuration"""
        trading_config = self.config_manager.trading_config
        
        return {
            'max_position_size': trading_config.max_position_size,
            'max_daily_loss': trading_config.max_daily_loss,
            'max_drawdown': trading_config.max_drawdown,
            'risk_per_trade': trading_config.risk_per_trade,
            'max_open_positions': trading_config.max_open_positions
        }
    
    async def _load_positions(self):
        """Load positions from persistent storage"""
        # This would typically load from a database
        # For now, we'll just initialize empty
        pass
    
    async def save_positions(self):
        """Save positions to persistent storage"""
        # This would typically save to a database
        # For now, we'll save to a JSON file
        try:
            positions_data = {}
            for pos_id, position in self.positions.items():
                positions_data[pos_id] = {
                    'position_id': position.position_id,
                    'broker_name': position.broker_name,
                    'symbol': position.symbol,
                    'asset_type': position.asset_type.value,
                    'side': position.side.value,
                    'quantity': str(position.quantity),
                    'entry_price': str(position.entry_price),
                    'current_price': str(position.current_price),
                    'unrealized_pnl': str(position.unrealized_pnl),
                    'realized_pnl': str(position.realized_pnl),
                    'status': position.status.value,
                    'entry_time': position.entry_time.isoformat(),
                    'last_update': position.last_update.isoformat(),
                    'stop_loss': str(position.stop_loss) if position.stop_loss else None,
                    'take_profit': str(position.take_profit) if position.take_profit else None,
                    'commission': str(position.commission),
                    'swap': str(position.swap),
                    'margin_used': str(position.margin_used),
                    'leverage': str(position.leverage),
                    'metadata': position.metadata
                }
            
            positions_file = Path("data/positions.json")
            positions_file.parent.mkdir(exist_ok=True)
            
            with open(positions_file, 'w') as f:
                json.dump(positions_data, f, indent=2)
            
            logger.info(f"Saved {len(self.positions)} positions to storage")
        
        except Exception as e:
            logger.error(f"Failed to save positions: {e}")
    
    def get_portfolio_summary(self) -> Dict[str, Any]:
        """Get a summary of the portfolio"""
        if not self.portfolio_metrics:
            return {}
        
        metrics = self.portfolio_metrics
        
        return {
            'total_equity': float(metrics.total_equity),
            'total_pnl': float(metrics.total_pnl),
            'unrealized_pnl': float(metrics.unrealized_pnl),
            'realized_pnl': float(metrics.realized_pnl),
            'margin_level': float(metrics.margin_level),
            'open_positions': metrics.open_positions,
            'win_rate': float(metrics.win_rate),
            'profit_factor': float(metrics.profit_factor),
            'risk_level': metrics.risk_level.value,
            'brokers_connected': len([b for b in self.brokers.values() if b.is_connected()]),
            'last_updated': metrics.last_updated.isoformat()
        }
    
    def get_positions_by_symbol(self, symbol: str) -> List[UniversalPosition]:
        """Get all positions for a specific symbol"""
        return [
            pos for pos in self.positions.values()
            if pos.symbol == symbol and pos.status == PositionStatus.OPEN
        ]
    
    def get_positions_by_broker(self, broker_name: str) -> List[UniversalPosition]:
        """Get all positions for a specific broker"""
        return [
            pos for pos in self.positions.values()
            if pos.broker_name == broker_name and pos.status == PositionStatus.OPEN
        ]
    
    async def emergency_close_all(self) -> Dict[str, List[OrderResult]]:
        """Emergency close all positions across all brokers"""
        logger.warning("Emergency close all positions initiated")
        
        results = {}
        
        for broker_name in self.brokers.keys():
            broker_positions = self.get_positions_by_broker(broker_name)
            broker_results = []
            
            for position in broker_positions:
                result = await self.close_position(position.position_id)
                broker_results.append(result)
            
            results[broker_name] = broker_results
        
        return results