["test_llm_chat.py::test_chat", "tests/test_llm_integration.py::TestDataManager::test_data_batch_creation", "tests/test_llm_integration.py::TestDataManager::test_data_point_creation", "tests/test_llm_integration.py::TestDataManager::test_yahoo_finance_source", "tests/test_llm_integration.py::TestEndToEndWorkflow::test_complete_trading_workflow", "tests/test_llm_integration.py::TestLLMAbstractionLayer::test_cost_estimation", "tests/test_llm_integration.py::TestLLMAbstractionLayer::test_fallback_mechanism", "tests/test_llm_integration.py::TestLLMAbstractionLayer::test_llm_initialization", "tests/test_llm_integration.py::TestLLMAbstractionLayer::test_local_provider", "tests/test_llm_integration.py::TestLLMAbstractionLayer::test_openai_provider", "tests/test_llm_integration.py::TestLLMAbstractionLayer::test_streaming_response", "tests/test_llm_integration.py::TestPerformanceBenchmarks::test_latency_benchmark", "tests/test_llm_integration.py::TestPerformanceBenchmarks::test_throughput_benchmark"]