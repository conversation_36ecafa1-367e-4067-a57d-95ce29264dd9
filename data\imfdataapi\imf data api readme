Access to IMF Data SDMX API in Python
Overview
 
The recommended Python library for SDMX data queries is sdmx1 This library is well integrated with Pandas’ library, and widely utilized for data analysis. This feature allows users to retrieve data into a dataframe that facilitates data analysis without additional data manipulations over the data message received as response to an API request. sdmx1 library has well documented use cases that can be reviewed at the following link https://sdmx1.readthedocs.io/en/v2.22.0/
This article provides guidance on how to:
Connect to the Portal as a public user
Connect to the Portal as an authenticated user
 
Connect to the Portal as a public user
 
Prerequisites
1.sdmx1
pip install sdmx1
 
Data query

Case: Retrieve data from the Integration workspace of iData PROD environment from the CPI dataset with the series key 111.CPI.CP01.IX.M:
 
Parameters
1. url – entry endpoint of the environment where the data is located
 
# import libraries
import sdmx
 
# retrieve data
data_msg = IMF_DATA.data(’CPI', key='USA+CAN.CPI.CP01.IX.M', params={'startPeriod': 2018})
 
# convert to pandas
cpi_df = data_msg.to_pandas()
 
 
Connecting to portal as an authenticated user
 
Prerequisites
1. SDMX1
pip install sdmx1
2. MSAL Microsoft Authentication Library (MSAL)
pip install msal
 
Authentication and Data query
 
Case: Retrieve data from the Integration workspace of iData PROD environment from the CPI dataset with the series key 111.CPI.CP01.IX.M:
 
1. scope – scope indicating target iData environment.
2. authority – Azure AD url to which the user is redirected for the authorization process
3. client_id – application (client) ID that the Azure AD app registrations assigned to the app (iData)
4. url – entry endpoint of the environment where the data is located
 
# import libraries
import  sdmx
from msal import PublicClientApplication


# parameter values for authorization and data requests
client_id = "446ce2fa-88b1-436c-b8e6-94491ca4f6fb"
authority = https://imfprdb2c.b2clogin.com/imfprdb2c.onmicrosoft.com/b2c_1a_signin_aad_simple_user_journey/
scope = https://imfprdb2c.onmicrosoft.com/4042e178-3e2f-4ff9-ac38-1276c901c13d/iData.Login
# authorize and retrieve access token
app = PublicClientApplication(client_id,authority=authority)
token = None
token = app.acquire_token_interactive(scopes=[scope])
# define header for a request
header = {'Authorization': f"{token['token_type']} {token['access_token']}"}

# retrieve data
data_msg = IMF_DATA.data('CPI', key='111.CPI.CP01.IX.M', params={'startPeriod': 2018}, headers=header)

# convert to pandas
cpi_df = data_msg.to_pandas()