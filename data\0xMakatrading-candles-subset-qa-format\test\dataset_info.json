{"builder_name": "parquet", "citation": "", "config_name": "default", "dataset_name": "trading-candles-subset-qa-format", "dataset_size": 95722658, "description": "", "download_checksums": {"hf://datasets/0xMaka/trading-candles-subset-qa-format@f98e2efc5fb884c6f78b5796c21b6555c980eec8/data/train-00000-of-00001-72e2dc587408353e.parquet": {"num_bytes": 38376275, "checksum": null}, "hf://datasets/0xMaka/trading-candles-subset-qa-format@f98e2efc5fb884c6f78b5796c21b6555c980eec8/data/test-00000-of-00001-39382d7545aba4d8.parquet": {"num_bytes": 16452379, "checksum": null}}, "download_size": 54828654, "features": {"context": {"dtype": "string", "_type": "Value"}, "question": {"dtype": "string", "_type": "Value"}, "answers": {"answer_start": {"feature": {"dtype": "int64", "_type": "Value"}, "_type": "Sequence"}, "text": {"feature": {"dtype": "string", "_type": "Value"}, "_type": "Sequence"}}, "id": {"dtype": "string", "_type": "Value"}}, "homepage": "", "license": "", "size_in_bytes": 150551312, "splits": {"train": {"name": "train", "num_bytes": 67005029, "num_examples": 280033, "dataset_name": "trading-candles-subset-qa-format"}, "test": {"name": "test", "num_bytes": 28717629, "num_examples": 120015, "dataset_name": "trading-candles-subset-qa-format"}}, "version": {"version_str": "0.0.0", "major": 0, "minor": 0, "patch": 0}}