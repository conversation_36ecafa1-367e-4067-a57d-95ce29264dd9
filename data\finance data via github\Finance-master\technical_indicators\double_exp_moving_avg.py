# Import dependencies
import numpy as np
import pandas as pd
import matplotlib.pyplot as plt
import yfinance as yf
import datetime as dt
yf.pdr_override()
import sys
import os
parent_dir = os.path.dirname(os.getcwd())
sys.path.append(parent_dir)
import ta_functions as ta

# input
symbol = "AAPL"
start = dt.date.today() - dt.timedelta(days=365 * 2)
end = dt.date.today()

# Read data
df = yf.download(symbol, start, end)

df["EMA"] = ta.EMA(df["Adj Close"], timeperiod=5)
df["EMA_S"] = ta.EMA(df["EMA"], timeperiod=5)
df["DEMA"] = (2 * df["EMA"]) - df["EMA_S"]

# Line Chart
fig = plt.figure(figsize=(16, 8))
ax1 = plt.subplot(111)
ax1.plot(df.index, df["Adj Close"])
ax1.plot(df.index, df["DEMA"])
ax1.axhline(y=df["Adj Close"].mean(), color="r")
ax1.grid()
# ax1.grid(True, which='both')
# ax1.grid(which='minor', linestyle='-', linewidth='0.5', color='black')
# ax1.grid(which='major', linestyle='-', linewidth='0.5', color='red')
# ax1.minorticks_on()
ax1.legend(loc="best")
ax1v = ax1.twinx()
ax1v.fill_between(df.index[0:], 0, df.Volume[0:], facecolor="#0079a3", alpha=0.4)
ax1v.axes.yaxis.set_ticklabels([])
ax1v.set_ylim(0, 3 * df.Volume.max())
ax1.set_title("Stock " + symbol + " Closing Price")
ax1.set_ylabel("Price")
plt.show()

# ## Candlestick with DEMA
from matplotlib import dates as mdates

dfc = df.copy()
dfc["VolumePositive"] = dfc["Open"] < dfc["Adj Close"]
dfc = dfc.dropna()
dfc = dfc.reset_index()
dfc["Date"] = mdates.date2num(dfc["Date"].tolist())
from mplfinance.original_flavor import candlestick_ohlc

fig = plt.figure(figsize=(16, 8))
ax1 = plt.subplot(111)
candlestick_ohlc(ax1, dfc.values, width=0.5, colorup="g", colordown="r", alpha=1.0)
ax1.plot(df.index, df["DEMA"])
ax1.xaxis_date()
ax1.xaxis.set_major_formatter(mdates.DateFormatter("%d-%m-%Y"))
ax1.grid(True, which="both")
ax1.minorticks_on()
ax1v = ax1.twinx()
colors = dfc.VolumePositive.map({True: "g", False: "r"})
ax1v.bar(dfc.Date, dfc["Volume"], color=colors, alpha=0.4)
ax1v.axes.yaxis.set_ticklabels([])
ax1v.set_ylim(0, 3 * df.Volume.max())
ax1.set_title("Stock " + symbol + " Closing Price")
ax1.set_ylabel("Price")
plt.show()