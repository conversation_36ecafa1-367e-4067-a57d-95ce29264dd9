import os
import yaml
import json
from pathlib import Path
from typing import Dict, List, Optional, Any, Union
from dataclasses import dataclass, asdict, field
from datetime import datetime
import logging
from cryptography.fernet import Fernet
import base64
from enum import Enum

logger = logging.getLogger(__name__)

class LLMProviderType(Enum):
    """LLM Provider types"""
    OPENAI = "openai"
    ANTHROPIC = "anthropic"
    DEEPSEEK = "deepseek"
    GEMINI = "gemini"
    QWEN_LOCAL = "qwen_local"
    OLLAMA = "ollama"
    HUGGINGFACE = "huggingface"
    CUSTOM = "custom"

@dataclass
class BrokerCredentials:
    """Secure storage for broker credentials"""
    broker_name: str
    api_key: Optional[str] = None
    secret_key: Optional[str] = None
    username: Optional[str] = None
    password: Optional[str] = None
    additional_fields: Dict[str, str] = None
    
    def __post_init__(self):
        if self.additional_fields is None:
            self.additional_fields = {}

@dataclass
class TradingConfig:
    """Trading configuration settings"""
    max_position_size: float = 0.02  # 2% of account
    max_daily_loss: float = 0.05     # 5% daily loss limit
    max_drawdown: float = 0.10       # 10% max drawdown
    risk_per_trade: float = 0.01     # 1% risk per trade
    max_open_positions: int = 5
    enable_stop_loss: bool = True
    enable_take_profit: bool = True
    default_stop_loss_pips: int = 50
    default_take_profit_pips: int = 100

@dataclass
class LLMProviderConfig:
    """Configuration for individual LLM providers"""
    enabled: bool = True
    provider_type: LLMProviderType = LLMProviderType.OPENAI
    api_key: Optional[str] = None
    base_url: Optional[str] = None
    model: str = "gpt-3.5-turbo"
    max_tokens: int = 1000
    temperature: float = 0.1
    timeout: int = 30
    retry_attempts: int = 3
    rate_limit: Dict[str, int] = field(default_factory=lambda: {"requests_per_minute": 60})
    cost_per_1k_tokens: Dict[str, float] = field(default_factory=lambda: {"input": 0.001, "output": 0.002})
    
    # Local model specific
    model_path: Optional[str] = None
    inference_url: Optional[str] = None
    gpu_enabled: bool = True
    quantization: Optional[str] = None  # int4, int8, fp16
    
    # Advanced settings
    context_window: int = 4096
    supports_streaming: bool = True
    supports_function_calling: bool = False
    priority: int = 1  # Lower number = higher priority
    
    def validate(self) -> List[str]:
        """Validate LLM provider configuration"""
        errors = []
        
        # Skip validation for disabled providers except for basic parameter checks
        if not self.enabled:
            return errors
        
        if self.provider_type in [LLMProviderType.OPENAI, LLMProviderType.ANTHROPIC, 
                                 LLMProviderType.DEEPSEEK, LLMProviderType.GEMINI] and not self.api_key:
            errors.append(f"API key required for {self.provider_type.value} provider")
        
        if self.provider_type in [LLMProviderType.QWEN_LOCAL, LLMProviderType.OLLAMA] and not (self.model_path or self.inference_url):
            errors.append(f"Model path or inference URL required for {self.provider_type.value} provider")
        
        if self.max_tokens <= 0:
            errors.append("max_tokens must be positive")
        
        if not 0 <= self.temperature <= 2:
            errors.append("temperature must be between 0 and 2")
        
        if self.timeout <= 0:
            errors.append("timeout must be positive")
        
        return errors

@dataclass
class AIConfig:
    """AI model configuration"""
    local_model_name: str = "qwen3-8b"
    local_model_path: str = "qwen3/Qwen3-8B-Q4_K_M.gguf"
    api_models: List[str] = None
    confidence_threshold: float = 0.7
    ensemble_voting: bool = True
    max_api_calls_per_hour: int = 100
    enable_fine_tuning: bool = True
    
    # LLM Provider configurations
    llm_providers: Dict[str, LLMProviderConfig] = field(default_factory=dict)
    fallback_order: List[str] = field(default_factory=list)
    
    # Cost management
    daily_cost_limit: float = 50.0  # USD
    cost_tracking_enabled: bool = True
    
    # Performance settings
    max_concurrent_requests: int = 10
    request_timeout: int = 300
    cache_enabled: bool = True
    cache_ttl: int = 3600  # seconds
    
    def __post_init__(self):
        if self.api_models is None:
            self.api_models = ["local"]
        
        # Initialize default LLM providers if none exist
        if not self.llm_providers:
            self.llm_providers = self._get_default_llm_providers()
        
        # Set default fallback order if none exists
        if not self.fallback_order:
            self.fallback_order = ["local"]
    
    def _get_default_llm_providers(self) -> Dict[str, LLMProviderConfig]:
        """Get default LLM provider configurations"""
        return {
            "openai": LLMProviderConfig(
                enabled=False,
                provider_type=LLMProviderType.OPENAI,
                model="gpt-4-turbo-preview",
                cost_per_1k_tokens={"input": 0.01, "output": 0.03}
            ),
            "anthropic": LLMProviderConfig(
                enabled=False,
                provider_type=LLMProviderType.ANTHROPIC,
                model="claude-3-sonnet-20240229",
                cost_per_1k_tokens={"input": 0.003, "output": 0.015}
            ),
            "deepseek": LLMProviderConfig(
                enabled=False,
                provider_type=LLMProviderType.DEEPSEEK,
                model="deepseek-chat",
                cost_per_1k_tokens={"input": 0.0014, "output": 0.0028}
            ),
            "local": LLMProviderConfig(
                enabled=True,
                provider_type=LLMProviderType.QWEN_LOCAL,
                model="qwen3-8b",
                model_path="qwen3/Qwen3-8B-Q4_K_M.gguf",
                cost_per_1k_tokens={"input": 0.0, "output": 0.0},
                gpu_enabled=False,
                quantization="Q4_K_M",
                max_tokens=2000,
                timeout=15
            )
        }
    
    def validate_llm_config(self) -> List[str]:
        """Validate LLM configuration"""
        errors = []
        
        # Validate each provider
        for name, provider in self.llm_providers.items():
            provider_errors = provider.validate()
            errors.extend([f"LLM Provider '{name}': {error}" for error in provider_errors])
        
        # Validate fallback order
        for provider_name in self.fallback_order:
            if provider_name not in self.llm_providers:
                errors.append(f"Fallback provider '{provider_name}' not found in LLM providers")
        
        # Check if at least one provider is enabled
        enabled_providers = [name for name, provider in self.llm_providers.items() if provider.enabled]
        if not enabled_providers:
            errors.append("At least one LLM provider must be enabled")
        
        return errors

@dataclass
class SystemConfig:
    """System-wide configuration"""
    environment: str = "development"
    log_level: str = "INFO"
    data_retention_days: int = 30
    backup_enabled: bool = True
    monitoring_enabled: bool = True
    performance_tracking: bool = True
    max_concurrent_trades: int = 10
    heartbeat_interval: int = 30  # seconds

class ConfigManager:
    """Centralized configuration management system"""
    
    def __init__(self, config_dir: str = "config", environment: str = "development"):
        self.config_dir = Path(config_dir)
        self.environment = environment
        self.encryption_key = None
        
        # Ensure config directories exist
        self._create_config_structure()
        
        # Load encryption key
        self._load_or_create_encryption_key()
        
        # Load configurations
        self.system_config = self._load_system_config()
        self.trading_config = self._load_trading_config()
        self.ai_config = self._load_ai_config()
        self.broker_configs = self._load_broker_configs()
        self.credentials = self._load_credentials()
    
    def _create_config_structure(self):
        """Create configuration directory structure"""
        directories = [
            self.config_dir,
            self.config_dir / "brokers",
            self.config_dir / "strategies",
            self.config_dir / "risk",
            self.config_dir / "ai",
            self.config_dir / "credentials",
            self.config_dir / "environments"
        ]
        
        for directory in directories:
            directory.mkdir(parents=True, exist_ok=True)
    
    def _load_or_create_encryption_key(self):
        """Load or create encryption key for credentials"""
        key_file = self.config_dir / "credentials" / ".key"
        
        if key_file.exists():
            with open(key_file, 'rb') as f:
                self.encryption_key = f.read()
        else:
            # Generate new key
            self.encryption_key = Fernet.generate_key()
            with open(key_file, 'wb') as f:
                f.write(self.encryption_key)
            
            # Set restrictive permissions
            os.chmod(key_file, 0o600)
    
    def _encrypt_data(self, data: str) -> str:
        """Encrypt sensitive data"""
        fernet = Fernet(self.encryption_key)
        encrypted_data = fernet.encrypt(data.encode())
        return base64.b64encode(encrypted_data).decode()
    
    def _decrypt_data(self, encrypted_data: str) -> str:
        """Decrypt sensitive data"""
        fernet = Fernet(self.encryption_key)
        decoded_data = base64.b64decode(encrypted_data.encode())
        return fernet.decrypt(decoded_data).decode()
    
    def _load_yaml_config(self, file_path: Path, default_data: Dict = None) -> Dict[str, Any]:
        """Load YAML configuration file"""
        if file_path.exists():
            try:
                with open(file_path, 'r') as f:
                    return yaml.safe_load(f) or {}
            except Exception as e:
                logger.error(f"Failed to load config {file_path}: {e}")
                return default_data or {}
        else:
            # Create default config if it doesn't exist
            if default_data:
                self._save_yaml_config(file_path, default_data)
            return default_data or {}
    
    def _save_yaml_config(self, file_path: Path, data: Dict[str, Any]):
        """Save YAML configuration file"""
        try:
            with open(file_path, 'w') as f:
                yaml.dump(data, f, default_flow_style=False, indent=2)
        except Exception as e:
            logger.error(f"Failed to save config {file_path}: {e}")
    
    def _load_system_config(self) -> SystemConfig:
        """Load system configuration"""
        config_file = self.config_dir / "environments" / f"{self.environment}.yaml"
        default_config = asdict(SystemConfig())
        
        config_data = self._load_yaml_config(config_file, default_config)
        return SystemConfig(**config_data)
    
    def _load_trading_config(self) -> TradingConfig:
        """Load trading configuration"""
        config_file = self.config_dir / "risk" / "trading.yaml"
        default_config = asdict(TradingConfig())
        
        config_data = self._load_yaml_config(config_file, default_config)
        return TradingConfig(**config_data)
    
    def _load_ai_config(self) -> AIConfig:
        """Load AI configuration"""
        config_file = self.config_dir / "ai" / "models.yaml"
        default_config = asdict(AIConfig())
        
        config_data = self._load_yaml_config(config_file, default_config)
        
        # Convert llm_providers dictionaries back to LLMProviderConfig objects
        if 'llm_providers' in config_data:
            llm_providers = {}
            for name, provider_dict in config_data['llm_providers'].items():
                if isinstance(provider_dict, dict):
                    # Convert provider_type string back to enum if needed
                    if 'provider_type' in provider_dict and isinstance(provider_dict['provider_type'], str):
                        provider_dict['provider_type'] = LLMProviderType(provider_dict['provider_type'])
                    llm_providers[name] = LLMProviderConfig(**provider_dict)
                else:
                    llm_providers[name] = provider_dict
            config_data['llm_providers'] = llm_providers
        
        return AIConfig(**config_data)
    
    def _load_broker_configs(self) -> Dict[str, Dict[str, Any]]:
        """Load all broker configurations"""
        broker_configs = {}
        brokers_dir = self.config_dir / "brokers"
        
        for config_file in brokers_dir.glob("*.yaml"):
            broker_name = config_file.stem
            config_data = self._load_yaml_config(config_file)
            if config_data:
                broker_configs[broker_name] = config_data
        
        return broker_configs
    
    def _load_credentials(self) -> Dict[str, BrokerCredentials]:
        """Load encrypted broker credentials"""
        credentials = {}
        credentials_file = self.config_dir / "credentials" / "brokers.json"
        
        if credentials_file.exists():
            try:
                with open(credentials_file, 'r') as f:
                    encrypted_data = json.load(f)
                
                for broker_name, encrypted_creds in encrypted_data.items():
                    try:
                        # Decrypt credentials
                        decrypted_data = {}
                        for key, value in encrypted_creds.items():
                            if key in ['api_key', 'secret_key', 'password'] and value:
                                decrypted_data[key] = self._decrypt_data(value)
                            else:
                                decrypted_data[key] = value
                        
                        credentials[broker_name] = BrokerCredentials(**decrypted_data)
                    
                    except Exception as e:
                        logger.error(f"Failed to decrypt credentials for {broker_name}: {e}")
            
            except Exception as e:
                logger.error(f"Failed to load credentials file: {e}")
        
        return credentials
    
    def save_broker_credentials(self, broker_name: str, credentials: BrokerCredentials):
        """Save encrypted broker credentials"""
        credentials_file = self.config_dir / "credentials" / "brokers.json"
        
        # Load existing credentials
        existing_creds = {}
        if credentials_file.exists():
            try:
                with open(credentials_file, 'r') as f:
                    existing_creds = json.load(f)
            except Exception as e:
                logger.error(f"Failed to load existing credentials: {e}")
        
        # Encrypt sensitive fields
        encrypted_creds = asdict(credentials)
        for field in ['api_key', 'secret_key', 'password']:
            if encrypted_creds.get(field):
                encrypted_creds[field] = self._encrypt_data(encrypted_creds[field])
        
        # Update credentials
        existing_creds[broker_name] = encrypted_creds
        
        # Save to file
        try:
            with open(credentials_file, 'w') as f:
                json.dump(existing_creds, f, indent=2)
            
            # Set restrictive permissions
            os.chmod(credentials_file, 0o600)
            
            # Update in-memory credentials
            self.credentials[broker_name] = credentials
            
            logger.info(f"Saved credentials for broker {broker_name}")
        
        except Exception as e:
            logger.error(f"Failed to save credentials for {broker_name}: {e}")
    
    def get_broker_credentials(self, broker_name: str) -> Optional[BrokerCredentials]:
        """Get broker credentials"""
        return self.credentials.get(broker_name)
    
    def get_broker_config(self, broker_name: str) -> Optional[Dict[str, Any]]:
        """Get broker configuration"""
        return self.broker_configs.get(broker_name)
    
    def validate_broker_credentials(self, broker_name: str, credentials: Dict[str, str]) -> bool:
        """Validate broker credentials against requirements"""
        broker_config = self.get_broker_config(broker_name)
        if not broker_config:
            return False
        
        auth_config = broker_config.get('broker', {}).get('authentication', {})
        required_fields = auth_config.get('required_fields', [])
        
        return all(field in credentials and credentials[field] for field in required_fields)
    
    def create_broker_config(self, broker_name: str, config_data: Dict[str, Any]):
        """Create a new broker configuration"""
        config_file = self.config_dir / "brokers" / f"{broker_name}.yaml"
        self._save_yaml_config(config_file, config_data)
        self.broker_configs[broker_name] = config_data
        logger.info(f"Created broker config for {broker_name}")
    
    def update_trading_config(self, **kwargs):
        """Update trading configuration"""
        for key, value in kwargs.items():
            if hasattr(self.trading_config, key):
                setattr(self.trading_config, key, value)
        
        # Save to file
        config_file = self.config_dir / "risk" / "trading.yaml"
        self._save_yaml_config(config_file, asdict(self.trading_config))
        logger.info("Updated trading configuration")
    
    def update_ai_config(self, **kwargs):
        """Update AI configuration"""
        for key, value in kwargs.items():
            if hasattr(self.ai_config, key):
                setattr(self.ai_config, key, value)
        
        # Save to file
        config_file = self.config_dir / "ai" / "models.yaml"
        self._save_yaml_config(config_file, asdict(self.ai_config))
        logger.info("Updated AI configuration")
    
    def get_environment_config(self) -> Dict[str, Any]:
        """Get environment-specific configuration"""
        return {
            'environment': self.environment,
            'system': asdict(self.system_config),
            'trading': asdict(self.trading_config),
            'ai': asdict(self.ai_config)
        }
    
    def export_config(self, include_credentials: bool = False) -> Dict[str, Any]:
        """Export all configuration for backup"""
        export_data = {
            'timestamp': datetime.utcnow().isoformat(),
            'environment': self.environment,
            'system_config': asdict(self.system_config),
            'trading_config': asdict(self.trading_config),
            'ai_config': asdict(self.ai_config),
            'broker_configs': self.broker_configs
        }
        
        if include_credentials:
            # Export credentials (still encrypted)
            credentials_file = self.config_dir / "credentials" / "brokers.json"
            if credentials_file.exists():
                with open(credentials_file, 'r') as f:
                    export_data['encrypted_credentials'] = json.load(f)
        
        return export_data
    
    def import_config(self, config_data: Dict[str, Any], overwrite: bool = False):
        """Import configuration from backup"""
        try:
            # Import system config
            if 'system_config' in config_data:
                if overwrite or not hasattr(self, 'system_config'):
                    self.system_config = SystemConfig(**config_data['system_config'])
            
            # Import trading config
            if 'trading_config' in config_data:
                if overwrite or not hasattr(self, 'trading_config'):
                    self.trading_config = TradingConfig(**config_data['trading_config'])
            
            # Import AI config
            if 'ai_config' in config_data:
                if overwrite or not hasattr(self, 'ai_config'):
                    self.ai_config = AIConfig(**config_data['ai_config'])
            
            # Import broker configs
            if 'broker_configs' in config_data:
                for broker_name, broker_config in config_data['broker_configs'].items():
                    if overwrite or broker_name not in self.broker_configs:
                        self.create_broker_config(broker_name, broker_config)
            
            # Import credentials if provided
            if 'encrypted_credentials' in config_data:
                credentials_file = self.config_dir / "credentials" / "brokers.json"
                with open(credentials_file, 'w') as f:
                    json.dump(config_data['encrypted_credentials'], f, indent=2)
                
                # Reload credentials
                self.credentials = self._load_credentials()
            
            logger.info("Configuration imported successfully")
        
        except Exception as e:
            logger.error(f"Failed to import configuration: {e}")
            raise
    
    def validate_configuration(self) -> Dict[str, List[str]]:
        """Validate all configuration settings"""
        issues = {
            'errors': [],
            'warnings': []
        }
        
        # Validate trading config
        if self.trading_config.max_position_size > 0.1:
            issues['warnings'].append("Max position size > 10% is risky")
        
        if self.trading_config.risk_per_trade > 0.05:
            issues['warnings'].append("Risk per trade > 5% is very aggressive")
        
        # Validate AI config
        if not Path(self.ai_config.local_model_path).exists():
            issues['errors'].append(f"Local model path does not exist: {self.ai_config.local_model_path}")
        
        # Validate LLM configuration
        llm_errors = self.ai_config.validate_llm_config()
        issues['errors'].extend(llm_errors)
        
        # Validate broker configs
        for broker_name, config in self.broker_configs.items():
            if 'broker' not in config:
                issues['errors'].append(f"Broker {broker_name} missing 'broker' section")
            
            # Check if credentials exist
            if broker_name not in self.credentials:
                issues['warnings'].append(f"No credentials configured for broker {broker_name}")
        
        return issues
    
    def get_llm_provider_config(self, provider_name: str) -> Optional[LLMProviderConfig]:
        """Get specific LLM provider configuration"""
        return self.ai_config.llm_providers.get(provider_name)
    
    def add_llm_provider(self, provider_name: str, config: LLMProviderConfig) -> bool:
        """Add a new LLM provider configuration"""
        try:
            # Validate the configuration
            errors = config.validate()
            if errors:
                logger.error(f"LLM provider validation errors: {errors}")
                return False
            
            # Add to configuration
            self.ai_config.llm_providers[provider_name] = config
            
            # Add to fallback order if not present
            if provider_name not in self.ai_config.fallback_order:
                self.ai_config.fallback_order.append(provider_name)
            
            # Save configuration
            self._save_ai_config()
            
            logger.info(f"Added LLM provider: {provider_name}")
            return True
        
        except Exception as e:
            logger.error(f"Failed to add LLM provider {provider_name}: {e}")
            return False
    
    def update_llm_provider(self, provider_name: str, **kwargs) -> bool:
        """Update LLM provider configuration"""
        try:
            if provider_name not in self.ai_config.llm_providers:
                logger.error(f"LLM provider {provider_name} not found")
                return False
            
            provider = self.ai_config.llm_providers[provider_name]
            
            # Update fields
            for key, value in kwargs.items():
                if hasattr(provider, key):
                    setattr(provider, key, value)
                else:
                    logger.warning(f"Unknown field {key} for LLM provider")
            
            # Validate updated configuration
            errors = provider.validate()
            if errors:
                logger.error(f"LLM provider validation errors: {errors}")
                return False
            
            # Save configuration
            self._save_ai_config()
            
            logger.info(f"Updated LLM provider: {provider_name}")
            return True
        
        except Exception as e:
            logger.error(f"Failed to update LLM provider {provider_name}: {e}")
            return False
    
    def remove_llm_provider(self, provider_name: str) -> bool:
        """Remove LLM provider configuration"""
        try:
            if provider_name not in self.ai_config.llm_providers:
                logger.error(f"LLM provider {provider_name} not found")
                return False
            
            # Remove from providers
            del self.ai_config.llm_providers[provider_name]
            
            # Remove from fallback order
            if provider_name in self.ai_config.fallback_order:
                self.ai_config.fallback_order.remove(provider_name)
            
            # Save configuration
            self._save_ai_config()
            
            logger.info(f"Removed LLM provider: {provider_name}")
            return True
        
        except Exception as e:
            logger.error(f"Failed to remove LLM provider {provider_name}: {e}")
            return False
    
    def set_llm_fallback_order(self, fallback_order: List[str]) -> bool:
        """Set LLM provider fallback order"""
        try:
            # Validate that all providers exist
            for provider_name in fallback_order:
                if provider_name not in self.ai_config.llm_providers:
                    logger.error(f"Provider {provider_name} not found in LLM providers")
                    return False
            
            self.ai_config.fallback_order = fallback_order
            self._save_ai_config()
            
            logger.info(f"Updated LLM fallback order: {fallback_order}")
            return True
        
        except Exception as e:
            logger.error(f"Failed to set LLM fallback order: {e}")
            return False
    
    def get_enabled_llm_providers(self) -> Dict[str, LLMProviderConfig]:
        """Get all enabled LLM providers"""
        return {name: config for name, config in self.ai_config.llm_providers.items() if config.enabled}
    
    def save_llm_api_key(self, provider_name: str, api_key: str) -> bool:
        """Securely save LLM provider API key"""
        try:
            if provider_name not in self.ai_config.llm_providers:
                logger.error(f"LLM provider {provider_name} not found")
                return False
            
            # Encrypt and save API key
            encrypted_key = self._encrypt_data(api_key)
            
            # Load existing LLM credentials
            llm_creds_file = self.config_dir / "credentials" / "llm_providers.json"
            existing_creds = {}
            
            if llm_creds_file.exists():
                try:
                    with open(llm_creds_file, 'r') as f:
                        existing_creds = json.load(f)
                except Exception as e:
                    logger.error(f"Failed to load existing LLM credentials: {e}")
            
            # Update credentials
            existing_creds[provider_name] = {
                'api_key': encrypted_key,
                'updated_at': datetime.utcnow().isoformat()
            }
            
            # Save credentials
            with open(llm_creds_file, 'w') as f:
                json.dump(existing_creds, f, indent=2)
            
            # Set restrictive permissions
            os.chmod(llm_creds_file, 0o600)
            
            # Update in-memory configuration
            self.ai_config.llm_providers[provider_name].api_key = api_key
            
            logger.info(f"Saved API key for LLM provider: {provider_name}")
            return True
        
        except Exception as e:
            logger.error(f"Failed to save API key for {provider_name}: {e}")
            return False
    
    def load_llm_api_keys(self) -> bool:
        """Load encrypted LLM API keys"""
        try:
            llm_creds_file = self.config_dir / "credentials" / "llm_providers.json"
            
            if not llm_creds_file.exists():
                logger.info("No LLM credentials file found")
                return True
            
            with open(llm_creds_file, 'r') as f:
                encrypted_creds = json.load(f)
            
            for provider_name, creds in encrypted_creds.items():
                if provider_name in self.ai_config.llm_providers:
                    try:
                        # Decrypt API key
                        api_key = self._decrypt_data(creds['api_key'])
                        self.ai_config.llm_providers[provider_name].api_key = api_key
                    except Exception as e:
                        logger.error(f"Failed to decrypt API key for {provider_name}: {e}")
            
            logger.info("Loaded LLM API keys")
            return True
        
        except Exception as e:
            logger.error(f"Failed to load LLM API keys: {e}")
            return False
    
    def _save_ai_config(self):
        """Save AI configuration to file"""
        config_file = self.config_dir / "ai" / "models.yaml"
        
        # Convert to dict for saving (excluding sensitive data)
        ai_config_dict = asdict(self.ai_config)
        
        # Remove API keys from saved config (they're stored separately encrypted)
        for provider_name, provider_config in ai_config_dict.get('llm_providers', {}).items():
            if 'api_key' in provider_config:
                provider_config['api_key'] = None
        
        self._save_yaml_config(config_file, ai_config_dict)
    
    def get_config_summary(self) -> Dict[str, Any]:
        """Get a summary of current configuration"""
        enabled_llm_providers = self.get_enabled_llm_providers()
        
        return {
            'environment': self.environment,
            'brokers_configured': len(self.broker_configs),
            'brokers_with_credentials': len(self.credentials),
            'trading_config': {
                'max_position_size': self.trading_config.max_position_size,
                'risk_per_trade': self.trading_config.risk_per_trade,
                'max_open_positions': self.trading_config.max_open_positions
            },
            'ai_config': {
                'local_model': self.ai_config.local_model_name,
                'api_models': self.ai_config.api_models,
                'confidence_threshold': self.ai_config.confidence_threshold,
                'llm_providers_total': len(self.ai_config.llm_providers),
                'llm_providers_enabled': len(enabled_llm_providers),
                'llm_fallback_order': self.ai_config.fallback_order,
                'daily_cost_limit': self.ai_config.daily_cost_limit
            },
            'system_config': {
                'log_level': self.system_config.log_level,
                'monitoring_enabled': self.system_config.monitoring_enabled,
                'backup_enabled': self.system_config.backup_enabled
            }
        }

# Global config manager instance
_global_config_manager: Optional[ConfigManager] = None

def get_global_config_manager() -> ConfigManager:
    """Get the global configuration manager instance"""
    global _global_config_manager
    if _global_config_manager is None:
        _global_config_manager = ConfigManager()
    return _global_config_manager

def set_global_config_manager(config_manager: ConfigManager) -> None:
    """Set the global configuration manager instance"""
    global _global_config_manager
    _global_config_manager = config_manager