#!/usr/bin/env python3
"""
Simple system integration test for Noryon Trading AI
Tests the core components and LLM integration
"""

import asyncio
import sys
from pathlib import Path

# Add project root to path
project_root = Path(__file__).parent
sys.path.insert(0, str(project_root))

async def test_llm_integration():
    """Test LLM integration"""
    print("🧠 Testing LLM Integration...")

    try:
        from core.llm.llm_abstraction_layer import LLMAbstractionLayer, LLMRequest

        # Test LLM layer initialization with config file path
        llm_layer = LLMAbstractionLayer("config/llm_brain_config.yaml")

        print("✅ LLM layer initialized successfully")

        # Test a simple generation
        request = LLMRequest(
            prompt="Test prompt: What is 2+2?",
            max_tokens=50,
            temperature=0.1
        )

        response = await llm_layer.generate(request)

        if response.success:
            print(f"✅ LLM generation successful: {response.content[:100]}...")
            print(f"   Provider: {response.provider}")
            print(f"   Latency: {response.latency:.2f}s")
            print(f"   Cost: ${response.cost:.4f}")
        else:
            print(f"❌ LLM generation failed: {response.error}")

        await llm_layer.close()
        return True

    except Exception as e:
        print(f"❌ LLM integration test failed: {e}")
        return False

async def test_data_manager():
    """Test data manager"""
    print("\n📊 Testing Data Manager...")

    try:
        from core.data.data_manager import DataManager

        # Create a simple config file for data manager
        import tempfile
        import json

        data_config = {
            "data_sources": {
                "yahoo_finance": {
                    "enabled": True,
                    "source": "yahoo_finance"
                }
            },
            "storage": {
                "database_url": "sqlite:///test.db",
                "redis_url": "redis://localhost:6379",
                "file_storage_path": "data"
            },
            "batch_size": 50,
            "processing_interval": 30
        }

        with tempfile.NamedTemporaryFile(mode='w', suffix='.json', delete=False) as f:
            json.dump(data_config, f)
            config_path = f.name

        data_manager = DataManager(config_path)
        print("✅ Data manager initialized successfully")

        # Test basic functionality
        status = data_manager.get_status()
        print(f"   Status: {status}")

        # Cleanup
        import os
        os.unlink(config_path)

        return True

    except Exception as e:
        print(f"❌ Data manager test failed: {e}")
        return False

async def test_config_manager():
    """Test configuration manager"""
    print("\n⚙️ Testing Configuration Manager...")
    
    try:
        from core.config.config_manager import ConfigManager
        
        config_manager = ConfigManager("config", "development")
        
        # Test configuration loading
        issues = config_manager.validate_configuration()
        
        if issues['errors']:
            print("❌ Configuration errors found:")
            for error in issues['errors']:
                print(f"   • {error}")
            return False
        
        if issues['warnings']:
            print("⚠️ Configuration warnings:")
            for warning in issues['warnings']:
                print(f"   • {warning}")
        
        print("✅ Configuration manager working correctly")
        return True
        
    except Exception as e:
        print(f"❌ Configuration manager test failed: {e}")
        return False

async def test_broker_registry():
    """Test broker registry"""
    print("\n🏦 Testing Broker Registry...")
    
    try:
        from core.registry.broker_registry import BrokerRegistry
        
        registry = BrokerRegistry()
        await registry.initialize()
        
        # Discover available brokers
        brokers = await registry.discover_brokers()
        print(f"✅ Broker registry initialized, found {len(brokers)} broker configs")
        
        for broker_name in brokers.keys():
            print(f"   • {broker_name}")
        
        return True
        
    except Exception as e:
        print(f"❌ Broker registry test failed: {e}")
        return False

async def test_portfolio_manager():
    """Test portfolio manager"""
    print("\n💼 Testing Portfolio Manager...")
    
    try:
        from core.portfolio.universal_portfolio_manager import UniversalPortfolioManager
        from core.config.config_manager import ConfigManager
        
        config_manager = ConfigManager("config", "development")
        portfolio_manager = UniversalPortfolioManager(config_manager)
        
        await portfolio_manager.initialize()
        print("✅ Portfolio manager initialized successfully")
        
        # Test basic functionality
        summary = portfolio_manager.get_portfolio_summary()
        print(f"   Portfolio summary: {summary}")
        
        return True
        
    except Exception as e:
        print(f"❌ Portfolio manager test failed: {e}")
        return False

async def main():
    """Run all integration tests"""
    print("🚀 Starting Noryon System Integration Tests\n")
    
    tests = [
        ("Configuration Manager", test_config_manager),
        ("Broker Registry", test_broker_registry),
        ("Portfolio Manager", test_portfolio_manager),
        ("Data Manager", test_data_manager),
        ("LLM Integration", test_llm_integration),
    ]
    
    results = {}
    
    for test_name, test_func in tests:
        try:
            result = await test_func()
            results[test_name] = result
        except Exception as e:
            print(f"❌ {test_name} test crashed: {e}")
            results[test_name] = False
    
    # Summary
    print("\n" + "="*60)
    print("📋 TEST SUMMARY")
    print("="*60)
    
    passed = 0
    total = len(results)
    
    for test_name, result in results.items():
        status = "✅ PASS" if result else "❌ FAIL"
        print(f"{status} {test_name}")
        if result:
            passed += 1
    
    print(f"\nResults: {passed}/{total} tests passed")
    
    if passed == total:
        print("🎉 All tests passed! System is ready for next phase.")
        return True
    else:
        print("⚠️ Some tests failed. Check the issues above.")
        return False

if __name__ == "__main__":
    success = asyncio.run(main())
    sys.exit(0 if success else 1)
