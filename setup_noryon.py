#!/usr/bin/env python3
"""
Noryon AI Trading System Setup Script

This script initializes the Noryon system with:
- Configuration management
- LLM provider setup
- Data management initialization
- Testing framework
- Directory structure creation

Usage:
    python setup_noryon.py [--config-only] [--test-mode]
"""

import os
import sys
import argparse
import asyncio
from pathlib import Path
from typing import Dict, Any, List
import logging
import json
from datetime import datetime

# Add project root to path
project_root = Path(__file__).parent
sys.path.insert(0, str(project_root))

# Import Noryon components
from core.config.config_manager import Config<PERSON><PERSON><PERSON>, LLMProviderConfig, LLMProviderType
from core.llm.llm_abstraction_layer import LLMAbstractionLayer
from core.data.data_manager import DataManager
from tests.test_llm_integration import TestRunner

# Setup logging
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s',
    handlers=[
        logging.StreamHandler(),
        logging.FileHandler('noryon_setup.log')
    ]
)
logger = logging.getLogger(__name__)

class NoryonSetup:
    """Noryon System Setup Manager"""
    
    def __init__(self, project_root: Path):
        self.project_root = project_root
        self.config_manager = None
        self.llm_layer = None
        self.data_manager = None
        
    async def setup_system(self, config_only: bool = False, test_mode: bool = False) -> bool:
        """Setup the complete Noryon system"""
        try:
            logger.info("🚀 Starting Noryon AI Trading System Setup")
            
            # Step 1: Create directory structure
            self._create_directory_structure()
            
            # Step 2: Initialize configuration
            await self._setup_configuration()
            
            # Step 3: Setup LLM providers (if not config-only)
            if not config_only:
                await self._setup_llm_providers()
                
                # Step 4: Initialize data management
                await self._setup_data_management()
                
                # Step 5: Run tests (if test-mode)
                if test_mode:
                    await self._run_system_tests()
            
            logger.info("✅ Noryon system setup completed successfully!")
            self._print_setup_summary()
            return True
            
        except Exception as e:
            logger.error(f"❌ Setup failed: {e}")
            return False
    
    def _create_directory_structure(self):
        """Create necessary directory structure"""
        logger.info("📁 Creating directory structure...")
        
        directories = [
            'config',
            'config/credentials',
            'config/ai',
            'config/trading',
            'config/brokers',
            'data',
            'data/cache',
            'data/historical',
            'data/real_time',
            'logs',
            'models',
            'models/local',
            'models/checkpoints',
            'backups',
            'temp'
        ]
        
        for directory in directories:
            dir_path = self.project_root / directory
            dir_path.mkdir(parents=True, exist_ok=True)
            logger.debug(f"Created directory: {dir_path}")
    
    async def _setup_configuration(self):
        """Initialize configuration management"""
        logger.info("⚙️ Setting up configuration management...")
        
        try:
            # Initialize config manager
            self.config_manager = ConfigManager(
                config_dir=self.project_root / 'config',
                environment='development'
            )
            
            # Configuration is automatically loaded during initialization
            
            # Setup default LLM providers
            self._setup_default_llm_providers()
            
            logger.info("✅ Configuration management initialized")
            
        except Exception as e:
            logger.error(f"Failed to setup configuration: {e}")
            raise
    
    def _setup_default_llm_providers(self):
        """Setup default LLM provider configurations"""
        logger.info("🤖 Setting up default LLM providers...")
        
        # Default providers with placeholder configurations
        default_providers = {
            'openai': LLMProviderConfig(
                provider_type=LLMProviderType.OPENAI,
                api_key=None,  # To be set by user
                model='gpt-4',
                max_tokens=4000,
                temperature=0.1,
                timeout=30,
                retry_attempts=3,
                rate_limit={"requests_per_minute": 60},
                cost_per_1k_tokens={"input": 0.03, "output": 0.06},
                enabled=False  # Disabled until API key is provided
            ),
            'anthropic': LLMProviderConfig(
                provider_type=LLMProviderType.ANTHROPIC,
                api_key=None,
                model='claude-3-sonnet-20240229',
                max_tokens=4000,
                temperature=0.1,
                timeout=30,
                retry_attempts=3,
                rate_limit={"requests_per_minute": 60},
                cost_per_1k_tokens={"input": 0.015, "output": 0.075},
                enabled=False
            ),
            'deepseek': LLMProviderConfig(
                provider_type=LLMProviderType.DEEPSEEK,
                api_key=None,
                model='deepseek-chat',
                max_tokens=4000,
                temperature=0.1,
                timeout=30,
                retry_attempts=3,
                rate_limit={"requests_per_minute": 60},
                cost_per_1k_tokens={"input": 0.002, "output": 0.002},
                enabled=False
            ),
            'qwen_local': LLMProviderConfig(
                provider_type=LLMProviderType.QWEN_LOCAL,
                model_path='models/local/qwen',
                inference_url='http://localhost:8000',
                max_tokens=4000,
                temperature=0.1,
                timeout=60,
                enabled=True  # Can be enabled without API key
            )
        }
        
        # Add providers to configuration
        for name, config in default_providers.items():
            success = self.config_manager.add_llm_provider(name, config)
            if success:
                logger.info(f"✅ Added LLM provider: {name}")
            else:
                logger.warning(f"⚠️ Failed to add LLM provider: {name}")
        
        # Set fallback order
        fallback_order = ['qwen_local', 'deepseek', 'openai', 'anthropic']
        self.config_manager.set_llm_fallback_order(fallback_order)
    
    async def _setup_llm_providers(self):
        """Initialize LLM abstraction layer"""
        logger.info("🧠 Setting up LLM abstraction layer...")
        
        try:
            # Initialize LLM layer with config
            self.llm_layer = LLMAbstractionLayer(
                config_manager=self.config_manager
            )
            
            # Initialize providers
            await self.llm_layer.initialize()
            
            # Test basic functionality
            test_response = await self.llm_layer.generate(
                prompt="Hello, this is a test message for Noryon setup.",
                max_tokens=50
            )
            
            if test_response.success:
                logger.info("✅ LLM layer initialized and tested successfully")
            else:
                logger.warning(f"⚠️ LLM test failed: {test_response.error}")
            
        except Exception as e:
            logger.error(f"Failed to setup LLM providers: {e}")
            # Don't raise - system can work without LLM initially
    
    async def _setup_data_management(self):
        """Initialize data management system"""
        logger.info("📊 Setting up data management...")
        
        try:
            # Initialize data manager
            self.data_manager = DataManager(
                config_manager=self.config_manager,
                cache_dir=self.project_root / 'data' / 'cache',
                storage_dir=self.project_root / 'data'
            )
            
            # Initialize data sources
            await self.data_manager.initialize()
            
            # Test data fetching
            test_data = await self.data_manager.get_market_data(
                symbol='AAPL',
                timeframe='1d',
                limit=5
            )
            
            if test_data:
                logger.info("✅ Data management initialized and tested successfully")
            else:
                logger.warning("⚠️ Data management test returned no data")
            
        except Exception as e:
            logger.error(f"Failed to setup data management: {e}")
            # Don't raise - system can work without data initially
    
    async def _run_system_tests(self):
        """Run comprehensive system tests"""
        logger.info("🧪 Running system tests...")
        
        try:
            test_runner = TestRunner()
            
            # Run all test suites
            results = await test_runner.run_all_tests()
            
            # Log results
            for suite_name, result in results.items():
                if result['success']:
                    logger.info(f"✅ {suite_name}: PASSED")
                else:
                    logger.error(f"❌ {suite_name}: FAILED - {result.get('error', 'Unknown error')}")
            
            # Overall test summary
            passed = sum(1 for r in results.values() if r['success'])
            total = len(results)
            
            logger.info(f"📊 Test Summary: {passed}/{total} test suites passed")
            
        except Exception as e:
            logger.error(f"Failed to run system tests: {e}")
    
    def _print_setup_summary(self):
        """Print setup completion summary"""
        print("\n" + "="*60)
        print("🎉 NORYON AI TRADING SYSTEM SETUP COMPLETE")
        print("="*60)
        
        if self.config_manager:
            summary = self.config_manager.get_config_summary()
            
            print(f"\n📋 Configuration Summary:")
            print(f"   Environment: {summary['environment']}")
            print(f"   LLM Providers: {summary['ai_config']['llm_providers_total']} total, {summary['ai_config']['llm_providers_enabled']} enabled")
            print(f"   Fallback Order: {', '.join(summary['ai_config']['llm_fallback_order'])}")
            print(f"   Daily Cost Limit: ${summary['ai_config']['daily_cost_limit']}")
        
        print(f"\n📁 Project Structure:")
        print(f"   Root: {self.project_root}")
        print(f"   Config: {self.project_root / 'config'}")
        print(f"   Data: {self.project_root / 'data'}")
        print(f"   Models: {self.project_root / 'models'}")
        print(f"   Logs: {self.project_root / 'logs'}")
        
        print(f"\n🚀 Next Steps:")
        print(f"   1. Add your LLM API keys using: python -m core.config.config_manager add_api_key <provider> <key>")
        print(f"   2. Configure your trading parameters in: config/trading/")
        print(f"   3. Set up broker connections in: config/brokers/")
        print(f"   4. Run tests: python -m tests.test_llm_integration")
        print(f"   5. Start the system: python main.py")
        
        print("\n" + "="*60)

def main():
    """Main setup function"""
    parser = argparse.ArgumentParser(description='Setup Noryon AI Trading System')
    parser.add_argument('--config-only', action='store_true', 
                       help='Only setup configuration, skip LLM and data initialization')
    parser.add_argument('--test-mode', action='store_true',
                       help='Run comprehensive tests after setup')
    
    args = parser.parse_args()
    
    # Initialize setup
    setup = NoryonSetup(project_root)
    
    # Run async setup
    try:
        success = asyncio.run(setup.setup_system(
            config_only=args.config_only,
            test_mode=args.test_mode
        ))
        
        if success:
            print("\n🎉 Setup completed successfully!")
            sys.exit(0)
        else:
            print("\n❌ Setup failed. Check logs for details.")
            sys.exit(1)
            
    except KeyboardInterrupt:
        print("\n⚠️ Setup interrupted by user")
        sys.exit(1)
    except Exception as e:
        print(f"\n💥 Unexpected error: {e}")
        sys.exit(1)

if __name__ == '__main__':
    main()