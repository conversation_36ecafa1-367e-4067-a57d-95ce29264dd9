# Import dependencies
import numpy as np
import pandas as pd
import matplotlib.pyplot as plt
import yfinance as yf
import datetime as dt
yf.pdr_override()

# input
symbol = "AAPL"
market = "^GSPC"
start = dt.date.today() - dt.<PERSON><PERSON>ta(days=365 * 4)
end = dt.date.today()

# Read data
df = yf.download(symbol, start, end)
mk = yf.download(market, start, end)

df["Returns"] = df["Adj Close"].pct_change().dropna()
mk["Returns"] = mk["Adj Close"].pct_change().dropna()

n = 5
covar = df["Returns"].rolling(n).cov(mk["Returns"])
variance = mk["Returns"].rolling(n).var()
df["Beta"] = covar / variance

fig = plt.figure(figsize=(14, 7))
ax1 = plt.subplot(2, 1, 1)
ax1.plot(df["Adj Close"])
ax1.set_title("Stock " + symbol + " Closing Price")
ax1.set_ylabel("Price")

ax2 = plt.subplot(2, 1, 2)
ax2.plot(df["Beta"], label="Beta", color="red")
# ax2.axhline(y=0, color='blue', linestyle='--')
ax2.grid()
ax2.set_ylabel("Beta")
ax2.set_xlabel("Date")
ax2.legend(loc="best")
plt.show()

# ## Candlestick with Beta
from matplotlib import dates as mdates

dfc = df.copy()
dfc["VolumePositive"] = dfc["Open"] < dfc["Adj Close"]
# dfc = dfc.dropna()
dfc = dfc.reset_index()
dfc["Date"] = pd.to_datetime(dfc["Date"])
dfc["Date"] = dfc["Date"].apply(mdates.date2num)
from mplfinance.original_flavor import candlestick_ohlc

fig = plt.figure(figsize=(14, 7))
ax1 = plt.subplot(2, 1, 1)
candlestick_ohlc(ax1, dfc.values, width=0.5, colorup="g", colordown="r", alpha=1.0)
ax1.xaxis_date()
ax1.xaxis.set_major_formatter(mdates.DateFormatter("%d-%m-%Y"))
ax1.grid(True, which="both")
ax1.minorticks_on()
ax1v = ax1.twinx()
colors = dfc.VolumePositive.map({True: "g", False: "r"})
ax1v.bar(dfc.Date, dfc["Volume"], color=colors, alpha=0.4)
ax1v.axes.yaxis.set_ticklabels([])
ax1v.set_ylim(0, 3 * df.Volume.max())
ax1.set_title("Stock " + symbol + " Closing Price")
ax1.set_ylabel("Price")

ax2 = plt.subplot(2, 1, 2)
ax2.plot(df["Beta"], label="Beta", color="red")
# ax2.axhline(y=0, color='blue', linestyle='--')
ax2.grid()
ax2.set_ylabel("Beta")
ax2.set_xlabel("Date")
ax2.legend(loc="best")
plt.show()
