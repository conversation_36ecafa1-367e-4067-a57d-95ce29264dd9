# Import dependencies
import numpy as np
import pandas as pd
import matplotlib.pyplot as plt
import yfinance as yf
import datetime as dt
yf.pdr_override()
import sys
import os
parent_dir = os.path.dirname(os.getcwd())
sys.path.append(parent_dir)
import ta_functions as ta

# input
symbol = "META"
start = dt.date.today() - dt.timedelta(days=365 * 3)
end = dt.date.today()

# Read data
df = yf.download(symbol, start, end)

df["macd"], df["macdsignal"], df["macdhist"] = ta.MACD(
    df["Adj Close"], fastperiod=12, slowperiod=26, signalperiod=9
)
df = df.dropna()

# Line Chart
fig = plt.figure(figsize=(14, 7))
ax1 = plt.subplot(2, 1, 1)
ax1.plot(df.index, df["Adj Close"])
ax1.axhline(y=df["Adj Close"].mean(), color="r")
ax1.grid()
# ax1.grid(True, which='both')
# ax1.grid(which='minor', linestyle='-', linewidth='0.5', color='black')
# ax1.grid(which='major', linestyle='-', linewidth='0.5', color='red')
# ax1.minorticks_on()
# ax1.legend(loc='best')
ax1v = ax1.twinx()
ax1v.fill_between(df.index[0:], 0, df.Volume[0:], facecolor="#0079a3", alpha=0.4)
ax1v.axes.yaxis.set_ticklabels([])
ax1v.set_ylim(0, 3 * df.Volume.max())
ax1.set_title("Stock " + symbol + " Closing Price")
ax1.set_ylabel("Price")

labels = ["macd", "macdsignal"]
ax2 = plt.subplot(2, 1, 2)
ax2.plot(df[["macd", "macdsignal"]], label=labels)
ax2.bar(df.index, df["macdhist"], label="macdhist")
ax2.grid()
ax2.set_ylabel("MACD")
ax2.set_xlabel("Date")
ax2.legend(loc="best")
plt.show()

# Line Chart
fig = plt.figure(figsize=(14, 7))
ax1 = plt.subplot(2, 1, 1)
ax1.plot(df.index, df["Adj Close"])
ax1.axhline(y=df["Adj Close"].mean(), color="r")
ax1.grid()
# ax1.grid(True, which='both')
# ax1.grid(which='minor', linestyle='-', linewidth='0.5', color='black')
# ax1.grid(which='major', linestyle='-', linewidth='0.5', color='red')
# ax1.minorticks_on()
# ax1.legend(loc='best')
ax1v = ax1.twinx()
ax1v.fill_between(df.index[0:], 0, df.Volume[0:], facecolor="#0079a3", alpha=0.4)
ax1v.axes.yaxis.set_ticklabels([])
ax1v.set_ylim(0, 3 * df.Volume.max())
ax1.set_title("Stock " + symbol + " Closing Price")
ax1.set_ylabel("Price")

labels = ["macd", "macdsignal"]
ax2 = plt.subplot(2, 1, 2)
df["positive"] = df["macdhist"] > 0
ax2.plot(df[["macd", "macdsignal"]], label=labels)
ax2.bar(
    df.index,
    df["macdhist"],
    color=df.positive.map({True: "g", False: "r"}),
    label="macdhist",
)
ax2.grid()
ax2.set_ylabel("MACD")
ax2.set_xlabel("Date")
ax2.legend(loc="best")
plt.show()

# ## Candlestick with MACD
from matplotlib import dates as mdates

dfc = df.copy()
dfc["macd"], dfc["macdsignal"], dfc["macdhist"] = ta.MACD(
    dfc["Adj Close"], fastperiod=12, slowperiod=26, signalperiod=9
)
dfc["VolumePositive"] = dfc["Open"] < dfc["Adj Close"]
dfc = dfc.dropna()
dfc = dfc.reset_index()
dfc["Date"] = mdates.date2num(dfc["Date"].tolist())
from mplfinance.original_flavor import candlestick_ohlc

fig = plt.figure(figsize=(14, 7))
ax1 = plt.subplot(2, 1, 1)
candlestick_ohlc(ax1, dfc.values, width=0.5, colorup="g", colordown="r", alpha=1.0)
ax1.xaxis_date()
ax1.xaxis.set_major_formatter(mdates.DateFormatter("%d-%m-%Y"))
ax1.grid(True, which="both")
ax1.minorticks_on()
ax1v = ax1.twinx()
ax1v.fill_between(dfc.Date, 0, dfc.Volume[0:], facecolor="#0079a3", alpha=0.4)
ax1v.axes.yaxis.set_ticklabels([])
ax1v.set_ylim(0, 3 * df.Volume.max())
ax1.set_title("Stock " + symbol + " Closing Price")
ax1.set_ylabel("Price")

labels = ["macd", "macdsignal"]
ax2 = plt.subplot(2, 1, 2)
ax2.plot(df[["macd", "macdsignal"]], label=labels)
ax2.bar(df.index, df["macdhist"], label="macdhist")
ax2.grid()
ax2.set_xlabel("Date")
ax2.legend(loc="best")
plt.show()

fig = plt.figure(figsize=(14, 7))
ax1 = plt.subplot(2, 1, 1)
candlestick_ohlc(ax1, dfc.values, width=0.5, colorup="g", colordown="r", alpha=1.0)
ax1.xaxis_date()
ax1.xaxis.set_major_formatter(mdates.DateFormatter("%d-%m-%Y"))
ax1.grid(True, which="both")
ax1.minorticks_on()
ax1v = ax1.twinx()
colors = dfc.VolumePositive.map({True: "g", False: "r"})
ax1v.bar(dfc.Date, dfc["Volume"], color=colors, alpha=0.4)
ax1v.axes.yaxis.set_ticklabels([])
ax1v.set_ylim(0, 3 * df.Volume.max())
ax1.set_title("Stock " + symbol + " Closing Price")
ax1.set_ylabel("Price")

labels = ["macd", "macdsignal"]
ax2 = plt.subplot(2, 1, 2)
df["positive"] = df["macdhist"] > 0
ax2.plot(df[["macd", "macdsignal"]], label=labels)
ax2.bar(
    df.index,
    df["macdhist"],
    color=df.positive.map({True: "g", False: "r"}),
    label="macdhist",
)
ax2.grid()
ax2.set_ylabel("MACD")
ax2.set_xlabel("Date")
ax2.legend(loc="best")
plt.show()

fig = plt.figure(figsize=(14, 7))
ax1 = plt.subplot(3, 1, 1)
candlestick_ohlc(ax1, dfc.values, width=0.5, colorup="g", colordown="r", alpha=1.0)
ax1.xaxis_date()
ax1.xaxis.set_major_formatter(mdates.DateFormatter("%d-%m-%Y"))
ax1.grid(True, which="both")
ax1.minorticks_on()
ax1v = ax1.twinx()
colors = dfc.VolumePositive.map({True: "g", False: "r"})
ax1v.bar(dfc.Date, dfc["Volume"], color=colors, alpha=0.4)
ax1v.axes.yaxis.set_ticklabels([])
ax1v.set_ylim(0, 3 * df.Volume.max())
ax1.set_title("Stock " + symbol + " Closing Price")
ax1.set_ylabel("Price")

labels = ["macd", "macdsignal"]
ax2 = plt.subplot(3, 1, 2)
df["positive"] = df["macdhist"] > 0
ax2.plot(df[["macd", "macdsignal"]], label=labels)
ax2.bar(
    df.index,
    df["macdhist"],
    color=df.positive.map({True: "g", False: "r"}),
    label="macdhist",
)
ax2.grid()
ax2.set_ylabel("MACD")
ax2.set_xlabel("Date")
ax2.legend(loc="best")

ax3 = plt.subplot(3, 1, 3)
ax3.bar(dfc.Date, dfc["Volume"], color=dfc.VolumePositive.map({True: "g", False: "r"}))
ax3.grid()
ax3.set_ylabel("Volume")
ax3.set_xlabel("Date")
plt.show()