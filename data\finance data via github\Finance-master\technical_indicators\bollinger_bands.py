# Import dependencies
import numpy as np
import pandas as pd
import matplotlib.pyplot as plt
import yfinance as yf
import datetime as dt
yf.pdr_override()

# input
symbol = "RIG"
start = dt.date.today() - dt.timedelta(days=365 * 4)
end = dt.date.today()

# Read data
df = yf.download(symbol, start, end)

n = 20
MA = pd.Series(df["Adj Close"].rolling(n).mean())
STD = pd.Series(df["Adj Close"].rolling(n).std())
bb1 = MA + 2 * STD
df["Upper Bollinger Band"] = pd.Series(bb1)
bb2 = MA - 2 * STD
df["Lower Bollinger Band"] = pd.Series(bb2)

plt.figure()
df[["Adj Close", "Upper Bollinger Band", "Lower Bollinger Band"]].plot(figsize=(14, 7))
plt.ylabel("Price")
plt.xlabel("Date")
plt.title(f"{symbol} Bollinger Bands")
plt.legend(loc="best")
plt.show()

# ## Candlestick with Bollinger Bands
from matplotlib import dates as mdates

dfc = df.copy()
dfc["VolumePositive"] = dfc["Open"] < dfc["Adj Close"]
# dfc = dfc.dropna()
dfc = dfc.reset_index()
dfc["Date"] = pd.to_datetime(dfc["Date"])
dfc["Date"] = dfc["Date"].apply(mdates.date2num)
from mplfinance.original_flavor import candlestick_ohlc

fig = plt.figure(figsize=(14, 7))
ax1 = plt.subplot(2, 1, 1)
candlestick_ohlc(ax1, dfc.values, width=0.5, colorup="g", colordown="r", alpha=1.0)
ax1.plot(df["Upper Bollinger Band"], label="Upper Bollinger Band")
ax1.plot(df["Lower Bollinger Band"], label="Lower Bollinger Band")
ax1.xaxis_date()
ax1.xaxis.set_major_formatter(mdates.DateFormatter("%d-%m-%Y"))
ax1.grid(True, which="both")
ax1.minorticks_on()
ax1v = ax1.twinx()
colors = dfc.VolumePositive.map({True: "g", False: "r"})
ax1v.bar(dfc.Date, dfc["Volume"], color=colors, alpha=0.4)
ax1v.axes.yaxis.set_ticklabels([])
ax1v.set_ylim(0, 3 * df.Volume.max())
ax1.set_title("Stock " + symbol + " Closing Price")
ax1.legend(loc="best")
ax1.set_ylabel("Price")
ax1.set_xlabel("Date")

fig = plt.figure(figsize=(14, 7))
ax1 = plt.subplot(2, 1, 1)
candlestick_ohlc(ax1, dfc.values, width=0.5, colorup="g", colordown="r", alpha=1.0)
ax1.plot(df["Upper Bollinger Band"], label="Upper Bollinger Band")
ax1.plot(df["Lower Bollinger Band"], label="Lower Bollinger Band")
ax1.xaxis_date()
ax1.xaxis.set_major_formatter(mdates.DateFormatter("%d-%m-%Y"))
ax1.grid(True, which="both")
ax1.minorticks_on()
ax1v = ax1.twinx()
colors = dfc.VolumePositive.map({True: "g", False: "r"})
ax1v.bar(dfc.Date, dfc["Volume"], color=colors, alpha=0.4)
ax1v.axes.yaxis.set_ticklabels([])
ax1v.set_ylim(0, 3 * df.Volume.max())
ax1.set_title("Stock " + symbol + " Closing Price")
ax1.legend(loc="best")
ax1.set_ylabel("Price")
ax1.set_xlabel("Date")

ax2 = plt.subplot(2, 1, 2)
ax2.bar(dfc.index, dfc["Volume"], color=dfc.VolumePositive.map({True: "g", False: "r"}))
ax2.grid()
ax2.set_ylabel("Volume")
ax2.set_xlabel("Date")
plt.show()
