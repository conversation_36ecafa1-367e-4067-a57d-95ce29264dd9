#!/usr/bin/env python3
"""
Noryon Data Management System

Comprehensive data management for AI trading system supporting:
- Market data ingestion and processing
- News and sentiment data
- Economic indicators
- Alternative data sources
- Data preprocessing for LLM training
- Real-time data streaming
- Data quality monitoring
- Feature engineering
"""

import asyncio
import json
import logging
import os
from abc import ABC, abstractmethod
from dataclasses import dataclass, field
from datetime import datetime, timedelta
from enum import Enum
from pathlib import Path
from typing import Dict, List, Optional, Any, Union, AsyncGenerator, Tuple, Callable

# Optional imports with graceful fallbacks
try:
    import aiofiles
    AIOFILES_AVAILABLE = True
except ImportError:
    aiofiles = None
    AIOFILES_AVAILABLE = False

try:
    import aiohttp
    AIOHTTP_AVAILABLE = True
except ImportError:
    aiohttp = None
    AIOHTTP_AVAILABLE = False

try:
    import numpy as np
    NUMPY_AVAILABLE = True
except ImportError:
    np = None
    NUMPY_AVAILABLE = False

try:
    import pandas as pd
    PANDAS_AVAILABLE = True
except ImportError:
    pd = None
    PANDAS_AVAILABLE = False

try:
    from sqlalchemy import create_engine, text
    from sqlalchemy.ext.asyncio import create_async_engine, AsyncSession
    SQLALCHEMY_AVAILABLE = True
except ImportError:
    create_engine = create_async_engine = AsyncSession = text = None
    SQLALCHEMY_AVAILABLE = False

try:
    import redis
    REDIS_AVAILABLE = True
except ImportError:
    redis = None
    REDIS_AVAILABLE = False

try:
    import yfinance as yf
    YFINANCE_AVAILABLE = True
except ImportError:
    yf = None
    YFINANCE_AVAILABLE = False

try:
    import ccxt
    CCXT_AVAILABLE = True
except ImportError:
    ccxt = None
    CCXT_AVAILABLE = False

try:
    from prometheus_client import Counter, Histogram, Gauge
    PROMETHEUS_AVAILABLE = True
except ImportError:
    Counter = Histogram = Gauge = None
    PROMETHEUS_AVAILABLE = False

# Configure logging
logger = logging.getLogger(__name__)

# Metrics (conditional on prometheus availability)
if PROMETHEUS_AVAILABLE:
    data_ingestion_total = Counter('data_ingestion_total', 'Total data points ingested', ['source', 'type'])
    data_processing_time = Histogram('data_processing_time_seconds', 'Data processing time', ['source', 'operation'])
    data_quality_score = Gauge('data_quality_score', 'Data quality score', ['source', 'metric'])
    data_storage_size = Gauge('data_storage_size_bytes', 'Data storage size', ['source', 'type'])
else:
    # Mock metrics when prometheus is not available
    class MockMetric:
        def inc(self, *args, **kwargs): pass
        def observe(self, *args, **kwargs): pass
        def set(self, *args, **kwargs): pass
        def labels(self, *args, **kwargs): return self
    
    data_ingestion_total = MockMetric()
    data_processing_time = MockMetric()
    data_quality_score = MockMetric()
    data_storage_size = MockMetric()

class DataSource(Enum):
    """Supported data sources"""
    YAHOO_FINANCE = "yahoo_finance"
    ALPHA_VANTAGE = "alpha_vantage"
    BINANCE = "binance"
    COINBASE = "coinbase"
    NEWS_API = "news_api"
    REDDIT = "reddit"
    TWITTER = "twitter"
    FRED = "fred"  # Federal Reserve Economic Data
    CUSTOM = "custom"
    LOCAL_FILE = "local_file"

class DataType(Enum):
    """Types of data"""
    PRICE_DATA = "price_data"
    VOLUME_DATA = "volume_data"
    NEWS_DATA = "news_data"
    SENTIMENT_DATA = "sentiment_data"
    ECONOMIC_DATA = "economic_data"
    SOCIAL_DATA = "social_data"
    TECHNICAL_INDICATORS = "technical_indicators"
    FUNDAMENTAL_DATA = "fundamental_data"
    ALTERNATIVE_DATA = "alternative_data"

@dataclass
class DataPoint:
    """Standardized data point format"""
    timestamp: datetime
    symbol: str
    data_type: DataType
    source: DataSource
    value: Union[float, str, Dict[str, Any]]
    metadata: Dict[str, Any] = field(default_factory=dict)
    quality_score: float = 1.0
    processed: bool = False

@dataclass
class DataBatch:
    """Batch of data points for efficient processing"""
    data_points: List[DataPoint]
    batch_id: str
    created_at: datetime = field(default_factory=datetime.now)
    processed_at: Optional[datetime] = None
    metadata: Dict[str, Any] = field(default_factory=dict)

class DataSourceInterface(ABC):
    """Abstract interface for data sources"""
    
    def __init__(self, config: Dict[str, Any]):
        self.config = config
        self.source = DataSource(config['source'])
        self.enabled = config.get('enabled', True)
        self.rate_limit = config.get('rate_limit', {})
    
    @abstractmethod
    async def fetch_data(self, symbols: List[str], start_date: datetime, end_date: datetime) -> List[DataPoint]:
        """Fetch data for given symbols and date range"""
        pass
    
    @abstractmethod
    async def fetch_realtime(self, symbols: List[str]) -> AsyncGenerator[DataPoint, None]:
        """Fetch real-time data stream"""
        pass
    
    @abstractmethod
    async def health_check(self) -> bool:
        """Check if data source is available"""
        pass

class YahooFinanceSource(DataSourceInterface):
    """Yahoo Finance data source"""
    
    async def fetch_data(self, symbols: List[str], start_date: datetime, end_date: datetime) -> List[DataPoint]:
        data_points = []
        
        for symbol in symbols:
            try:
                ticker = yf.Ticker(symbol)
                hist = ticker.history(start=start_date, end=end_date)
                
                for date, row in hist.iterrows():
                    # Price data points
                    for price_type in ['Open', 'High', 'Low', 'Close']:
                        data_points.append(DataPoint(
                            timestamp=date.to_pydatetime(),
                            symbol=symbol,
                            data_type=DataType.PRICE_DATA,
                            source=DataSource.YAHOO_FINANCE,
                            value=float(row[price_type]),
                            metadata={'price_type': price_type.lower()}
                        ))
                    
                    # Volume data
                    data_points.append(DataPoint(
                        timestamp=date.to_pydatetime(),
                        symbol=symbol,
                        data_type=DataType.VOLUME_DATA,
                        source=DataSource.YAHOO_FINANCE,
                        value=float(row['Volume']),
                        metadata={}
                    ))
                
                data_ingestion_total.labels(source='yahoo_finance', type='price_data').inc(len(hist) * 5)
                
            except Exception as e:
                logger.error(f"Error fetching Yahoo Finance data for {symbol}: {e}")
        
        return data_points
    
    async def fetch_realtime(self, symbols: List[str]) -> AsyncGenerator[DataPoint, None]:
        # Yahoo Finance doesn't provide real-time streaming, so we'll poll
        while True:
            for symbol in symbols:
                try:
                    ticker = yf.Ticker(symbol)
                    info = ticker.info
                    current_price = info.get('currentPrice') or info.get('regularMarketPrice')
                    
                    if current_price:
                        yield DataPoint(
                            timestamp=datetime.now(),
                            symbol=symbol,
                            data_type=DataType.PRICE_DATA,
                            source=DataSource.YAHOO_FINANCE,
                            value=float(current_price),
                            metadata={'price_type': 'current'}
                        )
                except Exception as e:
                    logger.error(f"Error fetching real-time data for {symbol}: {e}")
            
            await asyncio.sleep(60)  # Poll every minute
    
    async def health_check(self) -> bool:
        try:
            ticker = yf.Ticker("AAPL")
            info = ticker.info
            return 'currentPrice' in info or 'regularMarketPrice' in info
        except:
            return False

class BinanceSource(DataSourceInterface):
    """Binance cryptocurrency data source"""
    
    def __init__(self, config: Dict[str, Any]):
        super().__init__(config)
        self.exchange = ccxt.binance({
            'apiKey': config.get('api_key'),
            'secret': config.get('api_secret'),
            'sandbox': config.get('sandbox', False),
            'enableRateLimit': True,
        })
    
    async def fetch_data(self, symbols: List[str], start_date: datetime, end_date: datetime) -> List[DataPoint]:
        data_points = []
        
        for symbol in symbols:
            try:
                # Convert to Binance format (e.g., BTC/USDT -> BTCUSDT)
                binance_symbol = symbol.replace('/', '')
                
                # Fetch OHLCV data
                since = int(start_date.timestamp() * 1000)
                until = int(end_date.timestamp() * 1000)
                
                ohlcv = await asyncio.get_event_loop().run_in_executor(
                    None, 
                    lambda: self.exchange.fetch_ohlcv(binance_symbol, '1d', since, limit=1000)
                )
                
                for candle in ohlcv:
                    timestamp = datetime.fromtimestamp(candle[0] / 1000)
                    
                    # Price data
                    price_types = ['open', 'high', 'low', 'close']
                    for i, price_type in enumerate(price_types):
                        data_points.append(DataPoint(
                            timestamp=timestamp,
                            symbol=symbol,
                            data_type=DataType.PRICE_DATA,
                            source=DataSource.BINANCE,
                            value=float(candle[i + 1]),
                            metadata={'price_type': price_type}
                        ))
                    
                    # Volume data
                    data_points.append(DataPoint(
                        timestamp=timestamp,
                        symbol=symbol,
                        data_type=DataType.VOLUME_DATA,
                        source=DataSource.BINANCE,
                        value=float(candle[5]),
                        metadata={}
                    ))
                
                data_ingestion_total.labels(source='binance', type='price_data').inc(len(ohlcv) * 5)
                
            except Exception as e:
                logger.error(f"Error fetching Binance data for {symbol}: {e}")
        
        return data_points
    
    async def fetch_realtime(self, symbols: List[str]) -> AsyncGenerator[DataPoint, None]:
        # Implementation would use Binance WebSocket API
        # For now, we'll use polling
        while True:
            for symbol in symbols:
                try:
                    binance_symbol = symbol.replace('/', '')
                    ticker = await asyncio.get_event_loop().run_in_executor(
                        None,
                        lambda: self.exchange.fetch_ticker(binance_symbol)
                    )
                    
                    yield DataPoint(
                        timestamp=datetime.now(),
                        symbol=symbol,
                        data_type=DataType.PRICE_DATA,
                        source=DataSource.BINANCE,
                        value=float(ticker['last']),
                        metadata={'price_type': 'last'}
                    )
                    
                except Exception as e:
                    logger.error(f"Error fetching real-time Binance data for {symbol}: {e}")
            
            await asyncio.sleep(10)  # Poll every 10 seconds
    
    async def health_check(self) -> bool:
        try:
            await asyncio.get_event_loop().run_in_executor(
                None,
                lambda: self.exchange.fetch_status()
            )
            return True
        except:
            return False

class NewsAPISource(DataSourceInterface):
    """News API data source for sentiment analysis"""
    
    def __init__(self, config: Dict[str, Any]):
        super().__init__(config)
        self.api_key = config['api_key']
        self.base_url = "https://newsapi.org/v2"
        self.session = None
    
    async def _get_session(self):
        if not self.session:
            self.session = aiohttp.ClientSession(
                headers={'X-API-Key': self.api_key}
            )
        return self.session
    
    async def fetch_data(self, symbols: List[str], start_date: datetime, end_date: datetime) -> List[DataPoint]:
        data_points = []
        session = await self._get_session()
        
        for symbol in symbols:
            try:
                # Search for news related to the symbol
                params = {
                    'q': f'{symbol} stock OR {symbol} trading',
                    'from': start_date.strftime('%Y-%m-%d'),
                    'to': end_date.strftime('%Y-%m-%d'),
                    'sortBy': 'publishedAt',
                    'language': 'en'
                }
                
                async with session.get(f"{self.base_url}/everything", params=params) as resp:
                    if resp.status == 200:
                        data = await resp.json()
                        
                        for article in data.get('articles', []):
                            published_at = datetime.fromisoformat(
                                article['publishedAt'].replace('Z', '+00:00')
                            )
                            
                            data_points.append(DataPoint(
                                timestamp=published_at,
                                symbol=symbol,
                                data_type=DataType.NEWS_DATA,
                                source=DataSource.NEWS_API,
                                value={
                                    'title': article['title'],
                                    'description': article['description'],
                                    'content': article['content'],
                                    'url': article['url'],
                                    'source': article['source']['name']
                                },
                                metadata={'language': 'en'}
                            ))
                        
                        data_ingestion_total.labels(source='news_api', type='news_data').inc(len(data.get('articles', [])))
                    
            except Exception as e:
                logger.error(f"Error fetching news data for {symbol}: {e}")
        
        return data_points
    
    async def fetch_realtime(self, symbols: List[str]) -> AsyncGenerator[DataPoint, None]:
        # Poll for latest news every 15 minutes
        while True:
            end_time = datetime.now()
            start_time = end_time - timedelta(minutes=15)
            
            data_points = await self.fetch_data(symbols, start_time, end_time)
            for data_point in data_points:
                yield data_point
            
            await asyncio.sleep(900)  # 15 minutes
    
    async def health_check(self) -> bool:
        try:
            session = await self._get_session()
            async with session.get(f"{self.base_url}/top-headlines", params={'country': 'us', 'pageSize': 1}) as resp:
                return resp.status == 200
        except:
            return False

class DataProcessor:
    """Data processing and feature engineering"""
    
    def __init__(self, config: Dict[str, Any]):
        self.config = config
        self.feature_extractors = self._initialize_feature_extractors()
    
    def _initialize_feature_extractors(self) -> Dict[str, Callable]:
        """Initialize feature extraction functions"""
        return {
            'technical_indicators': self._extract_technical_indicators,
            'sentiment_scores': self._extract_sentiment_scores,
            'price_features': self._extract_price_features,
            'volume_features': self._extract_volume_features
        }
    
    async def process_batch(self, batch: DataBatch) -> DataBatch:
        """Process a batch of data points"""
        start_time = datetime.now()
        
        processed_points = []
        
        for data_point in batch.data_points:
            try:
                processed_point = await self._process_data_point(data_point)
                processed_points.append(processed_point)
            except Exception as e:
                logger.error(f"Error processing data point: {e}")
                # Keep original data point if processing fails
                processed_points.append(data_point)
        
        processing_time = (datetime.now() - start_time).total_seconds()
        data_processing_time.labels(source='batch', operation='process').observe(processing_time)
        
        return DataBatch(
            data_points=processed_points,
            batch_id=batch.batch_id,
            created_at=batch.created_at,
            processed_at=datetime.now(),
            metadata={**batch.metadata, 'processing_time': processing_time}
        )
    
    async def _process_data_point(self, data_point: DataPoint) -> DataPoint:
        """Process individual data point"""
        # Apply quality checks
        quality_score = self._calculate_quality_score(data_point)
        
        # Extract features based on data type
        if data_point.data_type == DataType.PRICE_DATA:
            processed_value = self._process_price_data(data_point)
        elif data_point.data_type == DataType.NEWS_DATA:
            processed_value = await self._process_news_data(data_point)
        else:
            processed_value = data_point.value
        
        return DataPoint(
            timestamp=data_point.timestamp,
            symbol=data_point.symbol,
            data_type=data_point.data_type,
            source=data_point.source,
            value=processed_value,
            metadata={**data_point.metadata, 'processed': True},
            quality_score=quality_score,
            processed=True
        )
    
    def _calculate_quality_score(self, data_point: DataPoint) -> float:
        """Calculate data quality score"""
        score = 1.0
        
        # Check for missing values
        if data_point.value is None:
            score *= 0.0
        
        # Check timestamp validity
        if data_point.timestamp > datetime.now():
            score *= 0.5
        
        # Check for outliers in price data
        if data_point.data_type == DataType.PRICE_DATA and isinstance(data_point.value, (int, float)):
            if data_point.value <= 0:
                score *= 0.0
            elif data_point.value > 1000000:  # Suspiciously high price
                score *= 0.7
        
        return score
    
    def _process_price_data(self, data_point: DataPoint) -> float:
        """Process price data"""
        value = float(data_point.value)
        
        # Apply any price adjustments or normalizations
        # For now, just return the original value
        return value
    
    async def _process_news_data(self, data_point: DataPoint) -> Dict[str, Any]:
        """Process news data and extract sentiment"""
        news_content = data_point.value
        
        # Extract text for sentiment analysis
        text = f"{news_content.get('title', '')} {news_content.get('description', '')}"
        
        # Simple sentiment scoring (in production, use proper NLP models)
        sentiment_score = self._simple_sentiment_analysis(text)
        
        return {
            **news_content,
            'sentiment_score': sentiment_score,
            'text_length': len(text),
            'processed_at': datetime.now().isoformat()
        }
    
    def _simple_sentiment_analysis(self, text: str) -> float:
        """Simple sentiment analysis (replace with proper NLP model)"""
        positive_words = ['good', 'great', 'excellent', 'positive', 'up', 'gain', 'profit', 'bull']
        negative_words = ['bad', 'terrible', 'negative', 'down', 'loss', 'bear', 'crash', 'decline']
        
        text_lower = text.lower()
        positive_count = sum(1 for word in positive_words if word in text_lower)
        negative_count = sum(1 for word in negative_words if word in text_lower)
        
        if positive_count + negative_count == 0:
            return 0.0
        
        return (positive_count - negative_count) / (positive_count + negative_count)
    
    def _extract_technical_indicators(self, price_data: pd.DataFrame) -> Dict[str, float]:
        """Extract technical indicators from price data"""
        indicators = {}
        
        if len(price_data) >= 20:
            # Simple Moving Average
            indicators['sma_20'] = price_data['close'].rolling(20).mean().iloc[-1]
            
            # Exponential Moving Average
            indicators['ema_20'] = price_data['close'].ewm(span=20).mean().iloc[-1]
            
            # RSI (simplified)
            delta = price_data['close'].diff()
            gain = (delta.where(delta > 0, 0)).rolling(14).mean()
            loss = (-delta.where(delta < 0, 0)).rolling(14).mean()
            rs = gain / loss
            indicators['rsi'] = 100 - (100 / (1 + rs)).iloc[-1]
        
        return indicators
    
    def _extract_sentiment_scores(self, news_data: List[DataPoint]) -> Dict[str, float]:
        """Extract sentiment scores from news data"""
        if not news_data:
            return {'sentiment_avg': 0.0, 'sentiment_count': 0}
        
        sentiment_scores = []
        for data_point in news_data:
            if isinstance(data_point.value, dict) and 'sentiment_score' in data_point.value:
                sentiment_scores.append(data_point.value['sentiment_score'])
        
        if sentiment_scores:
            return {
                'sentiment_avg': np.mean(sentiment_scores),
                'sentiment_std': np.std(sentiment_scores),
                'sentiment_count': len(sentiment_scores)
            }
        
        return {'sentiment_avg': 0.0, 'sentiment_count': 0}
    
    def _extract_price_features(self, price_data: pd.DataFrame) -> Dict[str, float]:
        """Extract price-based features"""
        features = {}
        
        if len(price_data) > 0:
            features['price_volatility'] = price_data['close'].pct_change().std()
            features['price_trend'] = (price_data['close'].iloc[-1] - price_data['close'].iloc[0]) / price_data['close'].iloc[0]
            
            if len(price_data) >= 5:
                features['price_momentum'] = price_data['close'].pct_change(5).iloc[-1]
        
        return features
    
    def _extract_volume_features(self, volume_data: pd.DataFrame) -> Dict[str, float]:
        """Extract volume-based features"""
        features = {}
        
        if len(volume_data) > 0:
            features['volume_avg'] = volume_data['volume'].mean()
            features['volume_trend'] = volume_data['volume'].pct_change().mean()
        
        return features

class DataStorage:
    """Data storage and retrieval system"""
    
    def __init__(self, config: Dict[str, Any]):
        self.config = config
        self.db_engine = create_async_engine(config['database_url'])
        self.redis_client = redis.from_url(config.get('redis_url', 'redis://localhost:6379'))
        self.file_storage_path = Path(config.get('file_storage_path', 'data'))
        self.file_storage_path.mkdir(exist_ok=True)
    
    async def store_batch(self, batch: DataBatch) -> bool:
        """Store data batch to database and files"""
        try:
            # Store to database
            await self._store_to_database(batch)
            
            # Store to files for training data
            await self._store_to_files(batch)
            
            # Cache recent data in Redis
            await self._cache_recent_data(batch)
            
            # Update storage metrics
            data_storage_size.labels(source='database', type='batch').inc(len(batch.data_points))
            
            return True
        
        except Exception as e:
            logger.error(f"Error storing batch {batch.batch_id}: {e}")
            return False
    
    async def _store_to_database(self, batch: DataBatch):
        """Store batch to SQL database"""
        # Implementation would depend on your database schema
        # This is a simplified example
        pass
    
    async def _store_to_files(self, batch: DataBatch):
        """Store batch to files for training data"""
        # Organize by date and data type
        date_str = batch.created_at.strftime('%Y-%m-%d')
        
        for data_type in DataType:
            type_data = [dp for dp in batch.data_points if dp.data_type == data_type]
            
            if type_data:
                file_path = self.file_storage_path / date_str / f"{data_type.value}.jsonl"
                file_path.parent.mkdir(parents=True, exist_ok=True)
                
                async with aiofiles.open(file_path, 'a') as f:
                    for data_point in type_data:
                        data_dict = {
                            'timestamp': data_point.timestamp.isoformat(),
                            'symbol': data_point.symbol,
                            'data_type': data_point.data_type.value,
                            'source': data_point.source.value,
                            'value': data_point.value,
                            'metadata': data_point.metadata,
                            'quality_score': data_point.quality_score
                        }
                        await f.write(json.dumps(data_dict) + '\n')
    
    async def _cache_recent_data(self, batch: DataBatch):
        """Cache recent data in Redis for fast access"""
        for data_point in batch.data_points:
            cache_key = f"recent:{data_point.symbol}:{data_point.data_type.value}"
            
            data_dict = {
                'timestamp': data_point.timestamp.isoformat(),
                'value': data_point.value,
                'quality_score': data_point.quality_score
            }
            
            # Store with 1 hour expiration
            self.redis_client.setex(cache_key, 3600, json.dumps(data_dict))
    
    async def get_recent_data(self, symbol: str, data_type: DataType, hours: int = 24) -> List[DataPoint]:
        """Get recent data from cache and database"""
        # First try cache
        cache_key = f"recent:{symbol}:{data_type.value}"
        cached_data = self.redis_client.get(cache_key)
        
        if cached_data:
            data_dict = json.loads(cached_data)
            return [DataPoint(
                timestamp=datetime.fromisoformat(data_dict['timestamp']),
                symbol=symbol,
                data_type=data_type,
                source=DataSource.CUSTOM,  # Mark as cached
                value=data_dict['value'],
                quality_score=data_dict['quality_score']
            )]
        
        # Fallback to database query
        # Implementation would depend on your database schema
        return []
    
    async def get_training_data(self, 
                               symbols: List[str], 
                               data_types: List[DataType],
                               start_date: datetime,
                               end_date: datetime) -> Dict[str, List[DataPoint]]:
        """Get data for training purposes"""
        training_data = {}
        
        # Read from stored files
        current_date = start_date
        while current_date <= end_date:
            date_str = current_date.strftime('%Y-%m-%d')
            
            for data_type in data_types:
                file_path = self.file_storage_path / date_str / f"{data_type.value}.jsonl"
                
                if file_path.exists():
                    if data_type.value not in training_data:
                        training_data[data_type.value] = []
                    
                    async with aiofiles.open(file_path, 'r') as f:
                        async for line in f:
                            try:
                                data_dict = json.loads(line.strip())
                                
                                if data_dict['symbol'] in symbols:
                                    data_point = DataPoint(
                                        timestamp=datetime.fromisoformat(data_dict['timestamp']),
                                        symbol=data_dict['symbol'],
                                        data_type=DataType(data_dict['data_type']),
                                        source=DataSource(data_dict['source']),
                                        value=data_dict['value'],
                                        metadata=data_dict['metadata'],
                                        quality_score=data_dict['quality_score']
                                    )
                                    training_data[data_type.value].append(data_point)
                            
                            except Exception as e:
                                logger.warning(f"Error parsing training data line: {e}")
            
            current_date += timedelta(days=1)
        
        return training_data

class DataManager:
    """Main data management orchestrator"""
    
    def __init__(self, config_path: str):
        with open(config_path, 'r') as f:
            self.config = json.load(f)
        
        self.sources: Dict[str, DataSourceInterface] = {}
        self.processor = DataProcessor(self.config.get('processing', {}))
        self.storage = DataStorage(self.config.get('storage', {}))
        self.batch_size = self.config.get('batch_size', 1000)
        self.processing_interval = self.config.get('processing_interval', 60)
        
        self._initialize_sources()
    
    def _initialize_sources(self):
        """Initialize all configured data sources"""
        sources_config = self.config.get('data_sources', {})
        
        for source_name, source_config in sources_config.items():
            if not source_config.get('enabled', True):
                continue
            
            try:
                source_type = source_config['source']
                
                if source_type == 'yahoo_finance':
                    self.sources[source_name] = YahooFinanceSource(source_config)
                elif source_type == 'binance':
                    self.sources[source_name] = BinanceSource(source_config)
                elif source_type == 'news_api':
                    self.sources[source_name] = NewsAPISource(source_config)
                
                logger.info(f"Initialized data source: {source_name}")
            
            except Exception as e:
                logger.error(f"Failed to initialize data source {source_name}: {e}")
    
    async def start_data_pipeline(self, symbols: List[str]):
        """Start the data ingestion and processing pipeline"""
        logger.info(f"Starting data pipeline for symbols: {symbols}")
        
        # Start real-time data collection
        tasks = []
        for source_name, source in self.sources.items():
            task = asyncio.create_task(
                self._collect_realtime_data(source, symbols, source_name)
            )
            tasks.append(task)
        
        # Start data processing
        processing_task = asyncio.create_task(self._process_data_pipeline())
        tasks.append(processing_task)
        
        # Wait for all tasks
        await asyncio.gather(*tasks)
    
    async def _collect_realtime_data(self, source: DataSourceInterface, symbols: List[str], source_name: str):
        """Collect real-time data from a source"""
        try:
            async for data_point in source.fetch_realtime(symbols):
                # Add to processing queue
                await self._add_to_processing_queue(data_point)
        
        except Exception as e:
            logger.error(f"Error in real-time data collection for {source_name}: {e}")
    
    async def _add_to_processing_queue(self, data_point: DataPoint):
        """Add data point to processing queue"""
        # In a real implementation, this would use a proper queue system
        # For now, we'll process immediately
        batch = DataBatch(
            data_points=[data_point],
            batch_id=f"single_{datetime.now().isoformat()}"
        )
        
        processed_batch = await self.processor.process_batch(batch)
        await self.storage.store_batch(processed_batch)
    
    async def _process_data_pipeline(self):
        """Main data processing pipeline"""
        while True:
            try:
                # Process any queued data
                # Implementation would depend on your queue system
                await asyncio.sleep(self.processing_interval)
            
            except Exception as e:
                logger.error(f"Error in data processing pipeline: {e}")
                await asyncio.sleep(60)  # Wait before retrying
    
    async def backfill_data(self, symbols: List[str], start_date: datetime, end_date: datetime):
        """Backfill historical data"""
        logger.info(f"Starting data backfill for {symbols} from {start_date} to {end_date}")
        
        for source_name, source in self.sources.items():
            try:
                data_points = await source.fetch_data(symbols, start_date, end_date)
                
                # Process in batches
                for i in range(0, len(data_points), self.batch_size):
                    batch_data = data_points[i:i + self.batch_size]
                    batch = DataBatch(
                        data_points=batch_data,
                        batch_id=f"backfill_{source_name}_{i}"
                    )
                    
                    processed_batch = await self.processor.process_batch(batch)
                    await self.storage.store_batch(processed_batch)
                
                logger.info(f"Completed backfill for {source_name}: {len(data_points)} data points")
            
            except Exception as e:
                logger.error(f"Error in backfill for {source_name}: {e}")
    
    async def get_training_dataset(self, 
                                  symbols: List[str],
                                  start_date: datetime,
                                  end_date: datetime) -> Dict[str, Any]:
        """Get formatted training dataset"""
        data_types = [DataType.PRICE_DATA, DataType.NEWS_DATA, DataType.VOLUME_DATA]
        
        raw_data = await self.storage.get_training_data(
            symbols, data_types, start_date, end_date
        )
        
        # Format for training
        training_dataset = {
            'symbols': symbols,
            'date_range': {'start': start_date.isoformat(), 'end': end_date.isoformat()},
            'data': raw_data,
            'metadata': {
                'total_points': sum(len(points) for points in raw_data.values()),
                'generated_at': datetime.now().isoformat()
            }
        }
        
        return training_dataset
    
    async def health_check(self) -> Dict[str, bool]:
        """Check health of all data sources"""
        health_status = {}
        
        for source_name, source in self.sources.items():
            try:
                health_status[source_name] = await source.health_check()
            except Exception as e:
                logger.error(f"Health check failed for {source_name}: {e}")
                health_status[source_name] = False
        
        return health_status

# Example usage
async def main():
    """Example usage of the data management system"""
    # Initialize data manager
    data_manager = DataManager('config/data_config.json')
    
    # Check health
    health = await data_manager.health_check()
    print(f"Data sources health: {health}")
    
    # Backfill some historical data
    symbols = ['AAPL', 'GOOGL', 'MSFT']
    start_date = datetime.now() - timedelta(days=30)
    end_date = datetime.now()
    
    await data_manager.backfill_data(symbols, start_date, end_date)
    
    # Get training dataset
    training_data = await data_manager.get_training_dataset(symbols, start_date, end_date)
    print(f"Training dataset contains {training_data['metadata']['total_points']} data points")

if __name__ == "__main__":
    asyncio.run(main())