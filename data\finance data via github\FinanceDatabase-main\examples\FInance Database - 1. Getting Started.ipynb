{"cells": [{"attachments": {}, "cell_type": "markdown", "id": "83014ba8", "metadata": {}, "source": ["<p align=\"center\">\n", "    <img src=\"https://user-images.githubusercontent.com/46355364/220746807-669cdbc1-ac67-404c-b0bb-4a3d67d9931f.jpg\" alt=\"Logo\">\n", "</center>\n", "\n", "[![GitHub Sponsors](https://img.shields.io/badge/Sponsor_this_Project-grey?logo=github)](https://github.com/sponsors/JerBouma)\n", "[![Documentation](https://img.shields.io/badge/Documentation-grey?logo=readme)](https://www.jeroenbouma.com/projects/financedatabase)\n", "[![Supported Python Versions](https://img.shields.io/pypi/pyversions/financedatabase)](https://pypi.org/project/financedatabase/)\n", "[![PYPI Version](https://img.shields.io/pypi/v/financedatabase)](https://pypi.org/project/financedatabase/)\n", "[![PYPI Downloads](https://static.pepy.tech/badge/financedatabase/month)](https://pepy.tech/project/financedatabase)\n", "\n", "The **FinanceDatabase** serves the role of providing anyone with any type of financial product categorisation entirely for free. To be able to achieve this, the FinanceDatabase relies on involvement from the community to add, edit and remove tickers over time. This is made easy enough that anyone, even with a lack of coding experience can contribute because of the usage of CSV files that can be manually edited. I'd like to invite you to go to the **[Contributing Guidelines](https://github.com/JerBouma/FinanceDatabase/blob/main/CONTRIBUTING.md)** to understand how you can help. Thank you!\n", "\n", "As a private investor, the sheer amount of information that can be found on the internet is rather daunting. Trying to \n", "understand what type of companies or ETFs are available is incredibly challenging with there being millions of\n", "companies and derivatives available on the market. Sure, the most traded companies and ETFs can quickly be found\n", "simply because they are known to the public (for example, Microsoft, Tesla, S&P500 ETF or an All-World ETF). However, \n", "what else is out there is often unknown.\n", "\n", "**This database tries to solve that**. It features 300.000+ symbols containing Equities, ETFs, Funds, Indices, \n", "Currencies, Cryptocurrencies and Money Markets. It therefore allows you to obtain a broad overview of sectors,\n", "industries, types of investments and much more.\n", "\n", "The aim of this database is explicitly _not_ to provide up-to-date fundamentals or stock data as those can be obtained \n", "with ease (with the help of this database) by using the [FinanceToolkit](https://github.com/JerBouma/FinanceToolkit). Instead, it gives insights into the products \n", "that exist in each country, industry and sector and gives the most essential information about each product. With \n", "this information, you can analyse specific areas of the financial world and/or find a product that is hard to find.\n"]}, {"attachments": {}, "cell_type": "markdown", "id": "3a2a1a46", "metadata": {}, "source": ["# Installation\n", "To install the FinanceDatabase it simply requires the following:\n", "\n", "```\n", "pip install financedatabase -U\n", "```\n", "\n", "From within Python use:\n", "\n", "```python\n", "import financedatabase as fd\n", "```"]}, {"cell_type": "code", "execution_count": null, "id": "e847b00b", "metadata": {}, "outputs": [], "source": ["import financedatabase as fd\n", "\n", "# Optional Financial Modeling Prep API key for Finance Toolkit functionality\n", "API_KEY = \"FINANCIAL_MODELING_PREP_API_KEY\""]}, {"attachments": {}, "cell_type": "markdown", "id": "d45b268e", "metadata": {}, "source": ["Initalization of each asset class is only required <u>once</u>. It is therefore important you save the class to a variable so that you can query the database much quicker. A simple example is shown below."]}, {"cell_type": "code", "execution_count": 2, "id": "6e0271f5", "metadata": {}, "outputs": [{"data": {"text/html": ["<div>\n", "<style scoped>\n", "    .dataframe tbody tr th:only-of-type {\n", "        vertical-align: middle;\n", "    }\n", "\n", "    .dataframe tbody tr th {\n", "        vertical-align: top;\n", "    }\n", "\n", "    .dataframe thead th {\n", "        text-align: right;\n", "    }\n", "</style>\n", "<table border=\"1\" class=\"dataframe\">\n", "  <thead>\n", "    <tr style=\"text-align: right;\">\n", "      <th></th>\n", "      <th>name</th>\n", "      <th>summary</th>\n", "      <th>currency</th>\n", "      <th>sector</th>\n", "      <th>industry_group</th>\n", "      <th>industry</th>\n", "      <th>exchange</th>\n", "      <th>market</th>\n", "      <th>country</th>\n", "      <th>state</th>\n", "      <th>city</th>\n", "      <th>zipcode</th>\n", "      <th>website</th>\n", "      <th>market_cap</th>\n", "      <th>isin</th>\n", "      <th>cusip</th>\n", "      <th>figi</th>\n", "      <th>composite_figi</th>\n", "      <th>shareclass_figi</th>\n", "    </tr>\n", "    <tr>\n", "      <th>symbol</th>\n", "      <th></th>\n", "      <th></th>\n", "      <th></th>\n", "      <th></th>\n", "      <th></th>\n", "      <th></th>\n", "      <th></th>\n", "      <th></th>\n", "      <th></th>\n", "      <th></th>\n", "      <th></th>\n", "      <th></th>\n", "      <th></th>\n", "      <th></th>\n", "      <th></th>\n", "      <th></th>\n", "      <th></th>\n", "      <th></th>\n", "      <th></th>\n", "    </tr>\n", "  </thead>\n", "  <tbody>\n", "    <tr>\n", "      <th>000002.SZ</th>\n", "      <td>China Vanke Co., Ltd.</td>\n", "      <td>China Vanke Co., Ltd., together with its subsi...</td>\n", "      <td>CNY</td>\n", "      <td>Real Estate</td>\n", "      <td>Real Estate</td>\n", "      <td>Real Estate Management &amp; Development</td>\n", "      <td>SHZ</td>\n", "      <td>Shenzhen Stock Exchange</td>\n", "      <td>China</td>\n", "      <td>NaN</td>\n", "      <td>Shenzhen</td>\n", "      <td>518083</td>\n", "      <td>http://www.vanke.com</td>\n", "      <td>Large Cap</td>\n", "      <td>CNE100001SR9</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "    </tr>\n", "    <tr>\n", "      <th>000004.SZ</th>\n", "      <td>two</td>\n", "      <td>two is a blank check company. The company was ...</td>\n", "      <td>CNY</td>\n", "      <td>Financials</td>\n", "      <td>Diversified Financials</td>\n", "      <td>Diversified Financial Services</td>\n", "      <td>SHZ</td>\n", "      <td>Shenzhen Stock Exchange</td>\n", "      <td>United States</td>\n", "      <td>CA</td>\n", "      <td>San Francisco</td>\n", "      <td>94129</td>\n", "      <td>http://www.sz000004.cn</td>\n", "      <td>Micro Cap</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "    </tr>\n", "    <tr>\n", "      <th>000005.SZ</th>\n", "      <td>Shenzhen Fountain Corporation</td>\n", "      <td>Shenzhen Fountain Corporation engages in real ...</td>\n", "      <td>CNY</td>\n", "      <td>Real Estate</td>\n", "      <td>Real Estate</td>\n", "      <td>Real Estate Management &amp; Development</td>\n", "      <td>SHZ</td>\n", "      <td>Shenzhen Stock Exchange</td>\n", "      <td>China</td>\n", "      <td>NaN</td>\n", "      <td>Shenzhen</td>\n", "      <td>518001</td>\n", "      <td>http://www.fountain.com.cn</td>\n", "      <td>Small Cap</td>\n", "      <td>CNE0000001L7</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "    </tr>\n", "    <tr>\n", "      <th>000006.SZ</th>\n", "      <td>Shenzhen Zhenye (Group) Co.,Ltd.</td>\n", "      <td>Shenzhen Zhenye (Group) Co.,Ltd. engages in th...</td>\n", "      <td>CNY</td>\n", "      <td>Real Estate</td>\n", "      <td>Real Estate</td>\n", "      <td>Real Estate Management &amp; Development</td>\n", "      <td>SHZ</td>\n", "      <td>Shenzhen Stock Exchange</td>\n", "      <td>China</td>\n", "      <td>NaN</td>\n", "      <td>Shenzhen</td>\n", "      <td>518008</td>\n", "      <td>http://www.zhenye.com</td>\n", "      <td>Small Cap</td>\n", "      <td>CNE000000164</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "    </tr>\n", "    <tr>\n", "      <th>000007.SZ</th>\n", "      <td>Shenzhen Quanxinhao Co., Ltd.</td>\n", "      <td>Shenzhen Quanxinhao Co., Ltd. owns and operate...</td>\n", "      <td>CNY</td>\n", "      <td>Consumer Discretionary</td>\n", "      <td>Consumer Services</td>\n", "      <td>Hotels, Restaurants &amp; Leisure</td>\n", "      <td>SHZ</td>\n", "      <td>Shenzhen Stock Exchange</td>\n", "      <td>China</td>\n", "      <td>NaN</td>\n", "      <td>Shenzhen</td>\n", "      <td>518031</td>\n", "      <td>http://www.sz000007.com</td>\n", "      <td>Micro Cap</td>\n", "      <td>CNE0000000P0</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "    </tr>\n", "    <tr>\n", "      <th>...</th>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "    </tr>\n", "    <tr>\n", "      <th>ZZMS.SG</th>\n", "      <td>Commerzbank AG</td>\n", "      <td>Commerzbank AG provides banking and capital ma...</td>\n", "      <td>EUR</td>\n", "      <td>Financials</td>\n", "      <td>Banks</td>\n", "      <td>Banks</td>\n", "      <td>STU</td>\n", "      <td>Stuttgart Stock Exchange</td>\n", "      <td>Germany</td>\n", "      <td>NaN</td>\n", "      <td>Frankfurt am Main</td>\n", "      <td>60311</td>\n", "      <td>http://www.commerzbank.com</td>\n", "      <td>Mid Cap</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "    </tr>\n", "    <tr>\n", "      <th>ZZVA.BE</th>\n", "      <td>Deutsche Bank AG</td>\n", "      <td>NaN</td>\n", "      <td>EUR</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>BER</td>\n", "      <td>Berlin Stock Exchange</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "    </tr>\n", "    <tr>\n", "      <th>ZZVA.DU</th>\n", "      <td>Deutsche Bank AG</td>\n", "      <td>NaN</td>\n", "      <td>EUR</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>DUS</td>\n", "      <td>Dusseldorf Stock Exchange</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "    </tr>\n", "    <tr>\n", "      <th>ZZZ.TO</th>\n", "      <td>Sleep Country Canada Holdings Inc.</td>\n", "      <td>Sleep Country Canada Holdings Inc., together w...</td>\n", "      <td>CAD</td>\n", "      <td>Consumer Discretionary</td>\n", "      <td>Retailing</td>\n", "      <td><PERSON><PERSON> Retail</td>\n", "      <td>TOR</td>\n", "      <td>TSX Toronto Exchange</td>\n", "      <td>Canada</td>\n", "      <td>ON</td>\n", "      <td><PERSON><PERSON></td>\n", "      <td>L6T 4N8</td>\n", "      <td>http://www.sleepcountry.ca</td>\n", "      <td>Small Cap</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "    </tr>\n", "    <tr>\n", "      <th>ZZZOF</th>\n", "      <td>one</td>\n", "      <td>one does not have significant operations. It i...</td>\n", "      <td>USD</td>\n", "      <td>Financials</td>\n", "      <td>Diversified Financials</td>\n", "      <td>Diversified Financial Services</td>\n", "      <td>PNK</td>\n", "      <td>OTC Bulletin Board</td>\n", "      <td>United States</td>\n", "      <td>CA</td>\n", "      <td>San Francisco</td>\n", "      <td>94129</td>\n", "      <td>http://www.a-star.co</td>\n", "      <td>Small Cap</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "    </tr>\n", "  </tbody>\n", "</table>\n", "<p>158965 rows × 19 columns</p>\n", "</div>"], "text/plain": ["                                         name  \\\n", "symbol                                          \n", "000002.SZ               China Vanke Co., Ltd.   \n", "000004.SZ                                 two   \n", "000005.SZ       Shenzhen Fountain Corporation   \n", "000006.SZ    Shenzhen Zhenye (Group) Co.,Ltd.   \n", "000007.SZ       Shenzhen Quanxinhao Co., Ltd.   \n", "...                                       ...   \n", "ZZMS.SG                        Commerzbank AG   \n", "ZZVA.BE                      Deutsche Bank AG   \n", "ZZVA.DU                      Deutsche Bank AG   \n", "ZZZ.TO     Sleep Country Canada Holdings Inc.   \n", "ZZZOF                                     one   \n", "\n", "                                                     summary currency  \\\n", "symbol                                                                  \n", "000002.SZ  China Vanke Co., Ltd., together with its subsi...      CNY   \n", "000004.SZ  two is a blank check company. The company was ...      CNY   \n", "000005.SZ  Shenzhen Fountain Corporation engages in real ...      CNY   \n", "000006.SZ  Shenzhen Zhenye (Group) Co.,Ltd. engages in th...      CNY   \n", "000007.SZ  Shenzhen Quanxinhao Co., Ltd. owns and operate...      CNY   \n", "...                                                      ...      ...   \n", "ZZMS.SG    Commerzbank AG provides banking and capital ma...      EUR   \n", "ZZVA.BE                                                  NaN      EUR   \n", "ZZVA.DU                                                  NaN      EUR   \n", "ZZZ.TO     Sleep Country Canada Holdings Inc., together w...      CAD   \n", "ZZZOF      one does not have significant operations. It i...      USD   \n", "\n", "                           sector          industry_group  \\\n", "symbol                                                      \n", "000002.SZ             Real Estate             Real Estate   \n", "000004.SZ              Financials  Diversified Financials   \n", "000005.SZ             Real Estate             Real Estate   \n", "000006.SZ             Real Estate             Real Estate   \n", "000007.SZ  Consumer Discretionary       Consumer Services   \n", "...                           ...                     ...   \n", "ZZMS.SG                Financials                   Banks   \n", "ZZVA.BE                       NaN                     NaN   \n", "ZZVA.DU                       NaN                     NaN   \n", "ZZZ.TO     Consumer Discretionary               Retailing   \n", "ZZZOF                  Financials  Diversified Financials   \n", "\n", "                                       industry exchange  \\\n", "symbol                                                     \n", "000002.SZ  Real Estate Management & Development      SHZ   \n", "000004.SZ        Diversified Financial Services      SHZ   \n", "000005.SZ  Real Estate Management & Development      SHZ   \n", "000006.SZ  Real Estate Management & Development      SHZ   \n", "000007.SZ         Hotels, Restaurants & Leisure      SHZ   \n", "...                                         ...      ...   \n", "ZZMS.SG                                   Banks      STU   \n", "ZZVA.BE                                     NaN      BER   \n", "ZZVA.DU                                     NaN      DUS   \n", "ZZZ.TO                         Specialty Retail      TOR   \n", "ZZZOF            Diversified Financial Services      PNK   \n", "\n", "                              market        country state               city  \\\n", "symbol                                                                         \n", "000002.SZ    Shenzhen Stock Exchange          China   NaN           Shenzhen   \n", "000004.SZ    Shenzhen Stock Exchange  United States    CA      San Francisco   \n", "000005.SZ    Shenzhen Stock Exchange          China   NaN           Shenzhen   \n", "000006.SZ    Shenzhen Stock Exchange          China   NaN           Shenzhen   \n", "000007.SZ    Shenzhen Stock Exchange          China   NaN           Shenzhen   \n", "...                              ...            ...   ...                ...   \n", "ZZMS.SG     Stuttgart Stock Exchange        Germany   NaN  Frankfurt am Main   \n", "ZZVA.BE        Berlin Stock Exchange            NaN   NaN                NaN   \n", "ZZVA.DU    Dusseldorf Stock Exchange            NaN   NaN                NaN   \n", "ZZZ.TO          TSX Toronto Exchange         Canada    ON           Brampton   \n", "ZZZOF             OTC Bulletin Board  United States    CA      San Francisco   \n", "\n", "           zipcode                     website market_cap          isin cusip  \\\n", "symbol                                                                          \n", "000002.SZ   518083        http://www.vanke.com  Large Cap  CNE100001SR9   NaN   \n", "000004.SZ    94129      http://www.sz000004.cn  Micro Cap           NaN   NaN   \n", "000005.SZ   518001  http://www.fountain.com.cn  Small Cap  CNE0000001L7   NaN   \n", "000006.SZ   518008       http://www.zhenye.com  Small Cap  CNE000000164   NaN   \n", "000007.SZ   518031     http://www.sz000007.com  Micro Cap  CNE0000000P0   NaN   \n", "...            ...                         ...        ...           ...   ...   \n", "ZZMS.SG      60311  http://www.commerzbank.com    Mid Cap           NaN   NaN   \n", "ZZVA.BE        NaN                         NaN        NaN           NaN   NaN   \n", "ZZVA.DU        NaN                         NaN        NaN           NaN   NaN   \n", "ZZZ.TO     L6T 4N8  http://www.sleepcountry.ca  Small Cap           NaN   NaN   \n", "ZZZOF        94129        http://www.a-star.co  Small Cap           NaN   NaN   \n", "\n", "          figi composite_figi shareclass_figi  \n", "symbol                                         \n", "000002.SZ  NaN            NaN             NaN  \n", "000004.SZ  NaN            NaN             NaN  \n", "000005.SZ  NaN            NaN             NaN  \n", "000006.SZ  NaN            NaN             NaN  \n", "000007.SZ  NaN            NaN             NaN  \n", "...        ...            ...             ...  \n", "ZZMS.SG    NaN            NaN             NaN  \n", "ZZVA.BE    NaN            NaN             NaN  \n", "ZZVA.DU    NaN            NaN             NaN  \n", "ZZZ.TO     NaN            NaN             NaN  \n", "ZZZOF      NaN            NaN             NaN  \n", "\n", "[158965 rows x 19 columns]"]}, "execution_count": 2, "metadata": {}, "output_type": "execute_result"}], "source": ["# Initialize the Equities database\n", "equities = fd.Equities()\n", "\n", "# Obtain all countries from the database\n", "equities.select()"]}, {"attachments": {}, "cell_type": "markdown", "id": "c578765b", "metadata": {}, "source": ["With `show_options` all possible options are given per column. **This is useful as it doesn't require loading the larger data files.** For example, obtaining all options for equities is done as follow:"]}, {"cell_type": "code", "execution_count": 3, "id": "a8f1241b", "metadata": {}, "outputs": [{"data": {"text/plain": ["{'currency': array(['ARS', 'AUD', 'BRL', 'CAD', 'CHF', 'CLP', 'CNY', 'COP', 'CZK',\n", "        'DKK', 'EUR', 'GBP', 'HKD', 'HUF', 'IDR', 'ILA', 'ILS', 'INR',\n", "        'ISK', 'JP<PERSON>', 'KES', 'KRW', 'LKR', 'MXN', 'MYR', 'NOK', 'NZD',\n", "        'PEN', 'PHP', 'PLN', 'QAR', 'RUB', 'SAR', 'SEK', 'SGD', 'THB',\n", "        'TRY', 'TWD', 'USD', 'ZAC', 'ZAR'], dtype=object),\n", " 'sector': array(['Communication Services', 'Consumer Discretionary',\n", "        'Consumer Staples', 'Energy', 'Financials', 'Health Care',\n", "        'Industrials', 'Information Technology', 'Materials',\n", "        'Real Estate', 'Utilities'], dtype=object),\n", " 'industry_group': array(['Automobiles & Components', 'Banks', 'Capital Goods',\n", "        'Commercial & Professional Services',\n", "        'Consumer Durables & Apparel', 'Consumer Services',\n", "        'Diversified Financials', 'Energy', 'Food & Staples Retailing',\n", "        'Food, Beverage & Tobacco', 'Health Care Equipment & Services',\n", "        'Household & Personal Products', 'Insurance', 'Materials',\n", "        'Media & Entertainment',\n", "        'Pharmaceuticals, Biotechnology & Life Sciences', 'Real Estate',\n", "        'Retailing', 'Semiconductors & Semiconductor Equipment',\n", "        'Software & Services', 'Technology Hardware & Equipment',\n", "        'Telecommunication Services', 'Transportation', 'Utilities'],\n", "       dtype=object),\n", " 'industry': array(['Aerospace & Defense', 'Air Freight & Logistics', 'Airlines',\n", "        'Auto Components', 'Automobiles', 'Banks', 'Beverages',\n", "        'Biotechnology', 'Building Products', 'Capital Markets',\n", "        'Chemicals', 'Commercial Services & Supplies',\n", "        'Communications Equipment', 'Construction & Engineering',\n", "        'Construction Materials', 'Consumer Finance', 'Distributors',\n", "        'Diversified Consumer Services', 'Diversified Financial Services',\n", "        'Diversified Telecommunication Services', 'Electric Utilities',\n", "        'Electrical Equipment',\n", "        'Electronic Equipment, Instruments & Components',\n", "        'Energy Equipment & Services', 'Entertainment',\n", "        'Equity Real Estate Investment Trusts (REITs)',\n", "        'Food & Staples Retailing', 'Food Products', 'Gas Utilities',\n", "        'Health Care Equipment & Supplies',\n", "        'Health Care Providers & Services', 'Health Care Technology',\n", "        'Hotels, Restaurants & Leisure', 'Household Durables',\n", "        'Household Products', 'IT Services',\n", "        'Independent Power and Renewable Electricity Producers',\n", "        'Industrial Conglomerates', 'Insurance',\n", "        'Interactive Media & Services',\n", "        'Internet & Direct Marketing Retail', 'Machinery', 'Marine',\n", "        'Media', 'Metals & Mining', 'Multi-Utilities',\n", "        'Oil, Gas & Consumable Fuels', 'Paper & Forest Products',\n", "        'Pharmaceuticals', 'Professional Services',\n", "        'Real Estate Management & Development', 'Road & Rail',\n", "        'Semiconductors & Semiconductor Equipment', 'Software',\n", "        'Specialty Retail', 'Technology Hardware, Storage & Peripherals',\n", "        'Textiles, Apparel & Luxury Goods', 'Thrifts & Mortgage Finance',\n", "        'Tobacco', 'Trading Companies & Distributors',\n", "        'Transportation Infrastructure', 'Water Utilities'], dtype=object),\n", " 'exchange': array(['AMS', 'AQS', 'ASE', 'ASX', 'ATH', 'BER', 'BRU', 'BSE', 'BTS',\n", "        'BUD', 'BUE', 'CAI', 'CCS', 'CNQ', 'CPH', 'CSE', 'DOH', 'DUS',\n", "        'EBS', 'ENX', 'FKA', 'FRA', 'GER', 'HAM', 'HAN', 'HEL', 'HKG',\n", "        'ICE', 'IOB', 'ISE', 'IST', 'JKT', 'JNB', 'JPX', 'KLS', 'KOE',\n", "        'KSC', 'LIS', 'L<PERSON>', 'LSE', 'MCE', 'MCX', 'MEX', 'MIL', 'MUN',\n", "        'NAE', 'NAS', 'NCM', 'NEO', 'NGM', 'NMS', 'NSE', 'NSI', 'NYQ',\n", "        'NYS', 'NZE', 'OBB', 'OSL', 'PAR', 'PCX', 'PNK', 'PRA', 'RIS',\n", "        'SAO', 'SAP', 'SAT', 'SAU', 'SES', 'SET', 'SGO', 'SHH', 'SHZ',\n", "        'STO', 'STU', 'TAI', 'TAL', 'TLO', 'TLV', 'TOR', 'TWO', 'VAN',\n", "        'VIE'], dtype=object),\n", " 'market': array(['Aequitas NEO Exchange (Lit Book)', 'Aktie Torget',\n", "        'Aquis Exchange', 'Athens Stock Exchange',\n", "        'Australian Securities Exchange', 'BATS BZX Exchange', 'BSE India',\n", "        'BX Worldcaps', 'Berlin Stock Exchange',\n", "        'Bolsa De Valores De Caracas',\n", "        'Bolsa de Comercio de Santiago de Chile', 'Borsa Istanbul',\n", "        'Borsa Italiana', 'Bovespa Soma', 'Budapest Stock Exchange',\n", "        'Buenos Aires Mercato De Valores', 'Bursa Malaysia',\n", "        'Canadian Securities Exchange', 'Dusseldorf Stock Exchange',\n", "        'Egyptian Exchange', 'EuroTLX', 'Euronext', 'Euronext Amsterdam',\n", "        'Euronext Brussels', 'Euronext Lisbon', 'Euronext Paris',\n", "        'First North Copenhagen', 'First North Iceland',\n", "        'Frankfurt Stock Exchange', 'Fukuoka Stock Exchange',\n", "        'Hamburg Stock Exchange', 'Hanover Stock Exchange',\n", "        'Hong Kong Stock Exchange', 'Indonesia Stock Exchange',\n", "        'Irish Stock Exchange', 'Johannesburg Stock Exchange', 'KONEX',\n", "        'KOSPI Stock Market', 'London Stock Exchange (OTC and ITR)',\n", "        'London Stock Exchange (international)',\n", "        'Metropolitan Stock Exchange', 'Mexico Stock Exchange',\n", "        'Moscow Exchange - MICEX', 'Munich Stock Exchange',\n", "        'NASDAQ Capital Market', 'NASDAQ Global Select',\n", "        'NASDAQ OMX Helsinki', 'NASDAQ OMX Riga', 'NASDAQ OMX Stockholm',\n", "        'NASDAQ OMX Tallinn', 'NASDAQ OMX Vilnius', 'NYSE Arca',\n", "        'NYSE MKT', 'Nasdaq Copenhagen',\n", "        'National Stock Exchange of India', 'New York Stock Exchange',\n", "        'New Zealand Exchange', 'Nordic Growth Market',\n", "        'OTC Bulletin Board', 'Oslo Bors', 'Prague Stock Exchange',\n", "        'Qatar Exchange', 'Sapporo Securities Exchange',\n", "        'Saudi Arabian Stock Exchange', 'Shanghai Stock Exchange',\n", "        'Shenzhen Stock Exchange', 'Singapore Exchange',\n", "        'Sociedad de Bolsas (SIBE)', 'Stuttgart Stock Exchange',\n", "        'TSX Toronto Exchange', 'TSX Venture Exchange',\n", "        'Taiwan Stock Exchange', 'Tel Aviv Stock Exchange',\n", "        'The Stock Exchange of Thailand', 'Tokyo Stock Exchange',\n", "        'Vienna Stock Exchange', 'XETRA', 'us24_market', 'us_market'],\n", "       dtype=object),\n", " 'country': array(['Afghanistan', 'Anguilla', 'Argentina', 'Australia', 'Austria',\n", "        'Azerbaijan', 'Bahamas', 'Bangladesh', 'Barbados', 'Belgium',\n", "        'Belize', 'Bermuda', 'Botswana', 'Brazil',\n", "        'British Virgin Islands', 'Cambodia', 'Canada', 'Cayman Islands',\n", "        'Chile', 'China', 'Colombia', 'Costa Rica', 'Cyprus',\n", "        'Czech Republic', 'Denmark', 'Dominican Republic', 'Egypt',\n", "        'Estonia', 'Falkland Islands', 'Finland', 'France',\n", "        'French Guiana', 'Gabon', 'Georgia', 'Germany', 'Ghana',\n", "        'Gibraltar', 'Greece', 'Greenland', 'Guernsey', 'Hong Kong',\n", "        'Hungary', 'Iceland', 'India', 'Indonesia', 'Ireland',\n", "        'Isle of Man', 'Israel', 'Italy', 'Ivory Coast', 'Japan', 'Jersey',\n", "        'Jordan', 'Kazakhstan', 'Kenya', 'Kyrgyzstan', 'Latvia',\n", "        'Liechtenstein', 'Lithuania', 'Luxembourg', 'Macau', 'Macedonia',\n", "        'Malaysia', 'Malta', 'Mauritius', 'Mexico', 'Monaco', 'Mongolia',\n", "        'Montenegro', 'Morocco', 'Mozambique', 'Myanmar', 'Namibia',\n", "        'Netherlands', 'Netherlands Antilles', 'New Zealand', 'Nigeria',\n", "        'Norway', 'Panama', 'Papua New Guinea', 'Peru', 'Philippines',\n", "        'Poland', 'Portugal', 'Qatar', 'Reunion', 'Romania', 'Russia',\n", "        'Saudi Arabia', 'Senegal', 'Singapore', 'Slovakia', 'Slovenia',\n", "        'South Africa', 'South Korea', 'Spain', 'Suriname', 'Sweden',\n", "        'Switzerland', 'Taiwan', 'Tanzania', 'Thailand', 'Turkey',\n", "        'Ukraine', 'United Arab Emirates', 'United Kingdom',\n", "        'United States', 'Uruguay', 'Vietnam', 'Zambia'], dtype=object),\n", " 'state': array(['AB', 'ACT', 'AK', 'AL', 'AM', 'AN', 'AP', 'AR', 'AV', 'AZ', 'BA',\n", "        'BC', 'BG', 'BI', 'BJ', 'BL', 'BO', 'BS', 'CA', 'CE', 'CI', 'CO',\n", "        'CT', 'CU', 'DC', 'DE', 'DF', 'EM', 'ES', 'FE', 'FI', 'FL', 'FO',\n", "        'FR', 'GA', 'GE', 'GJ', 'GO', 'GU', 'Gujarat', 'HI', 'IA', 'ID',\n", "        'IL', 'IN', 'JA', 'KS', 'KY', 'LA', 'LC', 'LT', 'LU', 'MA', 'MB',\n", "        '<PERSON>', '<PERSON>', '<PERSON>', 'M<PERSON>', '<PERSON>', 'M<PERSON>', 'MO', 'MS', 'MT', 'NB', 'NC',\n", "        'ND', 'NE', 'NF', 'NH', 'NJ', 'NL', 'NM', 'NS', 'NSW', 'NT', 'NU',\n", "        'NV', 'NY', 'OH', 'OK', 'ON', 'OR', 'PA', 'PD', 'PE', 'PG', 'PI',\n", "        'PR', 'PS', 'PV', 'QC', 'QLD', 'QR', 'RA', 'R<PERSON>', 'R<PERSON>', 'RJ', 'RM',\n", "        'RN', 'RS', 'SA', 'SC', 'SD', 'SE', 'SI', 'SK', 'SO', 'SP', 'TAS',\n", "        'TN', 'TO', 'TR', 'TS', 'TV', 'TX', 'UD', 'UT', 'VA', 'VC', 'VE',\n", "        'VI', 'VIC', 'VR', 'VT', 'WA', 'WI', 'WV', 'WY', 'YT'],\n", "       dtype=object),\n", " 'city': array([\"'s-Hertogenbosch\", '6th of October', 'Aabenraa', ...,\n", "        'a¬Ä¬òs-Hertogenbosch', 'tacheng', '√É¬çlhavo'], dtype=object),\n", " 'zipcode': array(['00-105', '00-116', '00-124', ..., 'YO8 8PH', 'Z05T1X3', 'v4B 3L1'],\n", "       dtype=object),\n", " 'market_cap': array(['Large Cap', 'Mega Cap', 'Micro Cap', 'Mid Cap', 'Nano Cap',\n", "        'Small Cap'], dtype=object),\n", " 'isin': array(['AN8068571086', 'ANN4327C1220', 'AT000000STR1', ...,\n", "        'ZAE000265971', 'ZAE000296554', 'ZAE000298253'], dtype=object),\n", " 'cusip': array(['00089H106', '00090Q103', '00108J109', ..., '99406100', '99501108',\n", "        '99502106'], dtype=object),\n", " 'figi': array(['#REF!', 'BBG000B9XKF0', 'BBG000B9XZV9', ..., 'BBG01FC8CFV3',\n", "        'BBG01FPC3G48', 'BBG01FRH5MP7'], dtype=object),\n", " 'composite_figi': array(['BBG000B9WX45', 'BBG000B9XG87', 'BBG000B9XRY4', ...,\n", "        'BBG01FC8CFN2', 'BBG01FP5R015', 'BBG01FRH5MK2'], dtype=object),\n", " 'shareclass_figi': array(['BBG001S112S8', 'BBG001S112X2', 'BBG001S112Y1', ...,\n", "        'BBG01CCBKDK1', 'BBG01FP5R033', 'BBG01FRH5ND8'], dtype=object)}"]}, "execution_count": 3, "metadata": {}, "output_type": "execute_result"}], "source": ["# Obtain all possible options for equities\n", "fd.show_options(\"equities\")"]}, {"cell_type": "markdown", "id": "0a0b5978", "metadata": {}, "source": ["As the equities database has already been loaded in, it is also possible to use a similar functionality from within the class as follows. The main difference is that this functionality allows you to see the options based on a specific filtering. For example."]}, {"cell_type": "code", "execution_count": 4, "id": "397db95a", "metadata": {}, "outputs": [{"data": {"text/plain": ["{'currency': array(['ARS', 'AUD', 'BRL', 'CHF', 'CZK', 'EUR', 'GBP', 'ILA', 'MXN',\n", "        'NOK', 'RUB', 'USD', 'ZAC'], dtype=object),\n", " 'sector': array(['Communication Services', 'Consumer Discretionary',\n", "        'Consumer Staples', 'Energy', 'Financials', 'Health Care',\n", "        'Industrials', 'Information Technology', 'Materials',\n", "        'Real Estate', 'Utilities'], dtype=object),\n", " 'industry_group': array(['Automobiles & Components', 'Banks', 'Capital Goods',\n", "        'Commercial & Professional Services',\n", "        'Consumer Durables & Apparel', 'Consumer Services',\n", "        'Diversified Financials', 'Energy', 'Food & Staples Retailing',\n", "        'Food, Beverage & Tobacco', 'Health Care Equipment & Services',\n", "        'Household & Personal Products', 'Insurance', 'Materials',\n", "        'Media & Entertainment',\n", "        'Pharmaceuticals, Biotechnology & Life Sciences', 'Real Estate',\n", "        'Retailing', 'Semiconductors & Semiconductor Equipment',\n", "        'Software & Services', 'Technology Hardware & Equipment',\n", "        'Telecommunication Services', 'Transportation', 'Utilities'],\n", "       dtype=object),\n", " 'industry': array(['Aerospace & Defense', 'Air Freight & Logistics', 'Airlines',\n", "        'Auto Components', 'Automobiles', 'Banks', 'Beverages',\n", "        'Biotechnology', 'Building Products', 'Capital Markets',\n", "        'Chemicals', 'Commercial Services & Supplies',\n", "        'Communications Equipment', 'Construction & Engineering',\n", "        'Consumer Finance', 'Diversified Financial Services',\n", "        'Diversified Telecommunication Services', 'Electrical Equipment',\n", "        'Electronic Equipment, Instruments & Components',\n", "        'Energy Equipment & Services',\n", "        'Equity Real Estate Investment Trusts (REITs)',\n", "        'Food & Staples Retailing', 'Health Care Equipment & Supplies',\n", "        'Hotels, Restaurants & Leisure', 'Household Durables',\n", "        'Household Products', 'IT Services',\n", "        'Independent Power and Renewable Electricity Producers',\n", "        'Insurance', 'Interactive Media & Services',\n", "        'Internet & Direct Marketing Retail', 'Machinery',\n", "        'Metals & Mining', 'Oil, Gas & Consumable Fuels',\n", "        'Pharmaceuticals', 'Professional Services',\n", "        'Real Estate Management & Development',\n", "        'Semiconductors & Semiconductor Equipment', 'Software',\n", "        'Specialty Retail', 'Technology Hardware, Storage & Peripherals',\n", "        'Textiles, Apparel & Luxury Goods'], dtype=object),\n", " 'exchange': array(['AMS', 'ASE', 'ASX', 'BER', 'BRU', 'BUE', 'DUS', 'EBS', 'FRA',\n", "        'GER', 'HAM', 'HAN', 'IOB', 'JNB', 'LSE', 'MCE', 'MCX', 'MEX',\n", "        'MIL', 'M<PERSON>', 'NCM', 'NGM', 'NMS', 'NYQ', 'OSL', 'PAR', 'PNK',\n", "        'PRA', 'SAO', 'STU', 'TLO', 'TLV', 'VIE'], dtype=object),\n", " 'market': array(['Australian Securities Exchange', 'BX Worldcaps',\n", "        'Berlin Stock Exchange', 'Borsa Italiana', 'Bovespa Soma',\n", "        'Buenos Aires Mercato De Valores', 'Dusseldorf Stock Exchange',\n", "        'EuroTLX', 'Euronext Amsterdam', 'Euronext Brussels',\n", "        'Euronext Paris', 'Frankfurt Stock Exchange',\n", "        'Hamburg Stock Exchange', 'Hanover Stock Exchange',\n", "        'Johannesburg Stock Exchange',\n", "        'London Stock Exchange (OTC and ITR)',\n", "        'London Stock Exchange (international)', 'Mexico Stock Exchange',\n", "        'Moscow Exchange - MICEX', 'Munich Stock Exchange',\n", "        'NASDAQ Capital Market', 'NASDAQ Global Select', 'NYSE MKT',\n", "        'New York Stock Exchange', 'Nordic Growth Market',\n", "        'OTC Bulletin Board', 'Oslo Bors', 'Prague Stock Exchange',\n", "        'Sociedad de Bolsas (SIBE)', 'Stuttgart Stock Exchange',\n", "        'Tel Aviv Stock Exchange', 'Vienna Stock Exchange', 'XETRA'],\n", "       dtype=object),\n", " 'country': array(['Netherlands'], dtype=object),\n", " 'market_cap': array(['Large Cap', 'Mega Cap', 'Micro Cap', 'Mid Cap', 'Nano Cap',\n", "        'Small Cap'], dtype=object)}"]}, "execution_count": 4, "metadata": {}, "output_type": "execute_result"}], "source": ["equities.show_options(country=\"Netherlands\")"]}, {"cell_type": "markdown", "id": "160a9974", "metadata": {}, "source": ["Or only showing one specific parameter."]}, {"cell_type": "code", "execution_count": 5, "id": "e056a632", "metadata": {}, "outputs": [{"data": {"text/plain": ["array(['Banks', 'Capital Markets', 'Consumer Finance',\n", "       'Diversified Financial Services', 'Insurance'], dtype=object)"]}, "execution_count": 5, "metadata": {}, "output_type": "execute_result"}], "source": ["equities.show_options(\n", "    selection=\"industry\",\n", "    sector=\"Financials\",\n", "    country=\"Netherlands\")"]}, {"attachments": {}, "cell_type": "markdown", "id": "99032b0f", "metadata": {}, "source": ["Given this information, it then becomes possible to filter the database based on the parameters you are interested in. For example, if you are interested 'Insurance' companies in the 'Netherlands' you can use the following. Note that I ommit the `sector` here, given that the selection I make is on a deeper level and therefore it is a given that the sector is 'Financials'."]}, {"cell_type": "code", "execution_count": 6, "id": "735ec31b", "metadata": {}, "outputs": [{"data": {"text/html": ["<div>\n", "<style scoped>\n", "    .dataframe tbody tr th:only-of-type {\n", "        vertical-align: middle;\n", "    }\n", "\n", "    .dataframe tbody tr th {\n", "        vertical-align: top;\n", "    }\n", "\n", "    .dataframe thead th {\n", "        text-align: right;\n", "    }\n", "</style>\n", "<table border=\"1\" class=\"dataframe\">\n", "  <thead>\n", "    <tr style=\"text-align: right;\">\n", "      <th></th>\n", "      <th>name</th>\n", "      <th>summary</th>\n", "      <th>currency</th>\n", "      <th>sector</th>\n", "      <th>industry_group</th>\n", "      <th>industry</th>\n", "      <th>exchange</th>\n", "      <th>market</th>\n", "      <th>country</th>\n", "      <th>state</th>\n", "      <th>city</th>\n", "      <th>zipcode</th>\n", "      <th>website</th>\n", "      <th>market_cap</th>\n", "      <th>isin</th>\n", "      <th>cusip</th>\n", "      <th>figi</th>\n", "      <th>composite_figi</th>\n", "      <th>shareclass_figi</th>\n", "    </tr>\n", "    <tr>\n", "      <th>symbol</th>\n", "      <th></th>\n", "      <th></th>\n", "      <th></th>\n", "      <th></th>\n", "      <th></th>\n", "      <th></th>\n", "      <th></th>\n", "      <th></th>\n", "      <th></th>\n", "      <th></th>\n", "      <th></th>\n", "      <th></th>\n", "      <th></th>\n", "      <th></th>\n", "      <th></th>\n", "      <th></th>\n", "      <th></th>\n", "      <th></th>\n", "      <th></th>\n", "    </tr>\n", "  </thead>\n", "  <tbody>\n", "    <tr>\n", "      <th>A16.F</th>\n", "      <td>ASR Nederland N.V.</td>\n", "      <td>ASR Nederland N.V. provides various insurance ...</td>\n", "      <td>EUR</td>\n", "      <td>Financials</td>\n", "      <td>Insurance</td>\n", "      <td>Insurance</td>\n", "      <td>FRA</td>\n", "      <td>Frankfurt Stock Exchange</td>\n", "      <td>Netherlands</td>\n", "      <td>NaN</td>\n", "      <td>Utrecht</td>\n", "      <td>3584 BA</td>\n", "      <td>http://www.asrnl.com</td>\n", "      <td>Mid Cap</td>\n", "      <td>NL0011872643</td>\n", "      <td>NaN</td>\n", "      <td>BBG00D2VFV96</td>\n", "      <td>BBG00D2VFV78</td>\n", "      <td>BBG00CWZ0HK0</td>\n", "    </tr>\n", "    <tr>\n", "      <th>A1EG34.SA</th>\n", "      <td>Aegon N.V.</td>\n", "      <td>Aegon N.V. provides a range of financial servi...</td>\n", "      <td>BRL</td>\n", "      <td>Financials</td>\n", "      <td>Insurance</td>\n", "      <td>Insurance</td>\n", "      <td>SAO</td>\n", "      <td><PERSON><PERSON><PERSON></td>\n", "      <td>Netherlands</td>\n", "      <td>NaN</td>\n", "      <td>The Hague</td>\n", "      <td>2591 TV</td>\n", "      <td>http://www.aegon.com</td>\n", "      <td>Mid Cap</td>\n", "      <td>NL0000303709</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "    </tr>\n", "    <tr>\n", "      <th>AEG</th>\n", "      <td>Aegon N.V.</td>\n", "      <td>Aegon N.V. provides a range of financial servi...</td>\n", "      <td>USD</td>\n", "      <td>Financials</td>\n", "      <td>Insurance</td>\n", "      <td>Insurance</td>\n", "      <td>NYQ</td>\n", "      <td>New York Stock Exchange</td>\n", "      <td>Netherlands</td>\n", "      <td>NaN</td>\n", "      <td>The Hague</td>\n", "      <td>2591 TV</td>\n", "      <td>http://www.aegon.com</td>\n", "      <td>Large Cap</td>\n", "      <td>NL0000303709</td>\n", "      <td>NaN</td>\n", "      <td>BBG000CKQTN4</td>\n", "      <td>BBG000CKQSN6</td>\n", "      <td>BBG001S6Y6M8</td>\n", "    </tr>\n", "    <tr>\n", "      <th>AEGOF</th>\n", "      <td>Aegon N.V.</td>\n", "      <td>Aegon N.V. provides a range of financial servi...</td>\n", "      <td>USD</td>\n", "      <td>Financials</td>\n", "      <td>Insurance</td>\n", "      <td>Insurance</td>\n", "      <td>PNK</td>\n", "      <td>OTC Bulletin Board</td>\n", "      <td>Netherlands</td>\n", "      <td>NaN</td>\n", "      <td>The Hague</td>\n", "      <td>2591 TV</td>\n", "      <td>http://www.aegon.com</td>\n", "      <td>Mid Cap</td>\n", "      <td>NL0000303709</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "    </tr>\n", "    <tr>\n", "      <th>AEND.DE</th>\n", "      <td>Aegon N.V.</td>\n", "      <td>Aegon N.V. provides a range of financial servi...</td>\n", "      <td>EUR</td>\n", "      <td>Financials</td>\n", "      <td>Insurance</td>\n", "      <td>Insurance</td>\n", "      <td>GER</td>\n", "      <td>XETRA</td>\n", "      <td>Netherlands</td>\n", "      <td>NaN</td>\n", "      <td>The Hague</td>\n", "      <td>2591 TV</td>\n", "      <td>http://www.aegon.com</td>\n", "      <td>Mid Cap</td>\n", "      <td>NL0000303709</td>\n", "      <td>NaN</td>\n", "      <td>BBG000DJK260</td>\n", "      <td>BBG000DJHZF1</td>\n", "      <td>BBG001S5V8R4</td>\n", "    </tr>\n", "    <tr>\n", "      <th>AEND.F</th>\n", "      <td>Aegon N.V.</td>\n", "      <td>Aegon N.V. provides a range of financial servi...</td>\n", "      <td>EUR</td>\n", "      <td>Financials</td>\n", "      <td>Insurance</td>\n", "      <td>Insurance</td>\n", "      <td>FRA</td>\n", "      <td>Frankfurt Stock Exchange</td>\n", "      <td>Netherlands</td>\n", "      <td>NaN</td>\n", "      <td>The Hague</td>\n", "      <td>2591 TV</td>\n", "      <td>http://www.aegon.com</td>\n", "      <td>Mid Cap</td>\n", "      <td>NL0000303709</td>\n", "      <td>NaN</td>\n", "      <td>BBG000DJJ002</td>\n", "      <td>BBG000DJHZF1</td>\n", "      <td>BBG001S5V8R4</td>\n", "    </tr>\n", "    <tr>\n", "      <th>AEND.SG</th>\n", "      <td>Aegon N.V.</td>\n", "      <td>Aegon N.V. provides a range of financial servi...</td>\n", "      <td>EUR</td>\n", "      <td>Financials</td>\n", "      <td>Insurance</td>\n", "      <td>Insurance</td>\n", "      <td>STU</td>\n", "      <td>Stuttgart Stock Exchange</td>\n", "      <td>Netherlands</td>\n", "      <td>NaN</td>\n", "      <td>The Hague</td>\n", "      <td>2591 TV</td>\n", "      <td>http://www.aegon.com</td>\n", "      <td>Mid Cap</td>\n", "      <td>NL0000303709</td>\n", "      <td>NaN</td>\n", "      <td>BBG000DJK2M2</td>\n", "      <td>BBG000DJHZF1</td>\n", "      <td>BBG001S5V8R4</td>\n", "    </tr>\n", "    <tr>\n", "      <th>AENF.DE</th>\n", "      <td>Aegon N.V.</td>\n", "      <td>Aegon N.V. provides a range of financial servi...</td>\n", "      <td>EUR</td>\n", "      <td>Financials</td>\n", "      <td>Insurance</td>\n", "      <td>Insurance</td>\n", "      <td>GER</td>\n", "      <td>XETRA</td>\n", "      <td>Netherlands</td>\n", "      <td>NaN</td>\n", "      <td>The Hague</td>\n", "      <td>2591 TV</td>\n", "      <td>http://www.aegon.com</td>\n", "      <td>Mid Cap</td>\n", "      <td>NL0000303709</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "    </tr>\n", "    <tr>\n", "      <th>AENF.F</th>\n", "      <td>Aegon N.V.</td>\n", "      <td>Aegon N.V. provides a range of financial servi...</td>\n", "      <td>EUR</td>\n", "      <td>Financials</td>\n", "      <td>Insurance</td>\n", "      <td>Insurance</td>\n", "      <td>FRA</td>\n", "      <td>Frankfurt Stock Exchange</td>\n", "      <td>Netherlands</td>\n", "      <td>NaN</td>\n", "      <td>The Hague</td>\n", "      <td>2591 TV</td>\n", "      <td>http://www.aegon.com</td>\n", "      <td>Mid Cap</td>\n", "      <td>NL0000303709</td>\n", "      <td>NaN</td>\n", "      <td>BBG000PQ4D38</td>\n", "      <td>BBG000PQ4CB1</td>\n", "      <td>BBG001S6Y6M8</td>\n", "    </tr>\n", "    <tr>\n", "      <th>AENF.SG</th>\n", "      <td>Aegon N.V.</td>\n", "      <td>Aegon N.V. provides a range of financial servi...</td>\n", "      <td>EUR</td>\n", "      <td>Financials</td>\n", "      <td>Insurance</td>\n", "      <td>Insurance</td>\n", "      <td>STU</td>\n", "      <td>Stuttgart Stock Exchange</td>\n", "      <td>Netherlands</td>\n", "      <td>NaN</td>\n", "      <td>The Hague</td>\n", "      <td>2591 TV</td>\n", "      <td>http://www.aegon.com</td>\n", "      <td>Mid Cap</td>\n", "      <td>NL0000303709</td>\n", "      <td>NaN</td>\n", "      <td>BBG000PQ4FQ8</td>\n", "      <td>BBG000PQ4CB1</td>\n", "      <td>BBG001S6Y6M8</td>\n", "    </tr>\n", "    <tr>\n", "      <th>AGN.AS</th>\n", "      <td>Aegon N.V.</td>\n", "      <td>Aegon N.V. provides a range of financial servi...</td>\n", "      <td>EUR</td>\n", "      <td>Financials</td>\n", "      <td>Insurance</td>\n", "      <td>Insurance</td>\n", "      <td>AMS</td>\n", "      <td>Euronext Amsterdam</td>\n", "      <td>Netherlands</td>\n", "      <td>NaN</td>\n", "      <td>The Hague</td>\n", "      <td>2591 TV</td>\n", "      <td>http://www.aegon.com</td>\n", "      <td>Mid Cap</td>\n", "      <td>NL0000303709</td>\n", "      <td>NaN</td>\n", "      <td>BBG000JN9DM6</td>\n", "      <td>BBG000JN9C93</td>\n", "      <td>BBG001S5V8R4</td>\n", "    </tr>\n", "    <tr>\n", "      <th>AGN.MI</th>\n", "      <td>Aegon N.V.</td>\n", "      <td>Aegon N.V. provides a range of financial servi...</td>\n", "      <td>EUR</td>\n", "      <td>Financials</td>\n", "      <td>Insurance</td>\n", "      <td>Insurance</td>\n", "      <td>MIL</td>\n", "      <td>Borsa Italiana</td>\n", "      <td>Netherlands</td>\n", "      <td>NaN</td>\n", "      <td>The Hague</td>\n", "      <td>2591 TV</td>\n", "      <td>http://www.aegon.com</td>\n", "      <td>Mid Cap</td>\n", "      <td>NL0000303709</td>\n", "      <td>NaN</td>\n", "      <td>BBG000MJVT05</td>\n", "      <td>BBG000MJVSD3</td>\n", "      <td>BBG001S5V8R4</td>\n", "    </tr>\n", "    <tr>\n", "      <th>AGN.VI</th>\n", "      <td>Aegon N.V.</td>\n", "      <td>Aegon N.V. provides a range of financial servi...</td>\n", "      <td>EUR</td>\n", "      <td>Financials</td>\n", "      <td>Insurance</td>\n", "      <td>Insurance</td>\n", "      <td>VIE</td>\n", "      <td>Vienna Stock Exchange</td>\n", "      <td>Netherlands</td>\n", "      <td>NaN</td>\n", "      <td>The Hague</td>\n", "      <td>2591 TV</td>\n", "      <td>http://www.aegon.com</td>\n", "      <td>Mid Cap</td>\n", "      <td>NL0000303709</td>\n", "      <td>NaN</td>\n", "      <td>BBG00HVY3GH6</td>\n", "      <td>BBG00HVY3GG7</td>\n", "      <td>BBG001S5V8R4</td>\n", "    </tr>\n", "    <tr>\n", "      <th>ASRNL.AS</th>\n", "      <td>ASR Nederland N.V.</td>\n", "      <td>ASR Nederland N.V. provides various insurance ...</td>\n", "      <td>EUR</td>\n", "      <td>Financials</td>\n", "      <td>Insurance</td>\n", "      <td>Insurance</td>\n", "      <td>AMS</td>\n", "      <td>Euronext Amsterdam</td>\n", "      <td>Netherlands</td>\n", "      <td>NaN</td>\n", "      <td>Utrecht</td>\n", "      <td>3584 BA</td>\n", "      <td>http://www.asrnl.com</td>\n", "      <td>Mid Cap</td>\n", "      <td>NL0011872643</td>\n", "      <td>NaN</td>\n", "      <td>BBG00CWZ0HG5</td>\n", "      <td>BBG00CWZ0HF6</td>\n", "      <td>BBG00CWZ0HK0</td>\n", "    </tr>\n", "    <tr>\n", "      <th>NN.AS</th>\n", "      <td>NN Group N.V.</td>\n", "      <td>NN Group N.V., a financial services company, p...</td>\n", "      <td>EUR</td>\n", "      <td>Financials</td>\n", "      <td>Insurance</td>\n", "      <td>Insurance</td>\n", "      <td>AMS</td>\n", "      <td>Euronext Amsterdam</td>\n", "      <td>Netherlands</td>\n", "      <td>NaN</td>\n", "      <td>The Hague</td>\n", "      <td>2595 AS</td>\n", "      <td>http://www.nn-group.com</td>\n", "      <td>Large Cap</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "    </tr>\n", "    <tr>\n", "      <th>NN.VI</th>\n", "      <td>NN Group N.V.</td>\n", "      <td>NN Group N.V., a financial services company, p...</td>\n", "      <td>EUR</td>\n", "      <td>Financials</td>\n", "      <td>Insurance</td>\n", "      <td>Insurance</td>\n", "      <td>VIE</td>\n", "      <td>Vienna Stock Exchange</td>\n", "      <td>Netherlands</td>\n", "      <td>NaN</td>\n", "      <td>The Hague</td>\n", "      <td>2595 AS</td>\n", "      <td>http://www.nn-group.com</td>\n", "      <td>Large Cap</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "    </tr>\n", "    <tr>\n", "      <th>NNGPF</th>\n", "      <td>NN Group N.V.</td>\n", "      <td>NN Group N.V., a financial services company, p...</td>\n", "      <td>USD</td>\n", "      <td>Financials</td>\n", "      <td>Insurance</td>\n", "      <td>Insurance</td>\n", "      <td>PNK</td>\n", "      <td>OTC Bulletin Board</td>\n", "      <td>Netherlands</td>\n", "      <td>NaN</td>\n", "      <td>The Hague</td>\n", "      <td>2595 AS</td>\n", "      <td>http://www.nn-group.com</td>\n", "      <td>Large Cap</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "    </tr>\n", "    <tr>\n", "      <th>NNGRY</th>\n", "      <td>NN Group N.V.</td>\n", "      <td>NN Group N.V., a financial services company, p...</td>\n", "      <td>USD</td>\n", "      <td>Financials</td>\n", "      <td>Insurance</td>\n", "      <td>Insurance</td>\n", "      <td>PNK</td>\n", "      <td>OTC Bulletin Board</td>\n", "      <td>Netherlands</td>\n", "      <td>NaN</td>\n", "      <td>The Hague</td>\n", "      <td>2595 AS</td>\n", "      <td>http://www.nn-group.com</td>\n", "      <td>Large Cap</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "    </tr>\n", "  </tbody>\n", "</table>\n", "</div>"], "text/plain": ["                         name  \\\n", "symbol                          \n", "A16.F      ASR Nederland N.V.   \n", "A1EG34.SA          Aegon N.V.   \n", "AEG                Aegon N.V.   \n", "AEGOF              Aegon N.V.   \n", "AEND.DE            Aegon N.V.   \n", "AEND.F             Aegon N.V.   \n", "AEND.SG            Aegon N.V.   \n", "AENF.DE            Aegon N.V.   \n", "AENF.F             Aegon N.V.   \n", "AENF.SG            Aegon N.V.   \n", "AGN.AS             Aegon N.V.   \n", "AGN.MI             Aegon N.V.   \n", "AGN.VI             Aegon N.V.   \n", "ASRNL.AS   ASR Nederland N.V.   \n", "NN.AS           NN Group N.V.   \n", "NN.VI           NN Group N.V.   \n", "NNGPF           NN Group N.V.   \n", "NNGRY           NN Group N.V.   \n", "\n", "                                                     summary currency  \\\n", "symbol                                                                  \n", "A16.F      ASR Nederland N.V. provides various insurance ...      EUR   \n", "A1EG34.SA  Aegon N.V. provides a range of financial servi...      BRL   \n", "AEG        Aegon N.V. provides a range of financial servi...      USD   \n", "AEGOF      Aegon N.V. provides a range of financial servi...      USD   \n", "AEND.DE    Aegon N.V. provides a range of financial servi...      EUR   \n", "AEND.F     Aegon N.V. provides a range of financial servi...      EUR   \n", "AEND.SG    Aegon N.V. provides a range of financial servi...      EUR   \n", "AENF.DE    Aegon N.V. provides a range of financial servi...      EUR   \n", "AENF.F     Aegon N.V. provides a range of financial servi...      EUR   \n", "AENF.SG    Aegon N.V. provides a range of financial servi...      EUR   \n", "AGN.AS     Aegon N.V. provides a range of financial servi...      EUR   \n", "AGN.MI     Aegon N.V. provides a range of financial servi...      EUR   \n", "AGN.VI     Aegon N.V. provides a range of financial servi...      EUR   \n", "ASRNL.AS   ASR Nederland N.V. provides various insurance ...      EUR   \n", "NN.AS      NN Group N.V., a financial services company, p...      EUR   \n", "NN.VI      NN Group N.V., a financial services company, p...      EUR   \n", "NNGPF      NN Group N.V., a financial services company, p...      USD   \n", "NNGRY      NN Group N.V., a financial services company, p...      USD   \n", "\n", "               sector industry_group   industry exchange  \\\n", "symbol                                                     \n", "A16.F      Financials      Insurance  Insurance      FRA   \n", "A1EG34.SA  Financials      Insurance  Insurance      SAO   \n", "AEG        Financials      Insurance  Insurance      NYQ   \n", "AEGOF      Financials      Insurance  Insurance      PNK   \n", "AEND.DE    Financials      Insurance  Insurance      GER   \n", "AEND.F     Financials      Insurance  Insurance      FRA   \n", "AEND.SG    Financials      Insurance  Insurance      STU   \n", "AENF.DE    Financials      Insurance  Insurance      GER   \n", "AENF.F     Financials      Insurance  Insurance      FRA   \n", "AENF.SG    Financials      Insurance  Insurance      STU   \n", "AGN.AS     Financials      Insurance  Insurance      AMS   \n", "AGN.MI     Financials      Insurance  Insurance      MIL   \n", "AGN.VI     Financials      Insurance  Insurance      VIE   \n", "ASRNL.AS   Financials      Insurance  Insurance      AMS   \n", "NN.AS      Financials      Insurance  Insurance      AMS   \n", "NN.VI      Financials      Insurance  Insurance      VIE   \n", "NNGPF      Financials      Insurance  Insurance      PNK   \n", "NNGRY      Financials      Insurance  Insurance      PNK   \n", "\n", "                             market      country state       city  zipcode  \\\n", "symbol                                                                       \n", "A16.F      Frankfurt Stock Exchange  Netherlands   NaN    Utrecht  3584 BA   \n", "A1EG34.SA              Bovespa Soma  Netherlands   NaN  The Hague  2591 TV   \n", "AEG         New York Stock Exchange  Netherlands   NaN  The Hague  2591 TV   \n", "AEGOF            OTC Bulletin Board  Netherlands   NaN  The Hague  2591 TV   \n", "AEND.DE                       XETRA  Netherlands   NaN  The Hague  2591 TV   \n", "AEND.F     Frankfurt Stock Exchange  Netherlands   NaN  The Hague  2591 TV   \n", "AEND.SG    Stuttgart Stock Exchange  Netherlands   NaN  The Hague  2591 TV   \n", "AENF.DE                       XETRA  Netherlands   NaN  The Hague  2591 TV   \n", "AENF.F     Frankfurt Stock Exchange  Netherlands   NaN  The Hague  2591 TV   \n", "AENF.SG    Stuttgart Stock Exchange  Netherlands   NaN  The Hague  2591 TV   \n", "AGN.AS           Euronext Amsterdam  Netherlands   NaN  The Hague  2591 TV   \n", "AGN.MI               Borsa Italiana  Netherlands   NaN  The Hague  2591 TV   \n", "AGN.VI        Vienna Stock Exchange  Netherlands   NaN  The Hague  2591 TV   \n", "ASRNL.AS         Euronext Amsterdam  Netherlands   NaN    Utrecht  3584 BA   \n", "NN.AS            Euronext Amsterdam  Netherlands   NaN  The Hague  2595 AS   \n", "NN.VI         Vienna Stock Exchange  Netherlands   NaN  The Hague  2595 AS   \n", "NNGPF            OTC Bulletin Board  Netherlands   NaN  The Hague  2595 AS   \n", "NNGRY            OTC Bulletin Board  Netherlands   NaN  The Hague  2595 AS   \n", "\n", "                           website market_cap          isin cusip  \\\n", "symbol                                                              \n", "A16.F         http://www.asrnl.com    Mid Cap  NL0011872643   NaN   \n", "A1EG34.SA     http://www.aegon.com    Mid Cap  NL0000303709   NaN   \n", "AEG           http://www.aegon.com  Large Cap  NL0000303709   NaN   \n", "AEGOF         http://www.aegon.com    Mid Cap  NL0000303709   NaN   \n", "AEND.DE       http://www.aegon.com    Mid Cap  NL0000303709   NaN   \n", "AEND.F        http://www.aegon.com    Mid Cap  NL0000303709   NaN   \n", "AEND.SG       http://www.aegon.com    Mid Cap  NL0000303709   NaN   \n", "AENF.DE       http://www.aegon.com    Mid Cap  NL0000303709   NaN   \n", "AENF.F        http://www.aegon.com    Mid Cap  NL0000303709   NaN   \n", "AENF.SG       http://www.aegon.com    Mid Cap  NL0000303709   NaN   \n", "AGN.AS        http://www.aegon.com    Mid Cap  NL0000303709   NaN   \n", "AGN.MI        http://www.aegon.com    Mid Cap  NL0000303709   NaN   \n", "AGN.VI        http://www.aegon.com    Mid Cap  NL0000303709   NaN   \n", "ASRNL.AS      http://www.asrnl.com    Mid Cap  NL0011872643   NaN   \n", "NN.AS      http://www.nn-group.com  Large Cap           NaN   NaN   \n", "NN.VI      http://www.nn-group.com  Large Cap           NaN   NaN   \n", "NNGPF      http://www.nn-group.com  Large Cap           NaN   NaN   \n", "NNGRY      http://www.nn-group.com  Large Cap           NaN   NaN   \n", "\n", "                   figi composite_figi shareclass_figi  \n", "symbol                                                  \n", "A16.F      BBG00D2VFV96   BBG00D2VFV78    BBG00CWZ0HK0  \n", "A1EG34.SA           NaN            NaN             NaN  \n", "AEG        BBG000CKQTN4   BBG000CKQSN6    BBG001S6Y6M8  \n", "AEGOF               NaN            NaN             NaN  \n", "AEND.DE    BBG000DJK260   BBG000DJHZF1    BBG001S5V8R4  \n", "AEND.F     BBG000DJJ002   BBG000DJHZF1    BBG001S5V8R4  \n", "AEND.SG    BBG000DJK2M2   BBG000DJHZF1    BBG001S5V8R4  \n", "AENF.DE             NaN            NaN             NaN  \n", "AENF.F     BBG000PQ4D38   BBG000PQ4CB1    BBG001S6Y6M8  \n", "AENF.SG    BBG000PQ4FQ8   BBG000PQ4CB1    BBG001S6Y6M8  \n", "AGN.AS     BBG000JN9DM6   BBG000JN9C93    BBG001S5V8R4  \n", "AGN.MI     BBG000MJVT05   BBG000MJVSD3    BBG001S5V8R4  \n", "AGN.VI     BBG00HVY3GH6   BBG00HVY3GG7    BBG001S5V8R4  \n", "ASRNL.AS   BBG00CWZ0HG5   BBG00CWZ0HF6    BBG00CWZ0HK0  \n", "NN.AS               NaN            NaN             NaN  \n", "NN.VI               NaN            NaN             NaN  \n", "NNGPF               NaN            NaN             NaN  \n", "NNGRY               NaN            NaN             NaN  "]}, "execution_count": 6, "metadata": {}, "output_type": "execute_result"}], "source": ["equities.select(\n", "    country=\"Netherlands\",\n", "    industry=\"Insurance\",\n", ")"]}, {"attachments": {}, "cell_type": "markdown", "id": "6ae6fb1d", "metadata": {}, "source": ["You'll see that the same company can appear multiple times. This is because by default all exchanges are shown. There are two methods to focus on one entry:\n", "\n", "- Use the `only_primary_listing` parameter. This will only show the primary listing of each company. This is useful mostly if you are looking into the US exchanges.\n", "- Use the `exchange` or `market` parameter. This will allow you to filter on a specific exchange or market. This is useful when you not neccesarily looking into US exchanges and are already filtering on a specific country.\n", "\n", "For example, filtering on the Netherlands it makes sense to select a Dutch exchange as well. This is for example the exchange \"AMS\" or the market \"Euronext Amsterdam\". This will already give you a much smaller selection."]}, {"cell_type": "code", "execution_count": 7, "id": "d56a5e98", "metadata": {}, "outputs": [{"data": {"text/html": ["<div>\n", "<style scoped>\n", "    .dataframe tbody tr th:only-of-type {\n", "        vertical-align: middle;\n", "    }\n", "\n", "    .dataframe tbody tr th {\n", "        vertical-align: top;\n", "    }\n", "\n", "    .dataframe thead th {\n", "        text-align: right;\n", "    }\n", "</style>\n", "<table border=\"1\" class=\"dataframe\">\n", "  <thead>\n", "    <tr style=\"text-align: right;\">\n", "      <th></th>\n", "      <th>name</th>\n", "      <th>summary</th>\n", "      <th>currency</th>\n", "      <th>sector</th>\n", "      <th>industry_group</th>\n", "      <th>industry</th>\n", "      <th>exchange</th>\n", "      <th>market</th>\n", "      <th>country</th>\n", "      <th>state</th>\n", "      <th>city</th>\n", "      <th>zipcode</th>\n", "      <th>website</th>\n", "      <th>market_cap</th>\n", "      <th>isin</th>\n", "      <th>cusip</th>\n", "      <th>figi</th>\n", "      <th>composite_figi</th>\n", "      <th>shareclass_figi</th>\n", "    </tr>\n", "    <tr>\n", "      <th>symbol</th>\n", "      <th></th>\n", "      <th></th>\n", "      <th></th>\n", "      <th></th>\n", "      <th></th>\n", "      <th></th>\n", "      <th></th>\n", "      <th></th>\n", "      <th></th>\n", "      <th></th>\n", "      <th></th>\n", "      <th></th>\n", "      <th></th>\n", "      <th></th>\n", "      <th></th>\n", "      <th></th>\n", "      <th></th>\n", "      <th></th>\n", "      <th></th>\n", "    </tr>\n", "  </thead>\n", "  <tbody>\n", "    <tr>\n", "      <th>AGN.AS</th>\n", "      <td>Aegon N.V.</td>\n", "      <td>Aegon N.V. provides a range of financial servi...</td>\n", "      <td>EUR</td>\n", "      <td>Financials</td>\n", "      <td>Insurance</td>\n", "      <td>Insurance</td>\n", "      <td>AMS</td>\n", "      <td>Euronext Amsterdam</td>\n", "      <td>Netherlands</td>\n", "      <td>NaN</td>\n", "      <td>The Hague</td>\n", "      <td>2591 TV</td>\n", "      <td>http://www.aegon.com</td>\n", "      <td>Mid Cap</td>\n", "      <td>NL0000303709</td>\n", "      <td>NaN</td>\n", "      <td>BBG000JN9DM6</td>\n", "      <td>BBG000JN9C93</td>\n", "      <td>BBG001S5V8R4</td>\n", "    </tr>\n", "    <tr>\n", "      <th>ASRNL.AS</th>\n", "      <td>ASR Nederland N.V.</td>\n", "      <td>ASR Nederland N.V. provides various insurance ...</td>\n", "      <td>EUR</td>\n", "      <td>Financials</td>\n", "      <td>Insurance</td>\n", "      <td>Insurance</td>\n", "      <td>AMS</td>\n", "      <td>Euronext Amsterdam</td>\n", "      <td>Netherlands</td>\n", "      <td>NaN</td>\n", "      <td>Utrecht</td>\n", "      <td>3584 BA</td>\n", "      <td>http://www.asrnl.com</td>\n", "      <td>Mid Cap</td>\n", "      <td>NL0011872643</td>\n", "      <td>NaN</td>\n", "      <td>BBG00CWZ0HG5</td>\n", "      <td>BBG00CWZ0HF6</td>\n", "      <td>BBG00CWZ0HK0</td>\n", "    </tr>\n", "    <tr>\n", "      <th>NN.AS</th>\n", "      <td>NN Group N.V.</td>\n", "      <td>NN Group N.V., a financial services company, p...</td>\n", "      <td>EUR</td>\n", "      <td>Financials</td>\n", "      <td>Insurance</td>\n", "      <td>Insurance</td>\n", "      <td>AMS</td>\n", "      <td>Euronext Amsterdam</td>\n", "      <td>Netherlands</td>\n", "      <td>NaN</td>\n", "      <td>The Hague</td>\n", "      <td>2595 AS</td>\n", "      <td>http://www.nn-group.com</td>\n", "      <td>Large Cap</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "    </tr>\n", "  </tbody>\n", "</table>\n", "</div>"], "text/plain": ["                        name  \\\n", "symbol                         \n", "AGN.AS            Aegon N.V.   \n", "ASRNL.AS  ASR Nederland N.V.   \n", "NN.AS          NN Group N.V.   \n", "\n", "                                                    summary currency  \\\n", "symbol                                                                 \n", "AGN.AS    Aegon N.V. provides a range of financial servi...      EUR   \n", "ASRNL.AS  ASR Nederland N.V. provides various insurance ...      EUR   \n", "NN.AS     NN Group N.V., a financial services company, p...      EUR   \n", "\n", "              sector industry_group   industry exchange              market  \\\n", "symbol                                                                        \n", "AGN.AS    Financials      Insurance  Insurance      AMS  Euronext Amsterdam   \n", "ASRNL.AS  Financials      Insurance  Insurance      AMS  Euronext Amsterdam   \n", "NN.AS     Financials      Insurance  Insurance      AMS  Euronext Amsterdam   \n", "\n", "              country state       city  zipcode                  website  \\\n", "symbol                                                                     \n", "AGN.AS    Netherlands   NaN  The Hague  2591 TV     http://www.aegon.com   \n", "ASRNL.AS  Netherlands   NaN    Utrecht  3584 BA     http://www.asrnl.com   \n", "NN.AS     Netherlands   NaN  The Hague  2595 AS  http://www.nn-group.com   \n", "\n", "         market_cap          isin cusip          figi composite_figi  \\\n", "symbol                                                                 \n", "AGN.AS      Mid Cap  NL0000303709   NaN  BBG000JN9DM6   BBG000JN9C93   \n", "ASRNL.AS    Mid Cap  NL0011872643   NaN  BBG00CWZ0HG5   BBG00CWZ0HF6   \n", "NN.AS     Large Cap           NaN   NaN           NaN            NaN   \n", "\n", "         shareclass_figi  \n", "symbol                    \n", "AGN.AS      BBG001S5V8R4  \n", "ASRNL.AS    BBG00CWZ0HK0  \n", "NN.AS                NaN  "]}, "execution_count": 7, "metadata": {}, "output_type": "execute_result"}], "source": ["equities.select(\n", "    country=\"Netherlands\",\n", "    industry=\"Insurance\",\n", "    market=\"Euronext Amsterdam\",\n", ")"]}, {"cell_type": "markdown", "id": "76cd189f", "metadata": {}, "source": ["Given that the Netherlands is a relatively small country, it is not uncommon for the list to become small quick. For example, the same selection for the United States is already much larger, also utilizing the `only_primary_listing` parameter."]}, {"cell_type": "code", "execution_count": 8, "id": "0dec592b", "metadata": {}, "outputs": [{"data": {"text/html": ["<div>\n", "<style scoped>\n", "    .dataframe tbody tr th:only-of-type {\n", "        vertical-align: middle;\n", "    }\n", "\n", "    .dataframe tbody tr th {\n", "        vertical-align: top;\n", "    }\n", "\n", "    .dataframe thead th {\n", "        text-align: right;\n", "    }\n", "</style>\n", "<table border=\"1\" class=\"dataframe\">\n", "  <thead>\n", "    <tr style=\"text-align: right;\">\n", "      <th></th>\n", "      <th>name</th>\n", "      <th>summary</th>\n", "      <th>currency</th>\n", "      <th>sector</th>\n", "      <th>industry_group</th>\n", "      <th>industry</th>\n", "      <th>exchange</th>\n", "      <th>market</th>\n", "      <th>country</th>\n", "      <th>state</th>\n", "      <th>city</th>\n", "      <th>zipcode</th>\n", "      <th>website</th>\n", "      <th>market_cap</th>\n", "      <th>isin</th>\n", "      <th>cusip</th>\n", "      <th>figi</th>\n", "      <th>composite_figi</th>\n", "      <th>shareclass_figi</th>\n", "    </tr>\n", "    <tr>\n", "      <th>symbol</th>\n", "      <th></th>\n", "      <th></th>\n", "      <th></th>\n", "      <th></th>\n", "      <th></th>\n", "      <th></th>\n", "      <th></th>\n", "      <th></th>\n", "      <th></th>\n", "      <th></th>\n", "      <th></th>\n", "      <th></th>\n", "      <th></th>\n", "      <th></th>\n", "      <th></th>\n", "      <th></th>\n", "      <th></th>\n", "      <th></th>\n", "      <th></th>\n", "    </tr>\n", "  </thead>\n", "  <tbody>\n", "    <tr>\n", "      <th>AAME</th>\n", "      <td>Atlantic American Corporation</td>\n", "      <td>Atlantic American Corporation, through its sub...</td>\n", "      <td>USD</td>\n", "      <td>Financials</td>\n", "      <td>Insurance</td>\n", "      <td>Insurance</td>\n", "      <td>NGM</td>\n", "      <td>Nordic Growth Market</td>\n", "      <td>United States</td>\n", "      <td>GA</td>\n", "      <td>Atlanta</td>\n", "      <td>30319-3054</td>\n", "      <td>http://www.atlam.com</td>\n", "      <td>Nano Cap</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "    </tr>\n", "    <tr>\n", "      <th>ACMT</th>\n", "      <td>ACMAT Corporation</td>\n", "      <td>ACMAT Corporation, through its subsidiary, ACS...</td>\n", "      <td>USD</td>\n", "      <td>Financials</td>\n", "      <td>Insurance</td>\n", "      <td>Insurance</td>\n", "      <td>PNK</td>\n", "      <td>OTC Bulletin Board</td>\n", "      <td>United States</td>\n", "      <td>CT</td>\n", "      <td>Farmington</td>\n", "      <td>6032</td>\n", "      <td>http://www.acmatcorp.com</td>\n", "      <td>Nano Cap</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "    </tr>\n", "    <tr>\n", "      <th>ACMTA</th>\n", "      <td>ACMAT Corporation</td>\n", "      <td>ACMAT Corporation, through its subsidiary, ACS...</td>\n", "      <td>USD</td>\n", "      <td>Financials</td>\n", "      <td>Insurance</td>\n", "      <td>Insurance</td>\n", "      <td>PNK</td>\n", "      <td>OTC Bulletin Board</td>\n", "      <td>United States</td>\n", "      <td>CT</td>\n", "      <td>Farmington</td>\n", "      <td>6032</td>\n", "      <td>http://www.acmatcorp.com</td>\n", "      <td>Nano Cap</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "    </tr>\n", "    <tr>\n", "      <th>AEL</th>\n", "      <td>American Equity Investment Life Holding Company</td>\n", "      <td>American Equity Investment Life Holding Compan...</td>\n", "      <td>USD</td>\n", "      <td>Financials</td>\n", "      <td>Insurance</td>\n", "      <td>Insurance</td>\n", "      <td>NYQ</td>\n", "      <td>New York Stock Exchange</td>\n", "      <td>United States</td>\n", "      <td>IA</td>\n", "      <td>West Des Moines</td>\n", "      <td>50266</td>\n", "      <td>http://www.american-equity.com</td>\n", "      <td>Mid Cap</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "    </tr>\n", "    <tr>\n", "      <th>AEL-PA</th>\n", "      <td>American Equity Investment Life Holding Company</td>\n", "      <td>American Equity Investment Life Holding Compan...</td>\n", "      <td>USD</td>\n", "      <td>Financials</td>\n", "      <td>Insurance</td>\n", "      <td>Insurance</td>\n", "      <td>NYQ</td>\n", "      <td>New York Stock Exchange</td>\n", "      <td>United States</td>\n", "      <td>IA</td>\n", "      <td>West Des Moines</td>\n", "      <td>50266</td>\n", "      <td>http://www.american-equity.com</td>\n", "      <td>Mid Cap</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "    </tr>\n", "    <tr>\n", "      <th>...</th>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "    </tr>\n", "    <tr>\n", "      <th>VERY</th>\n", "      <td>Vericity, Inc.</td>\n", "      <td>Vericity, Inc., together with its subsidiaries...</td>\n", "      <td>USD</td>\n", "      <td>Financials</td>\n", "      <td>Insurance</td>\n", "      <td>Insurance</td>\n", "      <td>NCM</td>\n", "      <td>NASDAQ Capital Market</td>\n", "      <td>United States</td>\n", "      <td>IL</td>\n", "      <td>Chicago</td>\n", "      <td>60631</td>\n", "      <td>http://www.vericity.com</td>\n", "      <td>Micro Cap</td>\n", "      <td>US92347D1000</td>\n", "      <td>92347D100</td>\n", "      <td>BBG00PC36SH8</td>\n", "      <td>BBG00PC36S42</td>\n", "      <td>BBG00PC36SW1</td>\n", "    </tr>\n", "    <tr>\n", "      <th>WRB</th>\n", "      <td>W. R. Berkley Corporation</td>\n", "      <td>W. R. Berkley Corporation, an insurance holdin...</td>\n", "      <td>USD</td>\n", "      <td>Financials</td>\n", "      <td>Insurance</td>\n", "      <td>Insurance</td>\n", "      <td>NYQ</td>\n", "      <td>New York Stock Exchange</td>\n", "      <td>United States</td>\n", "      <td>CT</td>\n", "      <td>Greenwich</td>\n", "      <td>6830</td>\n", "      <td>http://www.berkley.com</td>\n", "      <td>Large Cap</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>BBG000BD1KV8</td>\n", "      <td>BBG000BD1HP2</td>\n", "      <td>BBG001S5P463</td>\n", "    </tr>\n", "    <tr>\n", "      <th>WRB-PD</th>\n", "      <td>W. R. Berkley Corporation</td>\n", "      <td>W. R. Berkley Corporation, an insurance holdin...</td>\n", "      <td>USD</td>\n", "      <td>Financials</td>\n", "      <td>Insurance</td>\n", "      <td>Insurance</td>\n", "      <td>NYQ</td>\n", "      <td>New York Stock Exchange</td>\n", "      <td>United States</td>\n", "      <td>CT</td>\n", "      <td>Greenwich</td>\n", "      <td>6830</td>\n", "      <td>http://www.berkley.com</td>\n", "      <td>Large Cap</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "    </tr>\n", "    <tr>\n", "      <th>WRB-PE</th>\n", "      <td>W. R. Berkley Corporation</td>\n", "      <td>W. R. Berkley Corporation, an insurance holdin...</td>\n", "      <td>USD</td>\n", "      <td>Financials</td>\n", "      <td>Insurance</td>\n", "      <td>Insurance</td>\n", "      <td>NYQ</td>\n", "      <td>New York Stock Exchange</td>\n", "      <td>United States</td>\n", "      <td>CT</td>\n", "      <td>Greenwich</td>\n", "      <td>6830</td>\n", "      <td>http://www.berkley.com</td>\n", "      <td>Large Cap</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "    </tr>\n", "    <tr>\n", "      <th>Y</th>\n", "      <td>Alleghany Corporation</td>\n", "      <td>Alleghany Corporation provides property and ca...</td>\n", "      <td>USD</td>\n", "      <td>Financials</td>\n", "      <td>Insurance</td>\n", "      <td>Insurance</td>\n", "      <td>NYQ</td>\n", "      <td>New York Stock Exchange</td>\n", "      <td>United States</td>\n", "      <td>NY</td>\n", "      <td>New York</td>\n", "      <td>10018</td>\n", "      <td>http://www.alleghany.com</td>\n", "      <td>Large Cap</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "    </tr>\n", "  </tbody>\n", "</table>\n", "<p>168 rows × 19 columns</p>\n", "</div>"], "text/plain": ["                                                   name  \\\n", "symbol                                                    \n", "AAME                      Atlantic American Corporation   \n", "ACMT                                  ACMAT Corporation   \n", "ACMTA                                 ACMAT Corporation   \n", "AEL     American Equity Investment Life Holding Company   \n", "AEL-PA  American Equity Investment Life Holding Company   \n", "...                                                 ...   \n", "VERY                                     Vericity, Inc.   \n", "WRB                           W. R. Berkley Corporation   \n", "WRB-PD                        W. R. Berkley Corporation   \n", "WRB-PE                        W. R. Berkley Corporation   \n", "Y                                 Alleghany Corporation   \n", "\n", "                                                  summary currency  \\\n", "symbol                                                               \n", "AAME    Atlantic American Corporation, through its sub...      USD   \n", "ACMT    ACMAT Corporation, through its subsidiary, ACS...      USD   \n", "ACMTA   ACMAT Corporation, through its subsidiary, ACS...      USD   \n", "AEL     American Equity Investment Life Holding Compan...      USD   \n", "AEL-PA  American Equity Investment Life Holding Compan...      USD   \n", "...                                                   ...      ...   \n", "VERY    Vericity, Inc., together with its subsidiaries...      USD   \n", "WRB     W. R. Berkley Corporation, an insurance holdin...      USD   \n", "WRB-PD  W. R. Berkley Corporation, an insurance holdin...      USD   \n", "WRB-PE  W. R. Berkley Corporation, an insurance holdin...      USD   \n", "Y       Alleghany Corporation provides property and ca...      USD   \n", "\n", "            sector industry_group   industry exchange  \\\n", "symbol                                                  \n", "AAME    Financials      Insurance  Insurance      NGM   \n", "ACMT    Financials      Insurance  Insurance      PNK   \n", "ACMTA   Financials      Insurance  Insurance      PNK   \n", "AEL     Financials      Insurance  Insurance      NYQ   \n", "AEL-PA  Financials      Insurance  Insurance      NYQ   \n", "...            ...            ...        ...      ...   \n", "VERY    Financials      Insurance  Insurance      NCM   \n", "WRB     Financials      Insurance  Insurance      NYQ   \n", "WRB-PD  Financials      Insurance  Insurance      NYQ   \n", "WRB-PE  Financials      Insurance  Insurance      NYQ   \n", "Y       Financials      Insurance  Insurance      NYQ   \n", "\n", "                         market        country state             city  \\\n", "symbol                                                                  \n", "AAME       Nordic Growth Market  United States    GA          Atlanta   \n", "ACMT         OTC Bulletin Board  United States    CT       Farmington   \n", "ACMTA        OTC Bulletin Board  United States    CT       Farmington   \n", "AEL     New York Stock Exchange  United States    IA  West Des Moines   \n", "AEL-PA  New York Stock Exchange  United States    IA  West Des Moines   \n", "...                         ...            ...   ...              ...   \n", "VERY      NASDAQ Capital Market  United States    IL          Chicago   \n", "WRB     New York Stock Exchange  United States    CT        Greenwich   \n", "WRB-PD  New York Stock Exchange  United States    CT        Greenwich   \n", "WRB-PE  New York Stock Exchange  United States    CT        Greenwich   \n", "Y       New York Stock Exchange  United States    NY         New York   \n", "\n", "           zipcode                         website market_cap          isin  \\\n", "symbol                                                                        \n", "AAME    30319-3054            http://www.atlam.com   Nano Cap           NaN   \n", "ACMT          6032        http://www.acmatcorp.com   Nano Cap           NaN   \n", "ACMTA         6032        http://www.acmatcorp.com   Nano Cap           NaN   \n", "AEL          50266  http://www.american-equity.com    Mid Cap           NaN   \n", "AEL-PA       50266  http://www.american-equity.com    Mid Cap           NaN   \n", "...            ...                             ...        ...           ...   \n", "VERY         60631         http://www.vericity.com  Micro Cap  US92347D1000   \n", "WRB           6830          http://www.berkley.com  Large Cap           NaN   \n", "WRB-PD        6830          http://www.berkley.com  Large Cap           NaN   \n", "WRB-PE        6830          http://www.berkley.com  Large Cap           NaN   \n", "Y            10018        http://www.alleghany.com  Large Cap           NaN   \n", "\n", "            cusip          figi composite_figi shareclass_figi  \n", "symbol                                                          \n", "AAME          NaN           NaN            NaN             NaN  \n", "ACMT          NaN           NaN            NaN             NaN  \n", "ACMTA         NaN           NaN            NaN             NaN  \n", "AEL           NaN           NaN            NaN             NaN  \n", "AEL-PA        NaN           NaN            NaN             NaN  \n", "...           ...           ...            ...             ...  \n", "VERY    92347D100  BBG00PC36SH8   BBG00PC36S42    BBG00PC36SW1  \n", "WRB           NaN  BBG000BD1KV8   BBG000BD1HP2    BBG001S5P463  \n", "WRB-PD        NaN           NaN            NaN             NaN  \n", "WRB-PE        NaN           NaN            NaN             NaN  \n", "Y             NaN           NaN            NaN             NaN  \n", "\n", "[168 rows x 19 columns]"]}, "execution_count": 8, "metadata": {}, "output_type": "execute_result"}], "source": ["equities.select(\n", "    country=\"United States\",\n", "    industry=\"Insurance\",\n", "    only_primary_listing=True\n", ")"]}, {"cell_type": "markdown", "id": "2a7ff6d1", "metadata": {}, "source": ["For any of the variables, it is also possible to provide a list instead. Which means that it will return all entries that match any of the variables. As an example, the queries above can be combined into one."]}, {"cell_type": "code", "execution_count": 9, "id": "7dc89d18", "metadata": {}, "outputs": [{"data": {"text/html": ["<div>\n", "<style scoped>\n", "    .dataframe tbody tr th:only-of-type {\n", "        vertical-align: middle;\n", "    }\n", "\n", "    .dataframe tbody tr th {\n", "        vertical-align: top;\n", "    }\n", "\n", "    .dataframe thead th {\n", "        text-align: right;\n", "    }\n", "</style>\n", "<table border=\"1\" class=\"dataframe\">\n", "  <thead>\n", "    <tr style=\"text-align: right;\">\n", "      <th></th>\n", "      <th>name</th>\n", "      <th>summary</th>\n", "      <th>currency</th>\n", "      <th>sector</th>\n", "      <th>industry_group</th>\n", "      <th>industry</th>\n", "      <th>exchange</th>\n", "      <th>market</th>\n", "      <th>country</th>\n", "      <th>state</th>\n", "      <th>city</th>\n", "      <th>zipcode</th>\n", "      <th>website</th>\n", "      <th>market_cap</th>\n", "      <th>isin</th>\n", "      <th>cusip</th>\n", "      <th>figi</th>\n", "      <th>composite_figi</th>\n", "      <th>shareclass_figi</th>\n", "    </tr>\n", "    <tr>\n", "      <th>symbol</th>\n", "      <th></th>\n", "      <th></th>\n", "      <th></th>\n", "      <th></th>\n", "      <th></th>\n", "      <th></th>\n", "      <th></th>\n", "      <th></th>\n", "      <th></th>\n", "      <th></th>\n", "      <th></th>\n", "      <th></th>\n", "      <th></th>\n", "      <th></th>\n", "      <th></th>\n", "      <th></th>\n", "      <th></th>\n", "      <th></th>\n", "      <th></th>\n", "    </tr>\n", "  </thead>\n", "  <tbody>\n", "    <tr>\n", "      <th>AAME</th>\n", "      <td>Atlantic American Corporation</td>\n", "      <td>Atlantic American Corporation, through its sub...</td>\n", "      <td>USD</td>\n", "      <td>Financials</td>\n", "      <td>Insurance</td>\n", "      <td>Insurance</td>\n", "      <td>NGM</td>\n", "      <td>Nordic Growth Market</td>\n", "      <td>United States</td>\n", "      <td>GA</td>\n", "      <td>Atlanta</td>\n", "      <td>30319-3054</td>\n", "      <td>http://www.atlam.com</td>\n", "      <td>Nano Cap</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "    </tr>\n", "    <tr>\n", "      <th>ACMT</th>\n", "      <td>ACMAT Corporation</td>\n", "      <td>ACMAT Corporation, through its subsidiary, ACS...</td>\n", "      <td>USD</td>\n", "      <td>Financials</td>\n", "      <td>Insurance</td>\n", "      <td>Insurance</td>\n", "      <td>PNK</td>\n", "      <td>OTC Bulletin Board</td>\n", "      <td>United States</td>\n", "      <td>CT</td>\n", "      <td>Farmington</td>\n", "      <td>6032</td>\n", "      <td>http://www.acmatcorp.com</td>\n", "      <td>Nano Cap</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "    </tr>\n", "    <tr>\n", "      <th>ACMTA</th>\n", "      <td>ACMAT Corporation</td>\n", "      <td>ACMAT Corporation, through its subsidiary, ACS...</td>\n", "      <td>USD</td>\n", "      <td>Financials</td>\n", "      <td>Insurance</td>\n", "      <td>Insurance</td>\n", "      <td>PNK</td>\n", "      <td>OTC Bulletin Board</td>\n", "      <td>United States</td>\n", "      <td>CT</td>\n", "      <td>Farmington</td>\n", "      <td>6032</td>\n", "      <td>http://www.acmatcorp.com</td>\n", "      <td>Nano Cap</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "    </tr>\n", "    <tr>\n", "      <th>AEG</th>\n", "      <td>Aegon N.V.</td>\n", "      <td>Aegon N.V. provides a range of financial servi...</td>\n", "      <td>USD</td>\n", "      <td>Financials</td>\n", "      <td>Insurance</td>\n", "      <td>Insurance</td>\n", "      <td>NYQ</td>\n", "      <td>New York Stock Exchange</td>\n", "      <td>Netherlands</td>\n", "      <td>NaN</td>\n", "      <td>The Hague</td>\n", "      <td>2591 TV</td>\n", "      <td>http://www.aegon.com</td>\n", "      <td>Large Cap</td>\n", "      <td>NL0000303709</td>\n", "      <td>NaN</td>\n", "      <td>BBG000CKQTN4</td>\n", "      <td>BBG000CKQSN6</td>\n", "      <td>BBG001S6Y6M8</td>\n", "    </tr>\n", "    <tr>\n", "      <th>AEGOF</th>\n", "      <td>Aegon N.V.</td>\n", "      <td>Aegon N.V. provides a range of financial servi...</td>\n", "      <td>USD</td>\n", "      <td>Financials</td>\n", "      <td>Insurance</td>\n", "      <td>Insurance</td>\n", "      <td>PNK</td>\n", "      <td>OTC Bulletin Board</td>\n", "      <td>Netherlands</td>\n", "      <td>NaN</td>\n", "      <td>The Hague</td>\n", "      <td>2591 TV</td>\n", "      <td>http://www.aegon.com</td>\n", "      <td>Mid Cap</td>\n", "      <td>NL0000303709</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "    </tr>\n", "    <tr>\n", "      <th>...</th>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "    </tr>\n", "    <tr>\n", "      <th>VERY</th>\n", "      <td>Vericity, Inc.</td>\n", "      <td>Vericity, Inc., together with its subsidiaries...</td>\n", "      <td>USD</td>\n", "      <td>Financials</td>\n", "      <td>Insurance</td>\n", "      <td>Insurance</td>\n", "      <td>NCM</td>\n", "      <td>NASDAQ Capital Market</td>\n", "      <td>United States</td>\n", "      <td>IL</td>\n", "      <td>Chicago</td>\n", "      <td>60631</td>\n", "      <td>http://www.vericity.com</td>\n", "      <td>Micro Cap</td>\n", "      <td>US92347D1000</td>\n", "      <td>92347D100</td>\n", "      <td>BBG00PC36SH8</td>\n", "      <td>BBG00PC36S42</td>\n", "      <td>BBG00PC36SW1</td>\n", "    </tr>\n", "    <tr>\n", "      <th>WRB</th>\n", "      <td>W. R. Berkley Corporation</td>\n", "      <td>W. R. Berkley Corporation, an insurance holdin...</td>\n", "      <td>USD</td>\n", "      <td>Financials</td>\n", "      <td>Insurance</td>\n", "      <td>Insurance</td>\n", "      <td>NYQ</td>\n", "      <td>New York Stock Exchange</td>\n", "      <td>United States</td>\n", "      <td>CT</td>\n", "      <td>Greenwich</td>\n", "      <td>6830</td>\n", "      <td>http://www.berkley.com</td>\n", "      <td>Large Cap</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>BBG000BD1KV8</td>\n", "      <td>BBG000BD1HP2</td>\n", "      <td>BBG001S5P463</td>\n", "    </tr>\n", "    <tr>\n", "      <th>WRB-PD</th>\n", "      <td>W. R. Berkley Corporation</td>\n", "      <td>W. R. Berkley Corporation, an insurance holdin...</td>\n", "      <td>USD</td>\n", "      <td>Financials</td>\n", "      <td>Insurance</td>\n", "      <td>Insurance</td>\n", "      <td>NYQ</td>\n", "      <td>New York Stock Exchange</td>\n", "      <td>United States</td>\n", "      <td>CT</td>\n", "      <td>Greenwich</td>\n", "      <td>6830</td>\n", "      <td>http://www.berkley.com</td>\n", "      <td>Large Cap</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "    </tr>\n", "    <tr>\n", "      <th>WRB-PE</th>\n", "      <td>W. R. Berkley Corporation</td>\n", "      <td>W. R. Berkley Corporation, an insurance holdin...</td>\n", "      <td>USD</td>\n", "      <td>Financials</td>\n", "      <td>Insurance</td>\n", "      <td>Insurance</td>\n", "      <td>NYQ</td>\n", "      <td>New York Stock Exchange</td>\n", "      <td>United States</td>\n", "      <td>CT</td>\n", "      <td>Greenwich</td>\n", "      <td>6830</td>\n", "      <td>http://www.berkley.com</td>\n", "      <td>Large Cap</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "    </tr>\n", "    <tr>\n", "      <th>Y</th>\n", "      <td>Alleghany Corporation</td>\n", "      <td>Alleghany Corporation provides property and ca...</td>\n", "      <td>USD</td>\n", "      <td>Financials</td>\n", "      <td>Insurance</td>\n", "      <td>Insurance</td>\n", "      <td>NYQ</td>\n", "      <td>New York Stock Exchange</td>\n", "      <td>United States</td>\n", "      <td>NY</td>\n", "      <td>New York</td>\n", "      <td>10018</td>\n", "      <td>http://www.alleghany.com</td>\n", "      <td>Large Cap</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "    </tr>\n", "  </tbody>\n", "</table>\n", "<p>175 rows × 19 columns</p>\n", "</div>"], "text/plain": ["                                 name  \\\n", "symbol                                  \n", "AAME    Atlantic American Corporation   \n", "ACMT                ACMAT Corporation   \n", "ACMTA               ACMAT Corporation   \n", "AEG                        Aegon N.V.   \n", "AEGOF                      Aegon N.V.   \n", "...                               ...   \n", "VERY                   Vericity, Inc.   \n", "WRB         W. R. Berkley Corporation   \n", "WRB-PD      W. R. Berkley Corporation   \n", "WRB-PE      W. R. Berkley Corporation   \n", "Y               Alleghany Corporation   \n", "\n", "                                                  summary currency  \\\n", "symbol                                                               \n", "AAME    Atlantic American Corporation, through its sub...      USD   \n", "ACMT    ACMAT Corporation, through its subsidiary, ACS...      USD   \n", "ACMTA   ACMAT Corporation, through its subsidiary, ACS...      USD   \n", "AEG     Aegon N.V. provides a range of financial servi...      USD   \n", "AEGOF   Aegon N.V. provides a range of financial servi...      USD   \n", "...                                                   ...      ...   \n", "VERY    Vericity, Inc., together with its subsidiaries...      USD   \n", "WRB     W. R. Berkley Corporation, an insurance holdin...      USD   \n", "WRB-PD  W. R. Berkley Corporation, an insurance holdin...      USD   \n", "WRB-PE  W. R. Berkley Corporation, an insurance holdin...      USD   \n", "Y       Alleghany Corporation provides property and ca...      USD   \n", "\n", "            sector industry_group   industry exchange  \\\n", "symbol                                                  \n", "AAME    Financials      Insurance  Insurance      NGM   \n", "ACMT    Financials      Insurance  Insurance      PNK   \n", "ACMTA   Financials      Insurance  Insurance      PNK   \n", "AEG     Financials      Insurance  Insurance      NYQ   \n", "AEGOF   Financials      Insurance  Insurance      PNK   \n", "...            ...            ...        ...      ...   \n", "VERY    Financials      Insurance  Insurance      NCM   \n", "WRB     Financials      Insurance  Insurance      NYQ   \n", "WRB-PD  Financials      Insurance  Insurance      NYQ   \n", "WRB-PE  Financials      Insurance  Insurance      NYQ   \n", "Y       Financials      Insurance  Insurance      NYQ   \n", "\n", "                         market        country state        city     zipcode  \\\n", "symbol                                                                         \n", "AAME       Nordic Growth Market  United States    GA     Atlanta  30319-3054   \n", "ACMT         OTC Bulletin Board  United States    CT  Farmington        6032   \n", "ACMTA        OTC Bulletin Board  United States    CT  Farmington        6032   \n", "AEG     New York Stock Exchange    Netherlands   NaN   The Hague     2591 TV   \n", "AEGOF        OTC Bulletin Board    Netherlands   NaN   The Hague     2591 TV   \n", "...                         ...            ...   ...         ...         ...   \n", "VERY      NASDAQ Capital Market  United States    IL     Chicago       60631   \n", "WRB     New York Stock Exchange  United States    CT   Greenwich        6830   \n", "WRB-PD  New York Stock Exchange  United States    CT   Greenwich        6830   \n", "WRB-PE  New York Stock Exchange  United States    CT   Greenwich        6830   \n", "Y       New York Stock Exchange  United States    NY    New York       10018   \n", "\n", "                         website market_cap          isin      cusip  \\\n", "symbol                                                                 \n", "AAME        http://www.atlam.com   Nano Cap           NaN        NaN   \n", "ACMT    http://www.acmatcorp.com   Nano Cap           NaN        NaN   \n", "ACMTA   http://www.acmatcorp.com   Nano Cap           NaN        NaN   \n", "AEG         http://www.aegon.com  Large Cap  NL0000303709        NaN   \n", "AEGOF       http://www.aegon.com    Mid Cap  NL0000303709        NaN   \n", "...                          ...        ...           ...        ...   \n", "VERY     http://www.vericity.com  Micro Cap  US92347D1000  92347D100   \n", "WRB       http://www.berkley.com  Large Cap           NaN        NaN   \n", "WRB-PD    http://www.berkley.com  Large Cap           NaN        NaN   \n", "WRB-PE    http://www.berkley.com  Large Cap           NaN        NaN   \n", "Y       http://www.alleghany.com  Large Cap           NaN        NaN   \n", "\n", "                figi composite_figi shareclass_figi  \n", "symbol                                               \n", "AAME             NaN            NaN             NaN  \n", "ACMT             NaN            NaN             NaN  \n", "ACMTA            NaN            NaN             NaN  \n", "AEG     BBG000CKQTN4   BBG000CKQSN6    BBG001S6Y6M8  \n", "AEGOF            NaN            NaN             NaN  \n", "...              ...            ...             ...  \n", "VERY    BBG00PC36SH8   BBG00PC36S42    BBG00PC36SW1  \n", "WRB     BBG000BD1KV8   BBG000BD1HP2    BBG001S5P463  \n", "WRB-PD           NaN            NaN             NaN  \n", "WRB-PE           NaN            NaN             NaN  \n", "Y                NaN            NaN             NaN  \n", "\n", "[175 rows x 19 columns]"]}, "execution_count": 9, "metadata": {}, "output_type": "execute_result"}], "source": ["equities.select(\n", "    country=[\"Netherlands\", \"United States\"],\n", "    industry=\"Insurance\",\n", "    market=[\"Euronext Amsterdam\", \"Nordic Growth Market\", \"OTC Bulletin Board\",\n", "            \"New York Stock Exchange\", \"NASDAQ Global Select\", \"NYSE MKT\",\n", "            \"NASDAQ Capital Market\"]\n", ")"]}, {"cell_type": "markdown", "id": "e7bbf39b", "metadata": {}, "source": ["In case the current categorization doesn't lead to the results you are looking for, it is possible to use the `search` parameter. This allows you to filter on any column in the database via a custom string. This means that if the word or sentence you input is found somewhere in the column you select, it will return the result. \n", "\n", "By default the result will not be case sensitive but you can adjust this by setting `case_sensitive=True`. You can also filter the index (`symbol` column) by using `index` as shown below. Just like the `select` function, you can also provide lists here."]}, {"cell_type": "code", "execution_count": 10, "id": "d9b107df", "metadata": {}, "outputs": [{"data": {"text/html": ["<div>\n", "<style scoped>\n", "    .dataframe tbody tr th:only-of-type {\n", "        vertical-align: middle;\n", "    }\n", "\n", "    .dataframe tbody tr th {\n", "        vertical-align: top;\n", "    }\n", "\n", "    .dataframe thead th {\n", "        text-align: right;\n", "    }\n", "</style>\n", "<table border=\"1\" class=\"dataframe\">\n", "  <thead>\n", "    <tr style=\"text-align: right;\">\n", "      <th></th>\n", "      <th>name</th>\n", "      <th>summary</th>\n", "      <th>currency</th>\n", "      <th>sector</th>\n", "      <th>industry_group</th>\n", "      <th>industry</th>\n", "      <th>exchange</th>\n", "      <th>market</th>\n", "      <th>country</th>\n", "      <th>state</th>\n", "      <th>city</th>\n", "      <th>zipcode</th>\n", "      <th>website</th>\n", "      <th>market_cap</th>\n", "      <th>isin</th>\n", "      <th>cusip</th>\n", "      <th>figi</th>\n", "      <th>composite_figi</th>\n", "      <th>shareclass_figi</th>\n", "    </tr>\n", "    <tr>\n", "      <th>symbol</th>\n", "      <th></th>\n", "      <th></th>\n", "      <th></th>\n", "      <th></th>\n", "      <th></th>\n", "      <th></th>\n", "      <th></th>\n", "      <th></th>\n", "      <th></th>\n", "      <th></th>\n", "      <th></th>\n", "      <th></th>\n", "      <th></th>\n", "      <th></th>\n", "      <th></th>\n", "      <th></th>\n", "      <th></th>\n", "      <th></th>\n", "      <th></th>\n", "    </tr>\n", "  </thead>\n", "  <tbody>\n", "    <tr>\n", "      <th>109.F</th>\n", "      <td>Castlight Health, Inc.</td>\n", "      <td>Castlight Health, Inc. provides health navigat...</td>\n", "      <td>EUR</td>\n", "      <td>Health Care</td>\n", "      <td>Health Care Equipment &amp; Services</td>\n", "      <td>Health Care Providers &amp; Services</td>\n", "      <td>FRA</td>\n", "      <td>Frankfurt Stock Exchange</td>\n", "      <td>United States</td>\n", "      <td>CA</td>\n", "      <td>San Francisco</td>\n", "      <td>94105</td>\n", "      <td>http://www.castlighthealth.com</td>\n", "      <td>Small Cap</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "    </tr>\n", "    <tr>\n", "      <th>1KT.F</th>\n", "      <td>Keysight Technologies, Inc.</td>\n", "      <td>Keysight Technologies, Inc. provides electroni...</td>\n", "      <td>EUR</td>\n", "      <td>Information Technology</td>\n", "      <td>Technology Hardware &amp; Equipment</td>\n", "      <td>Electronic Equipment, Instruments &amp; Components</td>\n", "      <td>FRA</td>\n", "      <td>Frankfurt Stock Exchange</td>\n", "      <td>United States</td>\n", "      <td>CA</td>\n", "      <td><PERSON></td>\n", "      <td>95403-1738</td>\n", "      <td>http://www.keysight.com</td>\n", "      <td>Large Cap</td>\n", "      <td>US49338L1035</td>\n", "      <td>49338L103</td>\n", "      <td>BBG007DJZFD2</td>\n", "      <td>BBG007DJZFC3</td>\n", "      <td>BBG0059FN820</td>\n", "    </tr>\n", "    <tr>\n", "      <th>1N1.F</th>\n", "      <td>Nanalysis Scientific Corp.</td>\n", "      <td>Nanalysis Scientific Corp., develops, manufact...</td>\n", "      <td>EUR</td>\n", "      <td>Information Technology</td>\n", "      <td>Technology Hardware &amp; Equipment</td>\n", "      <td>Electronic Equipment, Instruments &amp; Components</td>\n", "      <td>FRA</td>\n", "      <td>Frankfurt Stock Exchange</td>\n", "      <td>Canada</td>\n", "      <td>AB</td>\n", "      <td>Calgary</td>\n", "      <td>T2E 7C3</td>\n", "      <td>http://www.nanalysis.com</td>\n", "      <td>Nano Cap</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "    </tr>\n", "    <tr>\n", "      <th>1YO.F</th>\n", "      <td>Yangtze Optical Fibre And Cable Joint Stock Li...</td>\n", "      <td>Yangtze Optical Fibre And Cable Joint Stock Li...</td>\n", "      <td>EUR</td>\n", "      <td>Information Technology</td>\n", "      <td>Technology Hardware &amp; Equipment</td>\n", "      <td>Communications Equipment</td>\n", "      <td>FRA</td>\n", "      <td>Frankfurt Stock Exchange</td>\n", "      <td>China</td>\n", "      <td>NaN</td>\n", "      <td><PERSON><PERSON></td>\n", "      <td>430073</td>\n", "      <td>http://www.yofc.com</td>\n", "      <td>Small Cap</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "    </tr>\n", "    <tr>\n", "      <th>1ZU.F</th>\n", "      <td>The Pennant Group, Inc.</td>\n", "      <td>The Pennant Group, Inc. provides healthcare se...</td>\n", "      <td>EUR</td>\n", "      <td>Health Care</td>\n", "      <td>Health Care Equipment &amp; Services</td>\n", "      <td>Health Care Equipment &amp; Supplies</td>\n", "      <td>FRA</td>\n", "      <td>Frankfurt Stock Exchange</td>\n", "      <td>United States</td>\n", "      <td>ID</td>\n", "      <td>Eagle</td>\n", "      <td>83616</td>\n", "      <td>http://pennantgroup.com</td>\n", "      <td>Small Cap</td>\n", "      <td>US70805E1091</td>\n", "      <td>7.08E+113</td>\n", "      <td>BBG00QJ35K78</td>\n", "      <td>BBG00QJ35K69</td>\n", "      <td>BBG00P33SZ15</td>\n", "    </tr>\n", "    <tr>\n", "      <th>...</th>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "    </tr>\n", "    <tr>\n", "      <th>V00.F</th>\n", "      <td>Vocera Communications, Inc.</td>\n", "      <td>Vocera Communications, Inc. provides secure, i...</td>\n", "      <td>EUR</td>\n", "      <td>Information Technology</td>\n", "      <td>Technology Hardware &amp; Equipment</td>\n", "      <td>Communications Equipment</td>\n", "      <td>FRA</td>\n", "      <td>Frankfurt Stock Exchange</td>\n", "      <td>United States</td>\n", "      <td>CA</td>\n", "      <td>San Jose</td>\n", "      <td>95126</td>\n", "      <td>http://www.vocera.com</td>\n", "      <td>Small Cap</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "    </tr>\n", "    <tr>\n", "      <th>VNI.F</th>\n", "      <td>Avnet, Inc.</td>\n", "      <td>Avnet, Inc., a technology solutions company, m...</td>\n", "      <td>EUR</td>\n", "      <td>Information Technology</td>\n", "      <td>Technology Hardware &amp; Equipment</td>\n", "      <td>Electronic Equipment, Instruments &amp; Components</td>\n", "      <td>FRA</td>\n", "      <td>Frankfurt Stock Exchange</td>\n", "      <td>United States</td>\n", "      <td>AZ</td>\n", "      <td>Phoenix</td>\n", "      <td>85034</td>\n", "      <td>http://www.avnet.com</td>\n", "      <td>Mid Cap</td>\n", "      <td>US0538071038</td>\n", "      <td>53807103</td>\n", "      <td>BBG000G99V31</td>\n", "      <td>BBG000G99TC6</td>\n", "      <td>BBG001S5NZJ2</td>\n", "    </tr>\n", "    <tr>\n", "      <th>WB6B.F</th>\n", "      <td>Tinkerine Studios Ltd.</td>\n", "      <td>Tinkerine Studios Ltd. designs, manufactures, ...</td>\n", "      <td>EUR</td>\n", "      <td>Information Technology</td>\n", "      <td>Technology Hardware &amp; Equipment</td>\n", "      <td>Technology Hardware, Storage &amp; Peripherals</td>\n", "      <td>FRA</td>\n", "      <td>Frankfurt Stock Exchange</td>\n", "      <td>Canada</td>\n", "      <td>BC</td>\n", "      <td>Delta</td>\n", "      <td>V4G 0A4</td>\n", "      <td>http://www.tinkerine.com</td>\n", "      <td>Nano Cap</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "    </tr>\n", "    <tr>\n", "      <th>ZT1A.F</th>\n", "      <td>Zebra Technologies Corporation</td>\n", "      <td>Zebra Technologies Corporation, together with ...</td>\n", "      <td>EUR</td>\n", "      <td>Information Technology</td>\n", "      <td>Technology Hardware &amp; Equipment</td>\n", "      <td>Communications Equipment</td>\n", "      <td>FRA</td>\n", "      <td>Frankfurt Stock Exchange</td>\n", "      <td>United States</td>\n", "      <td>IL</td>\n", "      <td>Lincolnshire</td>\n", "      <td>60069</td>\n", "      <td>http://www.zebra.com</td>\n", "      <td>Large Cap</td>\n", "      <td>US9892071054</td>\n", "      <td>989207105</td>\n", "      <td>BBG000GD38T1</td>\n", "      <td>BBG000GD2741</td>\n", "      <td>BBG001S6SX73</td>\n", "    </tr>\n", "    <tr>\n", "      <th>ZU1.F</th>\n", "      <td>Suzuken Co., Ltd.</td>\n", "      <td>Suzuken Co., Ltd., together with its subsidiar...</td>\n", "      <td>EUR</td>\n", "      <td>Health Care</td>\n", "      <td>Health Care Equipment &amp; Services</td>\n", "      <td>Health Care Providers &amp; Services</td>\n", "      <td>FRA</td>\n", "      <td>Frankfurt Stock Exchange</td>\n", "      <td>Japan</td>\n", "      <td>NaN</td>\n", "      <td>Nagoya</td>\n", "      <td>461-8701</td>\n", "      <td>http://www.suzuken.co.jp</td>\n", "      <td>Mid Cap</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "    </tr>\n", "  </tbody>\n", "</table>\n", "<p>64 rows × 19 columns</p>\n", "</div>"], "text/plain": ["                                                     name  \\\n", "symbol                                                      \n", "109.F                              Castlight Health, Inc.   \n", "1KT.F                         Keysight Technologies, Inc.   \n", "1N1.F                          Nanalysis Scientific Corp.   \n", "1YO.F   Yangtze Optical Fibre And Cable Joint Stock Li...   \n", "1ZU.F                             The Pennant Group, Inc.   \n", "...                                                   ...   \n", "V00.F                         Vocera Communications, Inc.   \n", "VNI.F                                         Avnet, Inc.   \n", "WB6B.F                             Tinkerine Studios Ltd.   \n", "ZT1A.F                     Zebra Technologies Corporation   \n", "ZU1.F                                   Suzuken Co., Ltd.   \n", "\n", "                                                  summary currency  \\\n", "symbol                                                               \n", "109.F   Castlight Health, Inc. provides health navigat...      EUR   \n", "1KT.F   Keysight Technologies, Inc. provides electroni...      EUR   \n", "1N1.F   Nanalysis Scientific Corp., develops, manufact...      EUR   \n", "1YO.F   Yangtze Optical Fibre And Cable Joint Stock Li...      EUR   \n", "1ZU.F   The Pennant Group, Inc. provides healthcare se...      EUR   \n", "...                                                   ...      ...   \n", "V00.F   Vocera Communications, Inc. provides secure, i...      EUR   \n", "VNI.F   Avnet, Inc., a technology solutions company, m...      EUR   \n", "WB6B.F  Tinkerine Studios Ltd. designs, manufactures, ...      EUR   \n", "ZT1A.F  Zebra Technologies Corporation, together with ...      EUR   \n", "ZU1.F   Suzuken Co., Ltd., together with its subsidiar...      EUR   \n", "\n", "                        sector                    industry_group  \\\n", "symbol                                                             \n", "109.F              Health Care  Health Care Equipment & Services   \n", "1KT.F   Information Technology   Technology Hardware & Equipment   \n", "1N1.F   Information Technology   Technology Hardware & Equipment   \n", "1YO.F   Information Technology   Technology Hardware & Equipment   \n", "1ZU.F              Health Care  Health Care Equipment & Services   \n", "...                        ...                               ...   \n", "V00.F   Information Technology   Technology Hardware & Equipment   \n", "VNI.F   Information Technology   Technology Hardware & Equipment   \n", "WB6B.F  Information Technology   Technology Hardware & Equipment   \n", "ZT1A.F  Information Technology   Technology Hardware & Equipment   \n", "ZU1.F              Health Care  Health Care Equipment & Services   \n", "\n", "                                              industry exchange  \\\n", "symbol                                                            \n", "109.F                 Health Care Providers & Services      FRA   \n", "1KT.F   Electronic Equipment, Instruments & Components      FRA   \n", "1N1.F   Electronic Equipment, Instruments & Components      FRA   \n", "1YO.F                         Communications Equipment      FRA   \n", "1ZU.F                 Health Care Equipment & Supplies      FRA   \n", "...                                                ...      ...   \n", "V00.F                         Communications Equipment      FRA   \n", "VNI.F   Electronic Equipment, Instruments & Components      FRA   \n", "WB6B.F      Technology Hardware, Storage & Peripherals      FRA   \n", "ZT1A.F                        Communications Equipment      FRA   \n", "ZU1.F                 Health Care Providers & Services      FRA   \n", "\n", "                          market        country state           city  \\\n", "symbol                                                                 \n", "109.F   Frankfurt Stock Exchange  United States    CA  San Francisco   \n", "1KT.F   Frankfurt Stock Exchange  United States    CA     Santa Rosa   \n", "1N1.F   Frankfurt Stock Exchange         Canada    AB        Calgary   \n", "1YO.F   Frankfurt Stock Exchange          China   NaN          Wuhan   \n", "1ZU.F   Frankfurt Stock Exchange  United States    ID          Eagle   \n", "...                          ...            ...   ...            ...   \n", "V00.F   Frankfurt Stock Exchange  United States    CA       San Jose   \n", "VNI.F   Frankfurt Stock Exchange  United States    AZ        Phoenix   \n", "WB6B.F  Frankfurt Stock Exchange         Canada    BC          Delta   \n", "ZT1A.F  Frankfurt Stock Exchange  United States    IL   Lincolnshire   \n", "ZU1.F   Frankfurt Stock Exchange          Japan   NaN         Nagoya   \n", "\n", "           zipcode                         website market_cap          isin  \\\n", "symbol                                                                        \n", "109.F        94105  http://www.castlighthealth.com  Small Cap           NaN   \n", "1KT.F   95403-1738         http://www.keysight.com  Large Cap  US49338L1035   \n", "1N1.F      T2E 7C3        http://www.nanalysis.com   Nano Cap           NaN   \n", "1YO.F       430073             http://www.yofc.com  Small Cap           NaN   \n", "1ZU.F        83616         http://pennantgroup.com  Small Cap  US70805E1091   \n", "...            ...                             ...        ...           ...   \n", "V00.F        95126           http://www.vocera.com  Small Cap           NaN   \n", "VNI.F        85034            http://www.avnet.com    Mid Cap  US0538071038   \n", "WB6B.F     V4G 0A4        http://www.tinkerine.com   Nano Cap           NaN   \n", "ZT1A.F       60069            http://www.zebra.com  Large Cap  US9892071054   \n", "ZU1.F     461-8701        http://www.suzuken.co.jp    Mid Cap           NaN   \n", "\n", "            cusip          figi composite_figi shareclass_figi  \n", "symbol                                                          \n", "109.F         NaN           NaN            NaN             NaN  \n", "1KT.F   49338L103  BBG007DJZFD2   BBG007DJZFC3    BBG0059FN820  \n", "1N1.F         NaN           NaN            NaN             NaN  \n", "1YO.F         NaN           NaN            NaN             NaN  \n", "1ZU.F   7.08E+113  BBG00QJ35K78   BBG00QJ35K69    BBG00P33SZ15  \n", "...           ...           ...            ...             ...  \n", "V00.F         NaN           NaN            NaN             NaN  \n", "VNI.F    53807103  BBG000G99V31   BBG000G99TC6    BBG001S5NZJ2  \n", "WB6B.F        NaN           NaN            NaN             NaN  \n", "ZT1A.F  989207105  BBG000GD38T1   BBG000GD2741    BBG001S6SX73  \n", "ZU1.F         NaN           NaN            NaN             NaN  \n", "\n", "[64 rows x 19 columns]"]}, "execution_count": 10, "metadata": {}, "output_type": "execute_result"}], "source": ["equities.search(\n", "    summary=[\"Robotics\", \"Education\"],\n", "    industry_group=\"Equipment\",\n", "    market=\"Frankfurt\",\n", "    index=\".F\"\n", ")"]}, {"cell_type": "markdown", "id": "7823c390", "metadata": {}, "source": ["Lastly, the Finance Database has a direct integration with the [Finance Toolkit](https://github.com/JerBouma/FinanceToolkit) making it possible to do financial analysis on the companies you've found in the Finance Database. Returning to the earlier example of the 3 insurance companies in the Netherlands, it becomes possible to load these into the Finance Toolkit with the `to_toolkit` functionality. \n", "\n", "To be able to get started, you need to obtain an API Key from FinancialModelingPrep. This is used to gain access to 30+ years of financial statement both annually and quarterly. Note that the Free plan is limited to 250 requests each day, 5 years of data and only features companies listed on US exchanges.\n", "\n", "___ \n", "\n", "<b><div align=\"center\">Obtain an API Key from FinancialModelingPrep <a href=\"https://www.jeroenbouma.com/fmp\" target=\"_blank\">here</a>.</div></b>\n", "___\n"]}, {"cell_type": "code", "execution_count": 11, "id": "73a55437", "metadata": {}, "outputs": [], "source": ["dutch_insurance_companies = equities.select(\n", "    country=\"Netherlands\",\n", "    industry=\"Insurance\",\n", "    market=\"Euronext Amsterdam\",\n", ")\n", "\n", "toolkit = dutch_insurance_companies.to_toolkit(\n", "    api_key=API_KEY\n", ")"]}, {"cell_type": "markdown", "id": "50ef354a", "metadata": {}, "source": ["With this integration, I can now access some of the most important financial metrics for these companies. Let's start simple with historical data."]}, {"cell_type": "code", "execution_count": 12, "id": "858b90a4", "metadata": {}, "outputs": [{"name": "stderr", "output_type": "stream", "text": ["Obtaining historical data: 100%|██████████| 4/4 [00:00<00:00,  9.17it/s]\n"]}, {"data": {"text/html": ["<div>\n", "<style scoped>\n", "    .dataframe tbody tr th:only-of-type {\n", "        vertical-align: middle;\n", "    }\n", "\n", "    .dataframe tbody tr th {\n", "        vertical-align: top;\n", "    }\n", "\n", "    .dataframe thead tr th {\n", "        text-align: left;\n", "    }\n", "\n", "    .dataframe thead tr:last-of-type th {\n", "        text-align: right;\n", "    }\n", "</style>\n", "<table border=\"1\" class=\"dataframe\">\n", "  <thead>\n", "    <tr>\n", "      <th></th>\n", "      <th colspan=\"4\" halign=\"left\">Open</th>\n", "      <th colspan=\"4\" halign=\"left\">High</th>\n", "      <th colspan=\"2\" halign=\"left\">Low</th>\n", "      <th>...</th>\n", "      <th colspan=\"2\" halign=\"left\">Excess Return</th>\n", "      <th colspan=\"4\" halign=\"left\">Excess Volatility</th>\n", "      <th colspan=\"4\" halign=\"left\">Cumulative Return</th>\n", "    </tr>\n", "    <tr>\n", "      <th></th>\n", "      <th>AGN.AS</th>\n", "      <th>ASRNL.AS</th>\n", "      <th>NN.AS</th>\n", "      <th>Benchmark</th>\n", "      <th>AGN.AS</th>\n", "      <th>ASRNL.AS</th>\n", "      <th>NN.AS</th>\n", "      <th>Benchmark</th>\n", "      <th>AGN.AS</th>\n", "      <th>ASRNL.AS</th>\n", "      <th>...</th>\n", "      <th>NN.AS</th>\n", "      <th>Benchmark</th>\n", "      <th>AGN.AS</th>\n", "      <th>ASRNL.AS</th>\n", "      <th>NN.AS</th>\n", "      <th>Benchmark</th>\n", "      <th>AGN.AS</th>\n", "      <th>ASRNL.AS</th>\n", "      <th>NN.AS</th>\n", "      <th>Benchmark</th>\n", "    </tr>\n", "    <tr>\n", "      <th>date</th>\n", "      <th></th>\n", "      <th></th>\n", "      <th></th>\n", "      <th></th>\n", "      <th></th>\n", "      <th></th>\n", "      <th></th>\n", "      <th></th>\n", "      <th></th>\n", "      <th></th>\n", "      <th></th>\n", "      <th></th>\n", "      <th></th>\n", "      <th></th>\n", "      <th></th>\n", "      <th></th>\n", "      <th></th>\n", "      <th></th>\n", "      <th></th>\n", "      <th></th>\n", "      <th></th>\n", "    </tr>\n", "  </thead>\n", "  <tbody>\n", "    <tr>\n", "      <th>2015-04-07</th>\n", "      <td>7.49</td>\n", "      <td>NaN</td>\n", "      <td>26.5</td>\n", "      <td>207.86</td>\n", "      <td>7.55</td>\n", "      <td>NaN</td>\n", "      <td>26.63</td>\n", "      <td>208.76</td>\n", "      <td>7.46</td>\n", "      <td>NaN</td>\n", "      <td>...</td>\n", "      <td>-0.0065</td>\n", "      <td>-0.0215</td>\n", "      <td>0.025</td>\n", "      <td>NaN</td>\n", "      <td>0.0201</td>\n", "      <td>0.0158</td>\n", "      <td>1.0</td>\n", "      <td>NaN</td>\n", "      <td>1.0</td>\n", "      <td>1.0</td>\n", "    </tr>\n", "    <tr>\n", "      <th>2015-04-08</th>\n", "      <td>7.5</td>\n", "      <td>NaN</td>\n", "      <td>26.53</td>\n", "      <td>207.55</td>\n", "      <td>7.54</td>\n", "      <td>NaN</td>\n", "      <td>26.67</td>\n", "      <td>208.51</td>\n", "      <td>7.46</td>\n", "      <td>NaN</td>\n", "      <td>...</td>\n", "      <td>-0.021</td>\n", "      <td>-0.0157</td>\n", "      <td>0.025</td>\n", "      <td>NaN</td>\n", "      <td>0.0201</td>\n", "      <td>0.0158</td>\n", "      <td>0.9934</td>\n", "      <td>NaN</td>\n", "      <td>0.998</td>\n", "      <td>1.0033</td>\n", "    </tr>\n", "    <tr>\n", "      <th>2015-04-09</th>\n", "      <td>7.52</td>\n", "      <td>NaN</td>\n", "      <td>26.61</td>\n", "      <td>207.78</td>\n", "      <td>7.53</td>\n", "      <td>NaN</td>\n", "      <td>26.86</td>\n", "      <td>209.18</td>\n", "      <td>7.47</td>\n", "      <td>NaN</td>\n", "      <td>...</td>\n", "      <td>-0.0114</td>\n", "      <td>-0.0151</td>\n", "      <td>0.025</td>\n", "      <td>NaN</td>\n", "      <td>0.0201</td>\n", "      <td>0.0158</td>\n", "      <td>0.9978</td>\n", "      <td>NaN</td>\n", "      <td>1.0061</td>\n", "      <td>1.0078</td>\n", "    </tr>\n", "    <tr>\n", "      <th>2015-04-10</th>\n", "      <td>7.53</td>\n", "      <td>NaN</td>\n", "      <td>26.86</td>\n", "      <td>209.2</td>\n", "      <td>7.61</td>\n", "      <td>NaN</td>\n", "      <td>27.36</td>\n", "      <td>210.09</td>\n", "      <td>7.52</td>\n", "      <td>NaN</td>\n", "      <td>...</td>\n", "      <td>0.0014</td>\n", "      <td>-0.014</td>\n", "      <td>0.025</td>\n", "      <td>NaN</td>\n", "      <td>0.0201</td>\n", "      <td>0.0158</td>\n", "      <td>1.0066</td>\n", "      <td>NaN</td>\n", "      <td>1.0272</td>\n", "      <td>1.0133</td>\n", "    </tr>\n", "    <tr>\n", "      <th>2015-04-13</th>\n", "      <td>7.59</td>\n", "      <td>NaN</td>\n", "      <td>27.4</td>\n", "      <td>209.87</td>\n", "      <td>7.64</td>\n", "      <td>NaN</td>\n", "      <td>27.9</td>\n", "      <td>210.63</td>\n", "      <td>7.57</td>\n", "      <td>NaN</td>\n", "      <td>...</td>\n", "      <td>-0.0009</td>\n", "      <td>-0.0239</td>\n", "      <td>0.025</td>\n", "      <td>NaN</td>\n", "      <td>0.0201</td>\n", "      <td>0.0158</td>\n", "      <td>1.0088</td>\n", "      <td>NaN</td>\n", "      <td>1.0462</td>\n", "      <td>1.0087</td>\n", "    </tr>\n", "    <tr>\n", "      <th>...</th>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "    </tr>\n", "    <tr>\n", "      <th>2025-03-31</th>\n", "      <td>6.02</td>\n", "      <td>52.86</td>\n", "      <td>51.28</td>\n", "      <td>549.83</td>\n", "      <td>6.06</td>\n", "      <td>52.98</td>\n", "      <td>51.34</td>\n", "      <td>560.71</td>\n", "      <td>6.0</td>\n", "      <td>52.56</td>\n", "      <td>...</td>\n", "      <td>-0.0483</td>\n", "      <td>-0.0358</td>\n", "      <td>0.025</td>\n", "      <td>0.0206</td>\n", "      <td>0.0201</td>\n", "      <td>0.0158</td>\n", "      <td>1.3326</td>\n", "      <td>4.2726</td>\n", "      <td>3.4861</td>\n", "      <td>3.2075</td>\n", "    </tr>\n", "    <tr>\n", "      <th>2025-04-01</th>\n", "      <td>6.09</td>\n", "      <td>53.22</td>\n", "      <td>51.48</td>\n", "      <td>557.45</td>\n", "      <td>6.13</td>\n", "      <td>53.6</td>\n", "      <td>51.6</td>\n", "      <td>562.94</td>\n", "      <td>6.04</td>\n", "      <td>52.98</td>\n", "      <td>...</td>\n", "      <td>-0.0389</td>\n", "      <td>-0.0388</td>\n", "      <td>0.025</td>\n", "      <td>0.0206</td>\n", "      <td>0.0201</td>\n", "      <td>0.0158</td>\n", "      <td>1.3458</td>\n", "      <td>4.3097</td>\n", "      <td>3.4956</td>\n", "      <td>3.2166</td>\n", "    </tr>\n", "    <tr>\n", "      <th>2025-04-02</th>\n", "      <td>6.08</td>\n", "      <td>53.18</td>\n", "      <td>51.3</td>\n", "      <td>555.05</td>\n", "      <td>6.13</td>\n", "      <td>53.58</td>\n", "      <td>51.5</td>\n", "      <td>567.42</td>\n", "      <td>6.0</td>\n", "      <td>52.7</td>\n", "      <td>...</td>\n", "      <td>-0.0408</td>\n", "      <td>-0.0357</td>\n", "      <td>0.025</td>\n", "      <td>0.0206</td>\n", "      <td>0.0201</td>\n", "      <td>0.0158</td>\n", "      <td>1.3502</td>\n", "      <td>4.2984</td>\n", "      <td>3.4997</td>\n", "      <td>3.2369</td>\n", "    </tr>\n", "    <tr>\n", "      <th>2025-04-03</th>\n", "      <td>5.9</td>\n", "      <td>52.32</td>\n", "      <td>50.7</td>\n", "      <td>545.11</td>\n", "      <td>6.01</td>\n", "      <td>53.22</td>\n", "      <td>51.54</td>\n", "      <td>547.97</td>\n", "      <td>5.64</td>\n", "      <td>52.18</td>\n", "      <td>...</td>\n", "      <td>-0.0564</td>\n", "      <td>-0.0898</td>\n", "      <td>0.025</td>\n", "      <td>0.0206</td>\n", "      <td>0.0201</td>\n", "      <td>0.0158</td>\n", "      <td>1.2467</td>\n", "      <td>4.2274</td>\n", "      <td>3.4439</td>\n", "      <td>3.0774</td>\n", "    </tr>\n", "    <tr>\n", "      <th>2025-04-04</th>\n", "      <td>5.622</td>\n", "      <td>52.0</td>\n", "      <td>50.32</td>\n", "      <td>523.67</td>\n", "      <td>5.642</td>\n", "      <td>52.5</td>\n", "      <td>50.62</td>\n", "      <td>525.87</td>\n", "      <td>5.096</td>\n", "      <td>49.45</td>\n", "      <td>...</td>\n", "      <td>-0.0797</td>\n", "      <td>-0.077</td>\n", "      <td>0.025</td>\n", "      <td>0.0206</td>\n", "      <td>0.0201</td>\n", "      <td>0.0158</td>\n", "      <td>1.1736</td>\n", "      <td>4.1081</td>\n", "      <td>3.3059</td>\n", "      <td>2.9622</td>\n", "    </tr>\n", "  </tbody>\n", "</table>\n", "<p>2585 rows × 48 columns</p>\n", "</div>"], "text/plain": ["             Open                            High                           \\\n", "           AGN.AS ASRNL.AS NN.AS Benchmark AGN.AS ASRNL.AS NN.AS Benchmark   \n", "date                                                                         \n", "2015-04-07   7.49      NaN  26.5    207.86   7.55      NaN 26.63    208.76   \n", "2015-04-08    7.5      NaN 26.53    207.55   7.54      NaN 26.67    208.51   \n", "2015-04-09   7.52      NaN 26.61    207.78   7.53      NaN 26.86    209.18   \n", "2015-04-10   7.53      NaN 26.86     209.2   7.61      NaN 27.36    210.09   \n", "2015-04-13   7.59      NaN  27.4    209.87   7.64      NaN  27.9    210.63   \n", "...           ...      ...   ...       ...    ...      ...   ...       ...   \n", "2025-03-31   6.02    52.86 51.28    549.83   6.06    52.98 51.34    560.71   \n", "2025-04-01   6.09    53.22 51.48    557.45   6.13     53.6  51.6    562.94   \n", "2025-04-02   6.08    53.18  51.3    555.05   6.13    53.58  51.5    567.42   \n", "2025-04-03    5.9    52.32  50.7    545.11   6.01    53.22 51.54    547.97   \n", "2025-04-04  5.622     52.0 50.32    523.67  5.642     52.5 50.62    525.87   \n", "\n", "              Low           ... Excess Return           Excess Volatility  \\\n", "           AGN.AS ASRNL.AS  ...         NN.AS Benchmark            AGN.AS   \n", "date                        ...                                             \n", "2015-04-07   7.46      NaN  ...       -0.0065   -0.0215             0.025   \n", "2015-04-08   7.46      NaN  ...        -0.021   -0.0157             0.025   \n", "2015-04-09   7.47      NaN  ...       -0.0114   -0.0151             0.025   \n", "2015-04-10   7.52      NaN  ...        0.0014    -0.014             0.025   \n", "2015-04-13   7.57      NaN  ...       -0.0009   -0.0239             0.025   \n", "...           ...      ...  ...           ...       ...               ...   \n", "2025-03-31    6.0    52.56  ...       -0.0483   -0.0358             0.025   \n", "2025-04-01   6.04    52.98  ...       -0.0389   -0.0388             0.025   \n", "2025-04-02    6.0     52.7  ...       -0.0408   -0.0357             0.025   \n", "2025-04-03   5.64    52.18  ...       -0.0564   -0.0898             0.025   \n", "2025-04-04  5.096    49.45  ...       -0.0797    -0.077             0.025   \n", "\n", "                                     Cumulative Return                  \\\n", "           ASRNL.AS  NN.AS Benchmark            AGN.AS ASRNL.AS  NN.AS   \n", "date                                                                     \n", "2015-04-07      NaN 0.0201    0.0158               1.0      NaN    1.0   \n", "2015-04-08      NaN 0.0201    0.0158            0.9934      NaN  0.998   \n", "2015-04-09      NaN 0.0201    0.0158            0.9978      NaN 1.0061   \n", "2015-04-10      NaN 0.0201    0.0158            1.0066      NaN 1.0272   \n", "2015-04-13      NaN 0.0201    0.0158            1.0088      NaN 1.0462   \n", "...             ...    ...       ...               ...      ...    ...   \n", "2025-03-31   0.0206 0.0201    0.0158            1.3326   4.2726 3.4861   \n", "2025-04-01   0.0206 0.0201    0.0158            1.3458   4.3097 3.4956   \n", "2025-04-02   0.0206 0.0201    0.0158            1.3502   4.2984 3.4997   \n", "2025-04-03   0.0206 0.0201    0.0158            1.2467   4.2274 3.4439   \n", "2025-04-04   0.0206 0.0201    0.0158            1.1736   4.1081 3.3059   \n", "\n", "                      \n", "           Benchmark  \n", "date                  \n", "2015-04-07       1.0  \n", "2015-04-08    1.0033  \n", "2015-04-09    1.0078  \n", "2015-04-10    1.0133  \n", "2015-04-13    1.0087  \n", "...              ...  \n", "2025-03-31    3.2075  \n", "2025-04-01    3.2166  \n", "2025-04-02    3.2369  \n", "2025-04-03    3.0774  \n", "2025-04-04    2.9622  \n", "\n", "[2585 rows x 48 columns]"]}, "execution_count": 12, "metadata": {}, "output_type": "execute_result"}], "source": ["toolkit.get_historical_data()"]}, {"cell_type": "markdown", "id": "d0f4dbbb", "metadata": {}, "source": ["And now let's make it more advanced by automatically calculating 60+ financial ratios for each. **This is just a small snippet of what is available within the Finance Toolkit, see for more information the GitHub page of the Finance Toolkit [here](https://github.com/JerBouma/FinanceToolkit) or the example Notebook [here](https://www.jeroenbouma.com/projects/financetoolkit/getting-started).**"]}, {"cell_type": "code", "execution_count": 13, "id": "cefdf880", "metadata": {}, "outputs": [{"name": "stderr", "output_type": "stream", "text": ["Obtaining financial statements: 100%|██████████| 3/3 [00:03<00:00,  1.21s/it]\n"]}, {"data": {"text/html": ["<div>\n", "<style scoped>\n", "    .dataframe tbody tr th:only-of-type {\n", "        vertical-align: middle;\n", "    }\n", "\n", "    .dataframe tbody tr th {\n", "        vertical-align: top;\n", "    }\n", "\n", "    .dataframe thead th {\n", "        text-align: right;\n", "    }\n", "</style>\n", "<table border=\"1\" class=\"dataframe\">\n", "  <thead>\n", "    <tr style=\"text-align: right;\">\n", "      <th></th>\n", "      <th></th>\n", "      <th>2015</th>\n", "      <th>2016</th>\n", "      <th>2017</th>\n", "      <th>2018</th>\n", "      <th>2019</th>\n", "      <th>2020</th>\n", "      <th>2021</th>\n", "      <th>2022</th>\n", "      <th>2023</th>\n", "      <th>2024</th>\n", "    </tr>\n", "  </thead>\n", "  <tbody>\n", "    <tr>\n", "      <th rowspan=\"5\" valign=\"top\">AGN.AS</th>\n", "      <th>Days of Inventory Outstanding</th>\n", "      <td>NaN</td>\n", "      <td>-12621.958</td>\n", "      <td>-2182.1207</td>\n", "      <td>-inf</td>\n", "      <td>-inf</td>\n", "      <td>-inf</td>\n", "      <td>-inf</td>\n", "      <td>-inf</td>\n", "      <td>-2949.2888</td>\n", "      <td>NaN</td>\n", "    </tr>\n", "    <tr>\n", "      <th>Days of Sales Outstanding</th>\n", "      <td>NaN</td>\n", "      <td>19.1127</td>\n", "      <td>0.0</td>\n", "      <td>75.4909</td>\n", "      <td>37.8338</td>\n", "      <td>55.4968</td>\n", "      <td>49.2384</td>\n", "      <td>-64.7491</td>\n", "      <td>22.0342</td>\n", "      <td>69.1609</td>\n", "    </tr>\n", "    <tr>\n", "      <th>Operating Cycle</th>\n", "      <td>NaN</td>\n", "      <td>-12602.8453</td>\n", "      <td>-2182.1207</td>\n", "      <td>-inf</td>\n", "      <td>-inf</td>\n", "      <td>-inf</td>\n", "      <td>-inf</td>\n", "      <td>-inf</td>\n", "      <td>-2927.2546</td>\n", "      <td>NaN</td>\n", "    </tr>\n", "    <tr>\n", "      <th>Days of Accounts Payable Outstanding</th>\n", "      <td>NaN</td>\n", "      <td>362.581</td>\n", "      <td>487.573</td>\n", "      <td>inf</td>\n", "      <td>inf</td>\n", "      <td>inf</td>\n", "      <td>inf</td>\n", "      <td>inf</td>\n", "      <td>89.0992</td>\n", "      <td>inf</td>\n", "    </tr>\n", "    <tr>\n", "      <th>Cash Conversion Cycle</th>\n", "      <td>NaN</td>\n", "      <td>-12965.4263</td>\n", "      <td>-2669.6937</td>\n", "      <td>-inf</td>\n", "      <td>-inf</td>\n", "      <td>-inf</td>\n", "      <td>-inf</td>\n", "      <td>-inf</td>\n", "      <td>-3016.3538</td>\n", "      <td>NaN</td>\n", "    </tr>\n", "    <tr>\n", "      <th>...</th>\n", "      <th>...</th>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "    </tr>\n", "    <tr>\n", "      <th rowspan=\"5\" valign=\"top\">NN.AS</th>\n", "      <th>EV-to-EBIT</th>\n", "      <td>3.6779</td>\n", "      <td>4.0378</td>\n", "      <td>3.8969</td>\n", "      <td>4.4146</td>\n", "      <td>4.8369</td>\n", "      <td>2.9465</td>\n", "      <td>3.9009</td>\n", "      <td>15.6765</td>\n", "      <td>5.9392</td>\n", "      <td>4.9981</td>\n", "    </tr>\n", "    <tr>\n", "      <th>EV-to-EBITDA</th>\n", "      <td>3.6042</td>\n", "      <td>3.9534</td>\n", "      <td>3.6938</td>\n", "      <td>4.0438</td>\n", "      <td>5.5222</td>\n", "      <td>2.8657</td>\n", "      <td>5.0888</td>\n", "      <td>7.6525</td>\n", "      <td>2.0593</td>\n", "      <td>1.7122</td>\n", "    </tr>\n", "    <tr>\n", "      <th>EV-to-Operating-Cash-Flow</th>\n", "      <td>-1.2674</td>\n", "      <td>-3.9051</td>\n", "      <td>-2.0777</td>\n", "      <td>-3.75</td>\n", "      <td>5.1372</td>\n", "      <td>1.1939</td>\n", "      <td>-6.7191</td>\n", "      <td>-2.2494</td>\n", "      <td>244.5613</td>\n", "      <td>-40.2091</td>\n", "    </tr>\n", "    <tr>\n", "      <th>Tangible Asset Value</th>\n", "      <td>***********.0</td>\n", "      <td>***********.0</td>\n", "      <td>***********.0</td>\n", "      <td>***********.0</td>\n", "      <td>***********.0</td>\n", "      <td>***********.0</td>\n", "      <td>***********.0</td>\n", "      <td>***********.0</td>\n", "      <td>***********.0</td>\n", "      <td>***********.0</td>\n", "    </tr>\n", "    <tr>\n", "      <th>Net Current Asset Value</th>\n", "      <td>***********.0</td>\n", "      <td>***********.0</td>\n", "      <td>**********.0</td>\n", "      <td>************.0</td>\n", "      <td>************.0</td>\n", "      <td>************.0</td>\n", "      <td>************.0</td>\n", "      <td>***********.0</td>\n", "      <td>************.0</td>\n", "      <td>0.0</td>\n", "    </tr>\n", "  </tbody>\n", "</table>\n", "<p>201 rows × 10 columns</p>\n", "</div>"], "text/plain": ["                                                     2015          2016  \\\n", "AGN.AS Days of Inventory Outstanding                  NaN    -12621.958   \n", "       Days of Sales Outstanding                      NaN       19.1127   \n", "       Operating Cycle                                NaN   -12602.8453   \n", "       Days of Accounts Payable Outstanding           NaN       362.581   \n", "       Cash Conversion Cycle                          NaN   -12965.4263   \n", "...                                                   ...           ...   \n", "NN.AS  EV-to-EBIT                                  3.6779        4.0378   \n", "       EV-to-EBITDA                                3.6042        3.9534   \n", "       EV-to-Operating-Cash-Flow                  -1.2674       -3.9051   \n", "       Tangible Asset Value                 ***********.0 ***********.0   \n", "       Net Current Asset Value              ***********.0 ***********.0   \n", "\n", "                                                     2017           2018  \\\n", "AGN.AS Days of Inventory Outstanding           -2182.1207           -inf   \n", "       Days of Sales Outstanding                      0.0        75.4909   \n", "       Operating Cycle                         -2182.1207           -inf   \n", "       Days of Accounts Payable Outstanding       487.573            inf   \n", "       Cash Conversion Cycle                   -2669.6937           -inf   \n", "...                                                   ...            ...   \n", "NN.AS  EV-to-EBIT                                  3.8969         4.4146   \n", "       EV-to-EBITDA                                3.6938         4.0438   \n", "       EV-to-Operating-Cash-Flow                  -2.0777          -3.75   \n", "       Tangible Asset Value                 ***********.0  ***********.0   \n", "       Net Current Asset Value               **********.0 ************.0   \n", "\n", "                                                      2019           2020  \\\n", "AGN.AS Days of Inventory Outstanding                  -inf           -inf   \n", "       Days of Sales Outstanding                   37.8338        55.4968   \n", "       Operating Cycle                                -inf           -inf   \n", "       Days of Accounts Payable Outstanding            inf            inf   \n", "       Cash Conversion Cycle                          -inf           -inf   \n", "...                                                    ...            ...   \n", "NN.AS  EV-to-EBIT                                   4.8369         2.9465   \n", "       EV-to-EBITDA                                 5.5222         2.8657   \n", "       EV-to-Operating-Cash-Flow                    5.1372         1.1939   \n", "       Tangible Asset Value                  ***********.0  ***********.0   \n", "       Net Current Asset Value              ************.0 ************.0   \n", "\n", "                                                      2021          2022  \\\n", "AGN.AS Days of Inventory Outstanding                  -inf          -inf   \n", "       Days of Sales Outstanding                   49.2384      -64.7491   \n", "       Operating Cycle                                -inf          -inf   \n", "       Days of Accounts Payable Outstanding            inf           inf   \n", "       Cash Conversion Cycle                          -inf          -inf   \n", "...                                                    ...           ...   \n", "NN.AS  EV-to-EBIT                                   3.9009       15.6765   \n", "       EV-to-EBITDA                                 5.0888        7.6525   \n", "       EV-to-Operating-Cash-Flow                   -6.7191       -2.2494   \n", "       Tangible Asset Value                  ***********.0 ***********.0   \n", "       Net Current Asset Value              ************.0 ***********.0   \n", "\n", "                                                      2023          2024  \n", "AGN.AS Days of Inventory Outstanding            -2949.2888           NaN  \n", "       Days of Sales Outstanding                   22.0342       69.1609  \n", "       Operating Cycle                          -2927.2546           NaN  \n", "       Days of Accounts Payable Outstanding        89.0992           inf  \n", "       Cash Conversion Cycle                    -3016.3538           NaN  \n", "...                                                    ...           ...  \n", "NN.AS  EV-to-EBIT                                   5.9392        4.9981  \n", "       EV-to-EBITDA                                 2.0593        1.7122  \n", "       EV-to-Operating-Cash-Flow                  244.5613      -40.2091  \n", "       Tangible Asset Value                  ***********.0 ***********.0  \n", "       Net Current Asset Value              ************.0           0.0  \n", "\n", "[201 rows x 10 columns]"]}, "execution_count": 13, "metadata": {}, "output_type": "execute_result"}], "source": ["toolkit.ratios.collect_all_ratios()"]}, {"attachments": {}, "cell_type": "markdown", "id": "754b1ee6", "metadata": {}, "source": ["All of these methods are also available for the other asset classes. The only difference is that the class name changes and the available columns. For example, for ETFs you would use `fd.ETFs()` instead of `fd.Equities()` and the `select` option has parameters such as `category_group` and `family` instead."]}, {"cell_type": "code", "execution_count": 14, "id": "f0e17359", "metadata": {}, "outputs": [{"data": {"text/html": ["<div>\n", "<style scoped>\n", "    .dataframe tbody tr th:only-of-type {\n", "        vertical-align: middle;\n", "    }\n", "\n", "    .dataframe tbody tr th {\n", "        vertical-align: top;\n", "    }\n", "\n", "    .dataframe thead th {\n", "        text-align: right;\n", "    }\n", "</style>\n", "<table border=\"1\" class=\"dataframe\">\n", "  <thead>\n", "    <tr style=\"text-align: right;\">\n", "      <th></th>\n", "      <th>name</th>\n", "      <th>currency</th>\n", "      <th>summary</th>\n", "      <th>category_group</th>\n", "      <th>category</th>\n", "      <th>family</th>\n", "      <th>exchange</th>\n", "    </tr>\n", "    <tr>\n", "      <th>symbol</th>\n", "      <th></th>\n", "      <th></th>\n", "      <th></th>\n", "      <th></th>\n", "      <th></th>\n", "      <th></th>\n", "      <th></th>\n", "    </tr>\n", "  </thead>\n", "  <tbody>\n", "    <tr>\n", "      <th>^ADFI-IV</th>\n", "      <td>NFIELD DYNAMIC FIXED INCOME ETF</td>\n", "      <td>USD</td>\n", "      <td>The NFIELD DYNAMIC FIXED INCOME ETF (ADFI) is ...</td>\n", "      <td>Fixed Income</td>\n", "      <td>Corporate Bonds</td>\n", "      <td>NaN</td>\n", "      <td>ASE</td>\n", "    </tr>\n", "    <tr>\n", "      <th>^BND</th>\n", "      <td>VANGUARD BD IDX FD</td>\n", "      <td>USD</td>\n", "      <td>The Vanguard Total Bond Market ETF seeks to tr...</td>\n", "      <td>Fixed Income</td>\n", "      <td>Investment Grade Bonds</td>\n", "      <td>Vanguard Asset Management</td>\n", "      <td>NIM</td>\n", "    </tr>\n", "    <tr>\n", "      <th>^BNDX</th>\n", "      <td>VANGUARD CHARLOTTE</td>\n", "      <td>USD</td>\n", "      <td>The Vanguard Total International Bond ETF seek...</td>\n", "      <td>Fixed Income</td>\n", "      <td>Investment Grade Bonds</td>\n", "      <td>Vanguard Asset Management</td>\n", "      <td>NIM</td>\n", "    </tr>\n", "    <tr>\n", "      <th>^VCIT</th>\n", "      <td>VANGUARD SCOTTSDAL</td>\n", "      <td>USD</td>\n", "      <td>The Vanguard Intermediate-Term Corporate Bond ...</td>\n", "      <td>Fixed Income</td>\n", "      <td>Corporate Bonds</td>\n", "      <td>Vanguard Asset Management</td>\n", "      <td>NIM</td>\n", "    </tr>\n", "    <tr>\n", "      <th>^VCLT</th>\n", "      <td>VANGUARD SCOTTSDAL</td>\n", "      <td>USD</td>\n", "      <td>The Vanguard Long-Term Corporate Bond ETF seek...</td>\n", "      <td>Fixed Income</td>\n", "      <td>Corporate Bonds</td>\n", "      <td>Vanguard Asset Management</td>\n", "      <td>NIM</td>\n", "    </tr>\n", "    <tr>\n", "      <th>...</th>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "    </tr>\n", "    <tr>\n", "      <th>ZUS-U.TO</th>\n", "      <td>BMO Ultra Short-Term US Bond ETF (US Dollar Un...</td>\n", "      <td>USD</td>\n", "      <td>BMO Ultra Short-Term US Bond ETF seeks to prov...</td>\n", "      <td>Fixed Income</td>\n", "      <td>Corporate Bonds</td>\n", "      <td>BMO Capital Markets</td>\n", "      <td>TOR</td>\n", "    </tr>\n", "    <tr>\n", "      <th>ZUS-V.TO</th>\n", "      <td>BMO Ultra Short-Term US Bond ETF (US Dollar Ac...</td>\n", "      <td>USD</td>\n", "      <td>BMO Ultra Short-Term US Bond ETF seeks to prov...</td>\n", "      <td>Fixed Income</td>\n", "      <td>Corporate Bonds</td>\n", "      <td>BMO Capital Markets</td>\n", "      <td>TOR</td>\n", "    </tr>\n", "    <tr>\n", "      <th>0A12.L</th>\n", "      <td>Invesco Ultra Short Duration ETF</td>\n", "      <td>NaN</td>\n", "      <td>The investment seeks maximum current income, c...</td>\n", "      <td>Fixed Income</td>\n", "      <td>Corporate Bonds</td>\n", "      <td>Invesco Investment Management</td>\n", "      <td>NaN</td>\n", "    </tr>\n", "    <tr>\n", "      <th>EMAG.SN</th>\n", "      <td>VanEck Vectors Emerging Markets Aggregate Bond...</td>\n", "      <td>NaN</td>\n", "      <td>The investment seeks to replicate as closely a...</td>\n", "      <td>Fixed Income</td>\n", "      <td>Corporate Bonds</td>\n", "      <td>VanEck Asset Management</td>\n", "      <td>NaN</td>\n", "    </tr>\n", "    <tr>\n", "      <th>EMLC.SN</th>\n", "      <td>VanEck Vectors J.<PERSON>. Morgan EM Local Currency B...</td>\n", "      <td>NaN</td>\n", "      <td>The investment seeks to replicate as closely a...</td>\n", "      <td>Fixed Income</td>\n", "      <td>Corporate Bonds</td>\n", "      <td>VanEck Asset Management</td>\n", "      <td>NaN</td>\n", "    </tr>\n", "  </tbody>\n", "</table>\n", "<p>6691 rows × 7 columns</p>\n", "</div>"], "text/plain": ["                                                       name currency  \\\n", "symbol                                                                 \n", "^ADFI-IV                    NFIELD DYNAMIC FIXED INCOME ETF      USD   \n", "^BND                                     VANGUARD BD IDX FD      USD   \n", "^BNDX                                    VANGUARD CHARLOTTE      USD   \n", "^VCIT                                    VANGUARD SCOTTSDAL      USD   \n", "^VCLT                                    VANGUARD SCOTTSDAL      USD   \n", "...                                                     ...      ...   \n", "ZUS-U.TO  BMO Ultra Short-Term US Bond ETF (US Dollar Un...      USD   \n", "ZUS-V.TO  BMO Ultra Short-Term US Bond ETF (US Dollar Ac...      USD   \n", "0A12.L                     Invesco Ultra Short Duration ETF      NaN   \n", "EMAG.SN   VanEck Vectors Emerging Markets Aggregate Bond...      NaN   \n", "EMLC.SN   VanEck Vectors J.P. Morgan EM Local Currency B...      NaN   \n", "\n", "                                                    summary category_group  \\\n", "symbol                                                                       \n", "^ADFI-IV  The NFIELD DYNAMIC FIXED INCOME ETF (ADFI) is ...   Fixed Income   \n", "^BND      The Vanguard Total Bond Market ETF seeks to tr...   Fixed Income   \n", "^BNDX     The Vanguard Total International Bond ETF seek...   Fixed Income   \n", "^VCIT     The Vanguard Intermediate-Term Corporate Bond ...   Fixed Income   \n", "^VCLT     The Vanguard Long-Term Corporate Bond ETF seek...   Fixed Income   \n", "...                                                     ...            ...   \n", "ZUS-U.TO  BMO Ultra Short-Term US Bond ETF seeks to prov...   Fixed Income   \n", "ZUS-V.TO  BMO Ultra Short-Term US Bond ETF seeks to prov...   Fixed Income   \n", "0A12.L    The investment seeks maximum current income, c...   Fixed Income   \n", "EMAG.SN   The investment seeks to replicate as closely a...   Fixed Income   \n", "EMLC.SN   The investment seeks to replicate as closely a...   Fixed Income   \n", "\n", "                        category                         family exchange  \n", "symbol                                                                    \n", "^ADFI-IV         Corporate Bonds                            NaN      ASE  \n", "^BND      Investment Grade Bonds      Vanguard Asset Management      NIM  \n", "^BNDX     Investment Grade Bonds      Vanguard Asset Management      NIM  \n", "^VCIT            Corporate Bonds      Vanguard Asset Management      NIM  \n", "^VCLT            Corporate Bonds      Vanguard Asset Management      NIM  \n", "...                          ...                            ...      ...  \n", "ZUS-U.TO         Corporate Bonds            BMO Capital Markets      TOR  \n", "ZUS-V.TO         Corporate Bonds            BMO Capital Markets      TOR  \n", "0A12.L           Corporate Bonds  Invesco Investment Management      NaN  \n", "EMAG.SN          Corporate Bonds        VanEck Asset Management      NaN  \n", "EMLC.SN          Corporate Bonds        VanEck Asset Management      NaN  \n", "\n", "[6691 rows x 7 columns]"]}, "execution_count": 14, "metadata": {}, "output_type": "execute_result"}], "source": ["# Obtain all sectors from the database\n", "etfs = fd.ETFs()\n", "\n", "etfs.select(\n", "    category_group=\"Fixed Income\"\n", ")"]}, {"attachments": {}, "cell_type": "markdown", "id": "0ab89b95", "metadata": {}, "source": ["This also translates to the available options, for example let's select `fd.Indices()` instead."]}, {"cell_type": "code", "execution_count": 15, "id": "e21194f5", "metadata": {}, "outputs": [{"data": {"text/plain": ["{'category_group': array(['Alternatives', 'Cash', 'Commodities', 'Communication Services',\n", "        'Consumer Discretionary', 'Consumer Staples', 'Currencies',\n", "        'Derivatives', 'Energy', 'Equities', 'Financials', 'Fixed Income',\n", "        'Health Care', 'Industrials', 'Information Technology',\n", "        'Materials', 'Real Estate', 'Utilities'], dtype=object),\n", " 'category': array(['Alternative', 'Blend', 'Bonds', 'Cash', 'Commercial Real Estate',\n", "        'Commodities Broad Basket', 'Communications',\n", "        'Consumer Discretionary', 'Consumer Staples', 'Corporate Bonds',\n", "        'Currencies', 'Derivatives', 'Developed Markets',\n", "        'Emerging Markets', 'Energy', 'Equities', 'Factors', 'Financials',\n", "        'Frontier Markets', 'Government Bonds', 'Growth', 'Health Care',\n", "        'High Yield Bonds', 'Industrials',\n", "        'Inflation-Protected Securities', 'Investment Grade Bonds',\n", "        'Large Cap', 'Materials', 'Micro Cap', 'Mid Cap',\n", "        'Money Market Instruments', 'Municipal Bonds', 'REITs',\n", "        'Real Estate Development', 'Real Estate Services',\n", "        'Residential Real Estate', 'Small Cap', 'Technology', 'Trading',\n", "        'Treasury Bonds', 'Utilities', 'Value'], dtype=object),\n", " 'currency': array(['AED', 'AUD', 'BGN', 'BRL', 'CAD', 'CHF', 'CLP', 'CNY', 'COP',\n", "        'CZK', 'DKK', 'EGP', 'EUR', 'GBP', 'GBp', 'HKD', 'HUF', 'IDR',\n", "        'ILS', 'INR', 'ISK', 'JPY', 'KEW', 'KRW', 'KWD', 'LKR', 'MCE',\n", "        'MXN', 'MYR', 'NOK', 'NZD', 'PEN', 'PHP', 'PKR', 'PLN', 'QAR',\n", "        'RUB', 'SAR', 'SEK', 'SGD', 'THB', 'TRY', 'TWD', 'USD', 'ZAR'],\n", "       dtype=object),\n", " 'exchange': array(['AMS', 'ASE', 'ASX', 'ATH', 'BRU', 'BSE', 'BUD', 'BUE', 'CAI',\n", "        'CCS', 'CSE', 'DJ<PERSON>', 'DOH', 'EBS', 'ENX', 'FGI', 'FSI', 'GER',\n", "        'HKG', 'ISE', 'IST', 'JKT', 'JNB', 'KLS', 'KOE', 'KSC', 'LIS',\n", "        'L<PERSON>', 'MC<PERSON>', 'MC<PERSON>', 'MEX', 'MI<PERSON>', 'NIM', 'NSI', 'NYB', 'NYS',\n", "        'NZE', 'OPI', 'OSA', 'OSL', 'PAR', 'PHS', 'PRA', 'RIS', 'SAO',\n", "        'SAU', 'SES', 'SET', 'SGO', 'SHH', 'SHZ', 'SNP', 'STO', 'STU',\n", "        'TAI', 'TAL', 'TLV', 'TSI', 'TWO', 'VAN', 'VIE', 'WCB', 'ZRH'],\n", "       dtype=object)}"]}, "execution_count": 15, "metadata": {}, "output_type": "execute_result"}], "source": ["indices = fd.Indices()\n", "\n", "indices.show_options()"]}, {"cell_type": "markdown", "id": "0c86e3cb", "metadata": {}, "source": ["And lastly, both the `search` and `to_toolkit` metrics also apply to each of the asset classes, using `fd.Funds()` and `fd.Cryptos()` respectively."]}, {"cell_type": "code", "execution_count": 16, "id": "072d64d8", "metadata": {}, "outputs": [{"data": {"text/html": ["<div>\n", "<style scoped>\n", "    .dataframe tbody tr th:only-of-type {\n", "        vertical-align: middle;\n", "    }\n", "\n", "    .dataframe tbody tr th {\n", "        vertical-align: top;\n", "    }\n", "\n", "    .dataframe thead th {\n", "        text-align: right;\n", "    }\n", "</style>\n", "<table border=\"1\" class=\"dataframe\">\n", "  <thead>\n", "    <tr style=\"text-align: right;\">\n", "      <th></th>\n", "      <th>name</th>\n", "      <th>currency</th>\n", "      <th>summary</th>\n", "      <th>category_group</th>\n", "      <th>category</th>\n", "      <th>family</th>\n", "      <th>exchange</th>\n", "    </tr>\n", "    <tr>\n", "      <th>symbol</th>\n", "      <th></th>\n", "      <th></th>\n", "      <th></th>\n", "      <th></th>\n", "      <th></th>\n", "      <th></th>\n", "      <th></th>\n", "    </tr>\n", "  </thead>\n", "  <tbody>\n", "    <tr>\n", "      <th>0P000015HA.F</th>\n", "      <td>Casermed Protecci&amp;#195;&amp;#179;n 6 PP</td>\n", "      <td>EUR</td>\n", "      <td>Casermed Protección 6 PP is a pension plan off...</td>\n", "      <td>Financials</td>\n", "      <td>Allocation</td>\n", "      <td>Sa Nostra Seguros de Vida SA</td>\n", "      <td>FRA</td>\n", "    </tr>\n", "    <tr>\n", "      <th>0P000015V5.F</th>\n", "      <td>BK Revalorizaci&amp;#195;&amp;#179;n Europa 2022 PP</td>\n", "      <td>EUR</td>\n", "      <td>BK Revalorización Europa 2022 PP is a pension ...</td>\n", "      <td>Financials</td>\n", "      <td><PERSON>s</td>\n", "      <td>Bankinter</td>\n", "      <td>FRA</td>\n", "    </tr>\n", "    <tr>\n", "      <th>0P000015VC.F</th>\n", "      <td>Bankia Protegido Renta 2023 PP</td>\n", "      <td>EUR</td>\n", "      <td>Bankia Protegido Renta 2023 PP is a protected ...</td>\n", "      <td>Financials</td>\n", "      <td><PERSON>s</td>\n", "      <td>Bankia F<PERSON>os</td>\n", "      <td>FRA</td>\n", "    </tr>\n", "    <tr>\n", "      <th>0P000017AE.F</th>\n", "      <td>Santander Universidades RF Mixta PP</td>\n", "      <td>EUR</td>\n", "      <td>Santander Universidades RF Mixta PP is a mixed...</td>\n", "      <td>Fixed Income</td>\n", "      <td><PERSON>s</td>\n", "      <td>Santander Asset Management SGIIC</td>\n", "      <td>FRA</td>\n", "    </tr>\n", "    <tr>\n", "      <th>0P000017AF.F</th>\n", "      <td>OpenBank Monetario PP</td>\n", "      <td>EUR</td>\n", "      <td>OpenBank Monetario PP is a monetary or money m...</td>\n", "      <td>Cash</td>\n", "      <td>Money Market Instruments</td>\n", "      <td>Santander Asset Management SGIIC</td>\n", "      <td>FRA</td>\n", "    </tr>\n", "    <tr>\n", "      <th>...</th>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "    </tr>\n", "    <tr>\n", "      <th>XXINTECB2P08.MX</th>\n", "      <td>Multifondo De Prevision 5 Banorte Generali Sie...</td>\n", "      <td>MXN</td>\n", "      <td>Multifondo De Prevision 5 Banorte Generali Sie...</td>\n", "      <td>Financials</td>\n", "      <td>Pension Plans</td>\n", "      <td>NaN</td>\n", "      <td>MEX</td>\n", "    </tr>\n", "    <tr>\n", "      <th>XXINTECB2P09.MX</th>\n", "      <td>Multifondo De Prevision 5 Banorte Generali Sie...</td>\n", "      <td>MXN</td>\n", "      <td>Multifondo De Prevision 5 Banorte Generali Sie...</td>\n", "      <td>Financials</td>\n", "      <td>Pension Plans</td>\n", "      <td>NaN</td>\n", "      <td>MEX</td>\n", "    </tr>\n", "    <tr>\n", "      <th>XXINTECB2P10.MX</th>\n", "      <td>Multifondo De Prevision 5 Banorte Generali Sie...</td>\n", "      <td>MXN</td>\n", "      <td>Multifondo De Prevision 5 Banorte Generali Sie...</td>\n", "      <td>Financials</td>\n", "      <td>Pension Plans</td>\n", "      <td>NaN</td>\n", "      <td>MEX</td>\n", "    </tr>\n", "    <tr>\n", "      <th>XXINTECB2V1.MX</th>\n", "      <td>Multifondo De Prevision 5 Banorte Generali Sie...</td>\n", "      <td>MXN</td>\n", "      <td>Multifondo De Prevision 5 Banorte Generali Sie...</td>\n", "      <td>Financials</td>\n", "      <td>Pension Plans</td>\n", "      <td>NaN</td>\n", "      <td>MEX</td>\n", "    </tr>\n", "    <tr>\n", "      <th>XXINTECB2V2.MX</th>\n", "      <td>Multifondo De Prevision 5 Banorte Generali Sie...</td>\n", "      <td>MXN</td>\n", "      <td>Multifondo De Prevision 5 Banorte Generali Sie...</td>\n", "      <td>Financials</td>\n", "      <td>Pension Plans</td>\n", "      <td>NaN</td>\n", "      <td>MEX</td>\n", "    </tr>\n", "  </tbody>\n", "</table>\n", "<p>625 rows × 7 columns</p>\n", "</div>"], "text/plain": ["                                                              name currency  \\\n", "symbol                                                                        \n", "0P000015HA.F                   Casermed Protecci&#195;&#179;n 6 PP      EUR   \n", "0P000015V5.F           BK Revalorizaci&#195;&#179;n Europa 2022 PP      EUR   \n", "0P000015VC.F                        Bankia Protegido Renta 2023 PP      EUR   \n", "0P000017AE.F                   Santander Universidades RF Mixta PP      EUR   \n", "0P000017AF.F                                 OpenBank Monetario PP      EUR   \n", "...                                                            ...      ...   \n", "XXINTECB2P08.MX  Multifondo De Prevision 5 Banorte Generali Sie...      MXN   \n", "XXINTECB2P09.MX  Multifondo De Prevision 5 Banorte Generali Sie...      MXN   \n", "XXINTECB2P10.MX  Multifondo De Prevision 5 Banorte Generali Sie...      MXN   \n", "XXINTECB2V1.MX   Multifondo De Prevision 5 Banorte Generali Sie...      MXN   \n", "XXINTECB2V2.MX   Multifondo De Prevision 5 Banorte Generali Sie...      MXN   \n", "\n", "                                                           summary  \\\n", "symbol                                                               \n", "0P000015HA.F     Casermed Protección 6 PP is a pension plan off...   \n", "0P000015V5.F     BK Revalorización Europa 2022 PP is a pension ...   \n", "0P000015VC.F     Bankia Protegido Renta 2023 PP is a protected ...   \n", "0P000017AE.F     Santander Universidades RF Mixta PP is a mixed...   \n", "0P000017AF.F     OpenBank Monetario PP is a monetary or money m...   \n", "...                                                            ...   \n", "XXINTECB2P08.MX  Multifondo De Prevision 5 Banorte Generali Sie...   \n", "XXINTECB2P09.MX  Multifondo De Prevision 5 Banorte Generali Sie...   \n", "XXINTECB2P10.MX  Multifondo De Prevision 5 Banorte Generali Sie...   \n", "XXINTECB2V1.MX   Multifondo De Prevision 5 Banorte Generali Sie...   \n", "XXINTECB2V2.MX   Multifondo De Prevision 5 Banorte Generali Sie...   \n", "\n", "                category_group                  category  \\\n", "symbol                                                     \n", "0P000015HA.F        Financials                Allocation   \n", "0P000015V5.F        Financials                     Bonds   \n", "0P000015VC.F        Financials                     Bonds   \n", "0P000017AE.F      Fixed Income                     Bonds   \n", "0P000017AF.F              Cash  Money Market Instruments   \n", "...                        ...                       ...   \n", "XXINTECB2P08.MX     Financials             Pension Plans   \n", "XXINTECB2P09.MX     Financials             Pension Plans   \n", "XXINTECB2P10.MX     Financials             Pension Plans   \n", "XXINTECB2V1.MX      Financials             Pension Plans   \n", "XXINTECB2V2.MX      Financials             Pension Plans   \n", "\n", "                                           family exchange  \n", "symbol                                                      \n", "0P000015HA.F         Sa Nostra Seguros de Vida SA      FRA  \n", "0P000015V5.F                            Bankinter      FRA  \n", "0P000015VC.F                        Bankia Fondos      FRA  \n", "0P000017AE.F     Santander Asset Management SGIIC      FRA  \n", "0P000017AF.F     Santander Asset Management SGIIC      FRA  \n", "...                                           ...      ...  \n", "XXINTECB2P08.MX                               NaN      MEX  \n", "XXINTECB2P09.MX                               NaN      MEX  \n", "XXINTECB2P10.MX                               NaN      MEX  \n", "XXINTECB2V1.MX                                NaN      MEX  \n", "XXINTECB2V2.MX                                NaN      MEX  \n", "\n", "[625 rows x 7 columns]"]}, "execution_count": 16, "metadata": {}, "output_type": "execute_result"}], "source": ["funds = fd.Funds()\n", "\n", "funds.search(summary=\"Pension\")"]}, {"cell_type": "code", "execution_count": 17, "id": "d1a556c1", "metadata": {}, "outputs": [{"name": "stderr", "output_type": "stream", "text": ["Obtaining historical data: 100%|██████████| 6/6 [00:00<00:00,  9.15it/s]\n"]}, {"name": "stdout", "output_type": "stream", "text": ["The following tickers acquired historical data from FinancialModelingPrep: ETH-USD, SPY\n", "The following tickers acquired historical data from YahooFinance: ETH-EUR, ETH-CAD, ETH-BTC, ETH-GBP\n"]}, {"data": {"text/html": ["<div>\n", "<style scoped>\n", "    .dataframe tbody tr th:only-of-type {\n", "        vertical-align: middle;\n", "    }\n", "\n", "    .dataframe tbody tr th {\n", "        vertical-align: top;\n", "    }\n", "\n", "    .dataframe thead tr th {\n", "        text-align: left;\n", "    }\n", "\n", "    .dataframe thead tr:last-of-type th {\n", "        text-align: right;\n", "    }\n", "</style>\n", "<table border=\"1\" class=\"dataframe\">\n", "  <thead>\n", "    <tr>\n", "      <th></th>\n", "      <th colspan=\"6\" halign=\"left\">Open</th>\n", "      <th colspan=\"4\" halign=\"left\">High</th>\n", "      <th>...</th>\n", "      <th colspan=\"4\" halign=\"left\">Excess Volatility</th>\n", "      <th colspan=\"6\" halign=\"left\">Cumulative Return</th>\n", "    </tr>\n", "    <tr>\n", "      <th></th>\n", "      <th>ETH-BTC</th>\n", "      <th>ETH-CAD</th>\n", "      <th>ETH-EUR</th>\n", "      <th>ETH-GBP</th>\n", "      <th>ETH-USD</th>\n", "      <th>Benchmark</th>\n", "      <th>ETH-BTC</th>\n", "      <th>ETH-CAD</th>\n", "      <th>ETH-EUR</th>\n", "      <th>ETH-GBP</th>\n", "      <th>...</th>\n", "      <th>ETH-EUR</th>\n", "      <th>ETH-GBP</th>\n", "      <th>ETH-USD</th>\n", "      <th>Benchmark</th>\n", "      <th>ETH-BTC</th>\n", "      <th>ETH-CAD</th>\n", "      <th>ETH-EUR</th>\n", "      <th>ETH-GBP</th>\n", "      <th>ETH-USD</th>\n", "      <th>Benchmark</th>\n", "    </tr>\n", "    <tr>\n", "      <th>Date</th>\n", "      <th></th>\n", "      <th></th>\n", "      <th></th>\n", "      <th></th>\n", "      <th></th>\n", "      <th></th>\n", "      <th></th>\n", "      <th></th>\n", "      <th></th>\n", "      <th></th>\n", "      <th></th>\n", "      <th></th>\n", "      <th></th>\n", "      <th></th>\n", "      <th></th>\n", "      <th></th>\n", "      <th></th>\n", "      <th></th>\n", "      <th></th>\n", "      <th></th>\n", "      <th></th>\n", "    </tr>\n", "  </thead>\n", "  <tbody>\n", "    <tr>\n", "      <th>2020Q1</th>\n", "      <td>0.0207</td>\n", "      <td>0.0207</td>\n", "      <td>0.0207</td>\n", "      <td>120.4332</td>\n", "      <td>132.3</td>\n", "      <td>260.56</td>\n", "      <td>0.0208</td>\n", "      <td>0.0208</td>\n", "      <td>0.0208</td>\n", "      <td>121.6618</td>\n", "      <td>...</td>\n", "      <td>0.2079</td>\n", "      <td>0.5395</td>\n", "      <td>0.558</td>\n", "      <td>0.2381</td>\n", "      <td>1.0</td>\n", "      <td>1.0</td>\n", "      <td>1.0</td>\n", "      <td>1.0</td>\n", "      <td>1.0</td>\n", "      <td>1.0</td>\n", "    </tr>\n", "    <tr>\n", "      <th>2020Q2</th>\n", "      <td>0.0248</td>\n", "      <td>0.0248</td>\n", "      <td>0.0248</td>\n", "      <td>202.6958</td>\n", "      <td>227.81</td>\n", "      <td>303.99</td>\n", "      <td>0.025</td>\n", "      <td>0.025</td>\n", "      <td>0.025</td>\n", "      <td>204.9717</td>\n", "      <td>...</td>\n", "      <td>0.1552</td>\n", "      <td>0.301</td>\n", "      <td>0.3305</td>\n", "      <td>0.1378</td>\n", "      <td>1.1981</td>\n", "      <td>1.1981</td>\n", "      <td>1.1981</td>\n", "      <td>1.6627</td>\n", "      <td>1.6943</td>\n", "      <td>1.2016</td>\n", "    </tr>\n", "    <tr>\n", "      <th>2020Q3</th>\n", "      <td>0.0332</td>\n", "      <td>0.0332</td>\n", "      <td>0.0332</td>\n", "      <td>306.6297</td>\n", "      <td>359.93</td>\n", "      <td>333.09</td>\n", "      <td>0.0335</td>\n", "      <td>0.0335</td>\n", "      <td>0.0335</td>\n", "      <td>307.8944</td>\n", "      <td>...</td>\n", "      <td>0.1982</td>\n", "      <td>0.2835</td>\n", "      <td>0.3022</td>\n", "      <td>0.0815</td>\n", "      <td>1.6135</td>\n", "      <td>1.6135</td>\n", "      <td>1.6135</td>\n", "      <td>2.5342</td>\n", "      <td>2.7043</td>\n", "      <td>1.3102</td>\n", "    </tr>\n", "    <tr>\n", "      <th>2020Q4</th>\n", "      <td>0.0261</td>\n", "      <td>0.0261</td>\n", "      <td>0.0261</td>\n", "      <td>611.1484</td>\n", "      <td>752.87</td>\n", "      <td>371.78</td>\n", "      <td>0.0258</td>\n", "      <td>0.0258</td>\n", "      <td>0.0258</td>\n", "      <td>613.3465</td>\n", "      <td>...</td>\n", "      <td>0.2275</td>\n", "      <td>0.3099</td>\n", "      <td>0.3143</td>\n", "      <td>0.0726</td>\n", "      <td>1.2271</td>\n", "      <td>1.2271</td>\n", "      <td>1.2271</td>\n", "      <td>4.9852</td>\n", "      <td>5.5431</td>\n", "      <td>1.469</td>\n", "    </tr>\n", "    <tr>\n", "      <th>2021Q1</th>\n", "      <td>0.0313</td>\n", "      <td>0.0313</td>\n", "      <td>0.0313</td>\n", "      <td>1574.7311</td>\n", "      <td>1841.03</td>\n", "      <td>395.34</td>\n", "      <td>0.0328</td>\n", "      <td>0.0328</td>\n", "      <td>0.0328</td>\n", "      <td>1660.7382</td>\n", "      <td>...</td>\n", "      <td>0.2946</td>\n", "      <td>0.4501</td>\n", "      <td>0.4599</td>\n", "      <td>0.0698</td>\n", "      <td>1.5749</td>\n", "      <td>1.5749</td>\n", "      <td>1.5749</td>\n", "      <td>13.5015</td>\n", "      <td>14.4274</td>\n", "      <td>1.5623</td>\n", "    </tr>\n", "    <tr>\n", "      <th>2021Q2</th>\n", "      <td>0.0603</td>\n", "      <td>0.0603</td>\n", "      <td>0.0603</td>\n", "      <td>1818.6083</td>\n", "      <td>2165.64</td>\n", "      <td>427.21</td>\n", "      <td>0.0653</td>\n", "      <td>0.0653</td>\n", "      <td>0.0653</td>\n", "      <td>1925.1167</td>\n", "      <td>...</td>\n", "      <td>0.3435</td>\n", "      <td>0.5638</td>\n", "      <td>0.5732</td>\n", "      <td>0.0505</td>\n", "      <td>3.1353</td>\n", "      <td>3.1353</td>\n", "      <td>3.1353</td>\n", "      <td>15.8344</td>\n", "      <td>17.1079</td>\n", "      <td>1.6929</td>\n", "    </tr>\n", "    <tr>\n", "      <th>2021Q3</th>\n", "      <td>0.0686</td>\n", "      <td>0.0686</td>\n", "      <td>0.0686</td>\n", "      <td>2458.9258</td>\n", "      <td>2849.71</td>\n", "      <td>436.02</td>\n", "      <td>0.0693</td>\n", "      <td>0.0693</td>\n", "      <td>0.0693</td>\n", "      <td>2630.7456</td>\n", "      <td>...</td>\n", "      <td>0.1915</td>\n", "      <td>0.3947</td>\n", "      <td>0.3984</td>\n", "      <td>0.0548</td>\n", "      <td>3.3092</td>\n", "      <td>3.3092</td>\n", "      <td>3.3092</td>\n", "      <td>21.4059</td>\n", "      <td>22.5463</td>\n", "      <td>1.7026</td>\n", "    </tr>\n", "    <tr>\n", "      <th>2021Q4</th>\n", "      <td>0.0787</td>\n", "      <td>0.0787</td>\n", "      <td>0.0787</td>\n", "      <td>3279.6384</td>\n", "      <td>3710.01</td>\n", "      <td>475.64</td>\n", "      <td>0.0786</td>\n", "      <td>0.0786</td>\n", "      <td>0.0786</td>\n", "      <td>3359.8831</td>\n", "      <td>...</td>\n", "      <td>0.1909</td>\n", "      <td>0.3013</td>\n", "      <td>0.3161</td>\n", "      <td>0.0627</td>\n", "      <td>3.8406</td>\n", "      <td>3.8406</td>\n", "      <td>3.8406</td>\n", "      <td>26.7237</td>\n", "      <td>27.621</td>\n", "      <td>1.891</td>\n", "    </tr>\n", "    <tr>\n", "      <th>2022Q1</th>\n", "      <td>0.0719</td>\n", "      <td>0.0719</td>\n", "      <td>0.0719</td>\n", "      <td>3033.0703</td>\n", "      <td>3386.69</td>\n", "      <td>457.89</td>\n", "      <td>0.0725</td>\n", "      <td>0.0725</td>\n", "      <td>0.0725</td>\n", "      <td>3093.5117</td>\n", "      <td>...</td>\n", "      <td>0.1503</td>\n", "      <td>0.3485</td>\n", "      <td>0.3592</td>\n", "      <td>0.0983</td>\n", "      <td>3.4831</td>\n", "      <td>3.4831</td>\n", "      <td>3.4831</td>\n", "      <td>24.4604</td>\n", "      <td>24.67</td>\n", "      <td>1.8038</td>\n", "    </tr>\n", "    <tr>\n", "      <th>2022Q2</th>\n", "      <td>0.0547</td>\n", "      <td>0.0547</td>\n", "      <td>0.0547</td>\n", "      <td>1052.4261</td>\n", "      <td>1099.09</td>\n", "      <td>376.24</td>\n", "      <td>0.0548</td>\n", "      <td>0.0548</td>\n", "      <td>0.0548</td>\n", "      <td>1057.0552</td>\n", "      <td>...</td>\n", "      <td>0.1598</td>\n", "      <td>0.3674</td>\n", "      <td>0.3712</td>\n", "      <td>0.1383</td>\n", "      <td>2.6039</td>\n", "      <td>2.6039</td>\n", "      <td>2.6039</td>\n", "      <td>8.4069</td>\n", "      <td>8.0409</td>\n", "      <td>1.5132</td>\n", "    </tr>\n", "    <tr>\n", "      <th>2022Q3</th>\n", "      <td>0.0682</td>\n", "      <td>0.0682</td>\n", "      <td>0.0682</td>\n", "      <td>1358.7999</td>\n", "      <td>1336.04</td>\n", "      <td>361.8</td>\n", "      <td>0.0681</td>\n", "      <td>0.0681</td>\n", "      <td>0.0681</td>\n", "      <td>1394.6359</td>\n", "      <td>...</td>\n", "      <td>0.2294</td>\n", "      <td>0.3951</td>\n", "      <td>0.4141</td>\n", "      <td>0.1104</td>\n", "      <td>3.2995</td>\n", "      <td>3.2995</td>\n", "      <td>3.2995</td>\n", "      <td>11.1822</td>\n", "      <td>9.9861</td>\n", "      <td>1.4386</td>\n", "    </tr>\n", "    <tr>\n", "      <th>2022Q4</th>\n", "      <td>0.0722</td>\n", "      <td>0.0722</td>\n", "      <td>0.0722</td>\n", "      <td>1118.1682</td>\n", "      <td>1199.19</td>\n", "      <td>381.5725</td>\n", "      <td>0.0725</td>\n", "      <td>0.0725</td>\n", "      <td>0.0725</td>\n", "      <td>1123.4692</td>\n", "      <td>...</td>\n", "      <td>0.1225</td>\n", "      <td>0.2972</td>\n", "      <td>0.3205</td>\n", "      <td>0.1106</td>\n", "      <td>3.4928</td>\n", "      <td>3.4928</td>\n", "      <td>3.4928</td>\n", "      <td>9.2088</td>\n", "      <td>8.9814</td>\n", "      <td>1.5457</td>\n", "    </tr>\n", "    <tr>\n", "      <th>2023Q1</th>\n", "      <td>0.0633</td>\n", "      <td>0.0633</td>\n", "      <td>0.0633</td>\n", "      <td>1653.9581</td>\n", "      <td>1793.68</td>\n", "      <td>404.66</td>\n", "      <td>0.0627</td>\n", "      <td>0.0627</td>\n", "      <td>0.0627</td>\n", "      <td>1686.7916</td>\n", "      <td>...</td>\n", "      <td>0.0992</td>\n", "      <td>0.226</td>\n", "      <td>0.2332</td>\n", "      <td>0.0838</td>\n", "      <td>3.0918</td>\n", "      <td>3.0918</td>\n", "      <td>3.0918</td>\n", "      <td>13.5695</td>\n", "      <td>13.6911</td>\n", "      <td>1.6628</td>\n", "    </tr>\n", "    <tr>\n", "      <th>2023Q2</th>\n", "      <td>0.0608</td>\n", "      <td>0.0608</td>\n", "      <td>0.0608</td>\n", "      <td>1674.5938</td>\n", "      <td>1851.98</td>\n", "      <td>441.44</td>\n", "      <td>0.061</td>\n", "      <td>0.061</td>\n", "      <td>0.061</td>\n", "      <td>1725.3853</td>\n", "      <td>...</td>\n", "      <td>0.0977</td>\n", "      <td>0.1556</td>\n", "      <td>0.1892</td>\n", "      <td>0.0533</td>\n", "      <td>2.9372</td>\n", "      <td>2.9372</td>\n", "      <td>2.9372</td>\n", "      <td>14.0673</td>\n", "      <td>14.5294</td>\n", "      <td>1.8071</td>\n", "    </tr>\n", "    <tr>\n", "      <th>2023Q3</th>\n", "      <td>0.0612</td>\n", "      <td>0.0612</td>\n", "      <td>0.0612</td>\n", "      <td>1564.9115</td>\n", "      <td>1667.2</td>\n", "      <td>429.9867</td>\n", "      <td>0.0621</td>\n", "      <td>0.0621</td>\n", "      <td>0.0621</td>\n", "      <td>1590.4768</td>\n", "      <td>...</td>\n", "      <td>0.0655</td>\n", "      <td>0.1453</td>\n", "      <td>0.1436</td>\n", "      <td>0.0552</td>\n", "      <td>2.9952</td>\n", "      <td>2.9952</td>\n", "      <td>2.9952</td>\n", "      <td>13.0116</td>\n", "      <td>12.5552</td>\n", "      <td>1.7486</td>\n", "    </tr>\n", "    <tr>\n", "      <th>2023Q4</th>\n", "      <td>0.0547</td>\n", "      <td>0.0547</td>\n", "      <td>0.0547</td>\n", "      <td>2080.7737</td>\n", "      <td>2291.51</td>\n", "      <td>474.325</td>\n", "      <td>0.0546</td>\n", "      <td>0.0546</td>\n", "      <td>0.0546</td>\n", "      <td>2100.3311</td>\n", "      <td>...</td>\n", "      <td>0.1599</td>\n", "      <td>0.2123</td>\n", "      <td>0.2186</td>\n", "      <td>0.0621</td>\n", "      <td>2.628</td>\n", "      <td>2.628</td>\n", "      <td>2.628</td>\n", "      <td>17.1119</td>\n", "      <td>17.1406</td>\n", "      <td>1.9469</td>\n", "    </tr>\n", "    <tr>\n", "      <th>2024Q1</th>\n", "      <td>0.0502</td>\n", "      <td>0.0502</td>\n", "      <td>0.0502</td>\n", "      <td>3251.6609</td>\n", "      <td>3507.31</td>\n", "      <td>523.675</td>\n", "      <td>0.0508</td>\n", "      <td>0.0508</td>\n", "      <td>0.0508</td>\n", "      <td>3301.8982</td>\n", "      <td>...</td>\n", "      <td>0.1565</td>\n", "      <td>0.2299</td>\n", "      <td>0.2614</td>\n", "      <td>0.0502</td>\n", "      <td>2.4348</td>\n", "      <td>2.4348</td>\n", "      <td>2.4348</td>\n", "      <td>26.8086</td>\n", "      <td>27.3931</td>\n", "      <td>2.1524</td>\n", "    </tr>\n", "    <tr>\n", "      <th>2024Q2</th>\n", "      <td>0.0559</td>\n", "      <td>0.0559</td>\n", "      <td>0.0559</td>\n", "      <td>3147.0488</td>\n", "      <td>3373.34</td>\n", "      <td>546.14</td>\n", "      <td>0.0558</td>\n", "      <td>0.0558</td>\n", "      <td>0.0558</td>\n", "      <td>3173.2053</td>\n", "      <td>...</td>\n", "      <td>0.144</td>\n", "      <td>0.2422</td>\n", "      <td>0.2988</td>\n", "      <td>0.0524</td>\n", "      <td>2.6763</td>\n", "      <td>2.6763</td>\n", "      <td>2.6763</td>\n", "      <td>25.9693</td>\n", "      <td>25.7918</td>\n", "      <td>2.2527</td>\n", "    </tr>\n", "    <tr>\n", "      <th>2024Q3</th>\n", "      <td>0.0406</td>\n", "      <td>0.0406</td>\n", "      <td>0.0406</td>\n", "      <td>2397.5129</td>\n", "      <td>2657.9</td>\n", "      <td>570.42</td>\n", "      <td>0.0407</td>\n", "      <td>0.0407</td>\n", "      <td>0.0407</td>\n", "      <td>2402.0461</td>\n", "      <td>...</td>\n", "      <td>0.1445</td>\n", "      <td>0.2879</td>\n", "      <td>0.2848</td>\n", "      <td>0.0748</td>\n", "      <td>1.9565</td>\n", "      <td>1.9565</td>\n", "      <td>1.9565</td>\n", "      <td>19.6518</td>\n", "      <td>19.5467</td>\n", "      <td>2.379</td>\n", "    </tr>\n", "    <tr>\n", "      <th>2024Q4</th>\n", "      <td>0.0358</td>\n", "      <td>0.0358</td>\n", "      <td>0.0358</td>\n", "      <td>3211.5027</td>\n", "      <td>3355.44</td>\n", "      <td>589.91</td>\n", "      <td>0.0365</td>\n", "      <td>0.0365</td>\n", "      <td>0.0365</td>\n", "      <td>3287.1143</td>\n", "      <td>...</td>\n", "      <td>0.1679</td>\n", "      <td>0.278</td>\n", "      <td>0.2818</td>\n", "      <td>0.0595</td>\n", "      <td>1.7488</td>\n", "      <td>1.7488</td>\n", "      <td>1.7488</td>\n", "      <td>26.6226</td>\n", "      <td>25.0249</td>\n", "      <td>2.4382</td>\n", "    </tr>\n", "    <tr>\n", "      <th>2025Q1</th>\n", "      <td>0.0221</td>\n", "      <td>0.0221</td>\n", "      <td>0.0221</td>\n", "      <td>1680.2089</td>\n", "      <td>1807.51</td>\n", "      <td>549.83</td>\n", "      <td>0.0222</td>\n", "      <td>0.0222</td>\n", "      <td>0.0222</td>\n", "      <td>1698.8374</td>\n", "      <td>...</td>\n", "      <td>0.1639</td>\n", "      <td>0.3157</td>\n", "      <td>0.3061</td>\n", "      <td>0.0768</td>\n", "      <td>1.058</td>\n", "      <td>1.058</td>\n", "      <td>1.058</td>\n", "      <td>13.7734</td>\n", "      <td>13.6912</td>\n", "      <td>2.3342</td>\n", "    </tr>\n", "    <tr>\n", "      <th>2025Q2</th>\n", "      <td>0.0218</td>\n", "      <td>0.0218</td>\n", "      <td>0.0218</td>\n", "      <td>1644.751</td>\n", "      <td>1816.4149</td>\n", "      <td>523.67</td>\n", "      <td>0.0217</td>\n", "      <td>0.0217</td>\n", "      <td>0.0217</td>\n", "      <td>1670.0551</td>\n", "      <td>...</td>\n", "      <td>0.1362</td>\n", "      <td>0.3737</td>\n", "      <td>0.3446</td>\n", "      <td>0.2157</td>\n", "      <td>1.0483</td>\n", "      <td>1.0483</td>\n", "      <td>1.0483</td>\n", "      <td>13.5542</td>\n", "      <td>13.5565</td>\n", "      <td>2.1557</td>\n", "    </tr>\n", "  </tbody>\n", "</table>\n", "<p>22 rows × 72 columns</p>\n", "</div>"], "text/plain": ["          Open                                                  High          \\\n", "       ETH-BTC ETH-CAD ETH-EUR   ETH-GBP   ETH-USD Benchmark ETH-BTC ETH-CAD   \n", "Date                                                                           \n", "2020Q1  0.0207  0.0207  0.0207  120.4332     132.3    260.56  0.0208  0.0208   \n", "2020Q2  0.0248  0.0248  0.0248  202.6958    227.81    303.99   0.025   0.025   \n", "2020Q3  0.0332  0.0332  0.0332  306.6297    359.93    333.09  0.0335  0.0335   \n", "2020Q4  0.0261  0.0261  0.0261  611.1484    752.87    371.78  0.0258  0.0258   \n", "2021Q1  0.0313  0.0313  0.0313 1574.7311   1841.03    395.34  0.0328  0.0328   \n", "2021Q2  0.0603  0.0603  0.0603 1818.6083   2165.64    427.21  0.0653  0.0653   \n", "2021Q3  0.0686  0.0686  0.0686 2458.9258   2849.71    436.02  0.0693  0.0693   \n", "2021Q4  0.0787  0.0787  0.0787 3279.6384   3710.01    475.64  0.0786  0.0786   \n", "2022Q1  0.0719  0.0719  0.0719 3033.0703   3386.69    457.89  0.0725  0.0725   \n", "2022Q2  0.0547  0.0547  0.0547 1052.4261   1099.09    376.24  0.0548  0.0548   \n", "2022Q3  0.0682  0.0682  0.0682 1358.7999   1336.04     361.8  0.0681  0.0681   \n", "2022Q4  0.0722  0.0722  0.0722 1118.1682   1199.19  381.5725  0.0725  0.0725   \n", "2023Q1  0.0633  0.0633  0.0633 1653.9581   1793.68    404.66  0.0627  0.0627   \n", "2023Q2  0.0608  0.0608  0.0608 1674.5938   1851.98    441.44   0.061   0.061   \n", "2023Q3  0.0612  0.0612  0.0612 1564.9115    1667.2  429.9867  0.0621  0.0621   \n", "2023Q4  0.0547  0.0547  0.0547 2080.7737   2291.51   474.325  0.0546  0.0546   \n", "2024Q1  0.0502  0.0502  0.0502 3251.6609   3507.31   523.675  0.0508  0.0508   \n", "2024Q2  0.0559  0.0559  0.0559 3147.0488   3373.34    546.14  0.0558  0.0558   \n", "2024Q3  0.0406  0.0406  0.0406 2397.5129    2657.9    570.42  0.0407  0.0407   \n", "2024Q4  0.0358  0.0358  0.0358 3211.5027   3355.44    589.91  0.0365  0.0365   \n", "2025Q1  0.0221  0.0221  0.0221 1680.2089   1807.51    549.83  0.0222  0.0222   \n", "2025Q2  0.0218  0.0218  0.0218  1644.751 1816.4149    523.67  0.0217  0.0217   \n", "\n", "                          ... Excess Volatility                            \\\n", "       ETH-EUR   ETH-GBP  ...           ETH-EUR ETH-GBP ETH-USD Benchmark   \n", "Date                      ...                                               \n", "2020Q1  0.0208  121.6618  ...            0.2079  0.5395   0.558    0.2381   \n", "2020Q2   0.025  204.9717  ...            0.1552   0.301  0.3305    0.1378   \n", "2020Q3  0.0335  307.8944  ...            0.1982  0.2835  0.3022    0.0815   \n", "2020Q4  0.0258  613.3465  ...            0.2275  0.3099  0.3143    0.0726   \n", "2021Q1  0.0328 1660.7382  ...            0.2946  0.4501  0.4599    0.0698   \n", "2021Q2  0.0653 1925.1167  ...            0.3435  0.5638  0.5732    0.0505   \n", "2021Q3  0.0693 2630.7456  ...            0.1915  0.3947  0.3984    0.0548   \n", "2021Q4  0.0786 3359.8831  ...            0.1909  0.3013  0.3161    0.0627   \n", "2022Q1  0.0725 3093.5117  ...            0.1503  0.3485  0.3592    0.0983   \n", "2022Q2  0.0548 1057.0552  ...            0.1598  0.3674  0.3712    0.1383   \n", "2022Q3  0.0681 1394.6359  ...            0.2294  0.3951  0.4141    0.1104   \n", "2022Q4  0.0725 1123.4692  ...            0.1225  0.2972  0.3205    0.1106   \n", "2023Q1  0.0627 1686.7916  ...            0.0992   0.226  0.2332    0.0838   \n", "2023Q2   0.061 1725.3853  ...            0.0977  0.1556  0.1892    0.0533   \n", "2023Q3  0.0621 1590.4768  ...            0.0655  0.1453  0.1436    0.0552   \n", "2023Q4  0.0546 2100.3311  ...            0.1599  0.2123  0.2186    0.0621   \n", "2024Q1  0.0508 3301.8982  ...            0.1565  0.2299  0.2614    0.0502   \n", "2024Q2  0.0558 3173.2053  ...             0.144  0.2422  0.2988    0.0524   \n", "2024Q3  0.0407 2402.0461  ...            0.1445  0.2879  0.2848    0.0748   \n", "2024Q4  0.0365 3287.1143  ...            0.1679   0.278  0.2818    0.0595   \n", "2025Q1  0.0222 1698.8374  ...            0.1639  0.3157  0.3061    0.0768   \n", "2025Q2  0.0217 1670.0551  ...            0.1362  0.3737  0.3446    0.2157   \n", "\n", "       Cumulative Return                                            \n", "                 ETH-BTC ETH-CAD ETH-EUR ETH-GBP ETH-USD Benchmark  \n", "Date                                                                \n", "2020Q1               1.0     1.0     1.0     1.0     1.0       1.0  \n", "2020Q2            1.1981  1.1981  1.1981  1.6627  1.6943    1.2016  \n", "2020Q3            1.6135  1.6135  1.6135  2.5342  2.7043    1.3102  \n", "2020Q4            1.2271  1.2271  1.2271  4.9852  5.5431     1.469  \n", "2021Q1            1.5749  1.5749  1.5749 13.5015 14.4274    1.5623  \n", "2021Q2            3.1353  3.1353  3.1353 15.8344 17.1079    1.6929  \n", "2021Q3            3.3092  3.3092  3.3092 21.4059 22.5463    1.7026  \n", "2021Q4            3.8406  3.8406  3.8406 26.7237  27.621     1.891  \n", "2022Q1            3.4831  3.4831  3.4831 24.4604   24.67    1.8038  \n", "2022Q2            2.6039  2.6039  2.6039  8.4069  8.0409    1.5132  \n", "2022Q3            3.2995  3.2995  3.2995 11.1822  9.9861    1.4386  \n", "2022Q4            3.4928  3.4928  3.4928  9.2088  8.9814    1.5457  \n", "2023Q1            3.0918  3.0918  3.0918 13.5695 13.6911    1.6628  \n", "2023Q2            2.9372  2.9372  2.9372 14.0673 14.5294    1.8071  \n", "2023Q3            2.9952  2.9952  2.9952 13.0116 12.5552    1.7486  \n", "2023Q4             2.628   2.628   2.628 17.1119 17.1406    1.9469  \n", "2024Q1            2.4348  2.4348  2.4348 26.8086 27.3931    2.1524  \n", "2024Q2            2.6763  2.6763  2.6763 25.9693 25.7918    2.2527  \n", "2024Q3            1.9565  1.9565  1.9565 19.6518 19.5467     2.379  \n", "2024Q4            1.7488  1.7488  1.7488 26.6226 25.0249    2.4382  \n", "2025Q1             1.058   1.058   1.058 13.7734 13.6912    2.3342  \n", "2025Q2            1.0483  1.0483  1.0483 13.5542 13.5565    2.1557  \n", "\n", "[22 rows x 72 columns]"]}, "execution_count": 17, "metadata": {}, "output_type": "execute_result"}], "source": ["cryptos = fd.Cryptos()\n", "\n", "eth_cryptos = cryptos.select(\n", "    cryptocurrency=\"ETH\"\n", ")\n", "\n", "cryptos_toolkit = eth_cryptos.to_toolkit(\n", "    api_key=API_KEY,\n", "    start_date=\"2020-01-01\"\n", ")\n", "\n", "cryptos_toolkit.get_historical_data(period=\"quarterly\")"]}], "metadata": {"kernelspec": {"display_name": "base", "language": "python", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.10.9"}}, "nbformat": 4, "nbformat_minor": 5}