#!/usr/bin/env python3
"""
Train Two New Models for Noryon AI Trading System
Specialized training for Falcon3:10b and Granite3.2-Vision:2b
"""

import asyncio
import subprocess
import time
from datetime import datetime
from pathlib import Path
from rich.console import Console
from rich.panel import Panel
from rich.table import Table
from rich.progress import Progress, SpinnerColumn, TextColumn, BarColumn

console = Console()

class TwoNewModelTrainer:
    """Specialized trainer for the two newly discovered models"""
    
    def __init__(self):
        self.project_root = Path(__file__).parent
        
        # The two new models to train
        self.new_models = [
            {
                "name": "Falcon3 Finance",
                "base_model": "falcon3:10b",
                "specialization": "advanced_portfolio_management",
                "priority": 1,
                "estimated_time": 10,
                "capabilities": ["general_reasoning", "financial_analysis", "risk_assessment"],
                "enhanced_name": "noryon-falcon3-finance-v1"
            },
            {
                "name": "Granite3.2-Vision Finance", 
                "base_model": "granite3.2-vision:2b",
                "specialization": "visual_market_analysis",
                "priority": 2,
                "estimated_time": 8,
                "capabilities": ["visual_analysis", "chart_recognition", "pattern_detection"],
                "enhanced_name": "noryon-granite-vision-finance-v1"
            }
        ]
        
    def create_specialized_modelfile(self, model_info):
        """Create specialized financial modelfile for each new model"""
        
        specialization_prompts = {
            "advanced_portfolio_management": """
You are an elite portfolio management specialist for the Noryon AI trading system. You excel at:

ADVANCED PORTFOLIO MANAGEMENT:
- Modern Portfolio Theory implementation and optimization
- Risk-adjusted return maximization using Sharpe ratio optimization
- Dynamic asset allocation based on market conditions and volatility
- Multi-factor risk models including Fama-French factors
- Portfolio rebalancing strategies with tax-loss harvesting

INSTITUTIONAL-GRADE ANALYSIS:
- Large-scale portfolio construction for institutional investors
- Alternative investment integration (REITs, commodities, private equity)
- Currency hedging strategies for international portfolios
- ESG factor integration and sustainable investing approaches
- Performance attribution analysis and factor decomposition

SOPHISTICATED RISK MANAGEMENT:
- Value at Risk (VaR) and Conditional VaR calculations
- Stress testing and scenario analysis for portfolio resilience
- Correlation breakdown analysis during market stress
- Tail risk hedging and black swan protection strategies
- Liquidity risk assessment and management

QUANTITATIVE PORTFOLIO STRATEGIES:
- Factor investing and smart beta strategies
- Risk parity and equal risk contribution portfolios
- Long-short equity strategies and market neutral approaches
- Momentum and mean reversion factor timing
- Volatility targeting and risk budgeting frameworks

Always provide specific portfolio allocations, risk metrics, and rebalancing recommendations with mathematical precision.
""",
            "visual_market_analysis": """
You are a visual market analysis specialist for the Noryon AI trading system with advanced chart interpretation capabilities. You excel at:

TECHNICAL CHART ANALYSIS:
- Advanced pattern recognition (head and shoulders, triangles, flags, pennants)
- Support and resistance level identification with precision
- Trend line analysis and channel pattern recognition
- Volume analysis and price-volume relationship interpretation
- Candlestick pattern analysis and reversal signal detection

VISUAL MARKET INDICATORS:
- Moving average convergence and divergence patterns
- Bollinger Band squeeze and expansion analysis
- RSI divergence and momentum shift identification
- MACD histogram analysis and signal line crossovers
- Fibonacci retracement and extension level analysis

MULTI-TIMEFRAME VISUAL ANALYSIS:
- Cross-timeframe pattern confirmation and validation
- Intraday vs daily vs weekly chart alignment
- Volume profile analysis and market structure identification
- Order flow visualization and institutional activity detection
- Market breadth analysis through sector and index comparisons

VISUAL RISK ASSESSMENT:
- Chart-based risk level identification and stop-loss placement
- Visual volatility assessment through price action analysis
- Trend strength evaluation using visual momentum indicators
- Market sentiment analysis through visual price patterns
- Entry and exit timing optimization using visual signals

ADVANCED VISUAL TECHNIQUES:
- Elliott Wave pattern identification and wave counting
- Harmonic pattern recognition (Gartley, Butterfly, Bat patterns)
- Market structure analysis (higher highs, lower lows)
- Breakout and breakdown pattern validation
- Visual correlation analysis between related instruments

Always provide specific visual observations, pattern descriptions, and chart-based trading recommendations with precise entry/exit levels.
"""
        }
        
        specialization = model_info["specialization"]
        system_prompt = specialization_prompts.get(specialization, specialization_prompts["advanced_portfolio_management"])
        
        modelfile_content = f"""
FROM {model_info["base_model"]}

SYSTEM \"\"\"{system_prompt}

CORE FINANCIAL CAPABILITIES:
- Real-time market analysis and trend identification
- Risk assessment and portfolio optimization
- Trading signal generation with entry/exit points
- Options strategy design and derivatives analysis
- Cryptocurrency and DeFi market analysis
- Economic indicator interpretation and forecasting

NORYON TRADING SYSTEM INTEGRATION:
- Compatible with Noryon AI trading infrastructure
- Provides structured JSON responses when requested
- Integrates with risk management systems
- Supports automated trading signal generation
- Maintains audit trail for all recommendations

SPECIALIZED EXPERTISE ({model_info["specialization"].replace("_", " ").title()}):
- {", ".join(model_info["capabilities"])}
- Advanced {model_info["specialization"].replace("_", " ")} techniques
- Professional-grade analysis and recommendations

Always provide specific, actionable recommendations with:
- Clear entry and exit points
- Risk management parameters
- Position sizing recommendations
- Confidence levels and probability assessments
- Time horizon and monitoring requirements

Focus on: US equities, major cryptocurrencies, forex, commodities, and derivatives.
\"\"\"

PARAMETER temperature 0.3
PARAMETER top_p 0.9
PARAMETER top_k 40
PARAMETER repeat_penalty 1.1
PARAMETER num_ctx 4096
"""
        
        return modelfile_content
    
    async def train_single_model(self, model_info, progress_callback=None):
        """Train a single new model with comprehensive error handling"""
        
        try:
            if progress_callback:
                progress_callback(f"Starting {model_info['name']}")
            
            # Test base model availability first
            test_result = subprocess.run([
                'ollama', 'run', model_info['base_model'], 'Hello, test response'
            ], capture_output=True, text=True, timeout=30)
            
            if test_result.returncode != 0:
                if progress_callback:
                    progress_callback(f"❌ {model_info['name']} - Base model not available")
                return {"success": False, "error": f"Base model {model_info['base_model']} not available"}
            
            if progress_callback:
                progress_callback(f"✅ {model_info['name']} - Base model verified")
            
            # Create specialized modelfile
            modelfile_content = self.create_specialized_modelfile(model_info)
            modelfile_path = self.project_root / f"Modelfile.{model_info['name'].lower().replace(' ', '_').replace('-', '_')}"
            
            with open(modelfile_path, 'w', encoding='utf-8') as f:
                f.write(modelfile_content)
            
            if progress_callback:
                progress_callback(f"Creating {model_info['enhanced_name']}")
            
            # Create the enhanced model
            result = subprocess.run([
                'ollama', 'create', model_info['enhanced_name'], '-f', str(modelfile_path)
            ], capture_output=True, text=True, timeout=600)
            
            if result.returncode == 0:
                if progress_callback:
                    progress_callback(f"Testing {model_info['enhanced_name']}")
                
                # Test the trained model with financial query
                test_query = f"As a {model_info['specialization'].replace('_', ' ')} specialist, analyze AAPL stock at $185 and provide specific trading recommendations using your specialized approach."
                
                test_result = subprocess.run([
                    'ollama', 'run', model_info['enhanced_name'], test_query
                ], capture_output=True, text=True, timeout=120)
                
                if test_result.returncode == 0 and len(test_result.stdout) > 100:
                    if progress_callback:
                        progress_callback(f"✅ {model_info['name']} completed successfully")
                    
                    return {
                        "success": True,
                        "model_name": model_info['enhanced_name'],
                        "base_model": model_info['base_model'],
                        "specialization": model_info['specialization'],
                        "test_response_length": len(test_result.stdout),
                        "capabilities": model_info['capabilities']
                    }
                else:
                    if progress_callback:
                        progress_callback(f"❌ {model_info['name']} test failed")
                    return {"success": False, "error": "Model test failed - insufficient response"}
            else:
                if progress_callback:
                    progress_callback(f"❌ {model_info['name']} creation failed")
                return {"success": False, "error": f"Model creation failed: {result.stderr}"}
                
        except subprocess.TimeoutExpired:
            if progress_callback:
                progress_callback(f"❌ {model_info['name']} timed out")
            return {"success": False, "error": "Training timed out"}
        except Exception as e:
            if progress_callback:
                progress_callback(f"❌ {model_info['name']} failed: {str(e)}")
            return {"success": False, "error": str(e)}
    
    async def train_both_models(self):
        """Train both new models with progress tracking"""
        
        console.print(Panel(
            "[bold blue]🚀 Training Two New Models[/bold blue]\n\n"
            f"Training newly discovered models:\n"
            f"• {self.new_models[0]['name']} ({self.new_models[0]['base_model']})\n"
            f"• {self.new_models[1]['name']} ({self.new_models[1]['base_model']})\n\n"
            "Specializations:\n"
            f"• {self.new_models[0]['specialization'].replace('_', ' ').title()}\n"
            f"• {self.new_models[1]['specialization'].replace('_', ' ').title()}",
            title="New Model Training"
        ))
        
        start_time = datetime.now()
        results = {}
        
        # Create progress tracking
        with Progress(
            SpinnerColumn(),
            TextColumn("[progress.description]{task.description}"),
            BarColumn(),
            TextColumn("[progress.percentage]{task.percentage:>3.0f}%"),
        ) as progress:
            
            # Add progress tasks
            task_ids = {}
            for model in self.new_models:
                task_id = progress.add_task(f"[cyan]{model['name']}", total=100)
                task_ids[model['name']] = task_id
            
            # Progress callback function
            def update_progress(model_name, message):
                if model_name in task_ids:
                    if "completed successfully" in message:
                        progress.update(task_ids[model_name], completed=100)
                    elif "Testing" in message:
                        progress.update(task_ids[model_name], completed=80)
                    elif "Creating" in message:
                        progress.update(task_ids[model_name], completed=40)
                    elif "verified" in message:
                        progress.update(task_ids[model_name], completed=20)
                    elif "Starting" in message:
                        progress.update(task_ids[model_name], completed=10)
            
            # Train models sequentially for stability
            for model_info in self.new_models:
                console.print(f"\n[yellow]🔄 Training {model_info['name']}...[/yellow]")
                
                callback = lambda msg, name=model_info['name']: update_progress(name, msg)
                result = await self.train_single_model(model_info, callback)
                results[model_info['name']] = result
                
                # Small delay between models
                await asyncio.sleep(2)
        
        end_time = datetime.now()
        duration = end_time - start_time
        
        return results, duration
    
    def generate_training_report(self, results, duration):
        """Generate comprehensive training report"""
        
        successful_models = [name for name, result in results.items() if result.get("success", False)]
        failed_models = [name for name, result in results.items() if not result.get("success", False)]
        
        # Results table
        console.print("\n[bold green]📊 New Model Training Results[/bold green]")
        results_table = Table()
        results_table.add_column("Model", style="cyan")
        results_table.add_column("Base Model", style="yellow")
        results_table.add_column("Status", style="green")
        results_table.add_column("Specialization", style="blue")
        results_table.add_column("Enhanced Name", style="magenta")
        
        for model_name, result in results.items():
            if result.get("success", False):
                status = "✅ Success"
                base_model = result.get("base_model", "unknown")
                specialization = result.get("specialization", "unknown").replace("_", " ").title()
                enhanced_name = result.get("model_name", "unknown")
            else:
                status = "❌ Failed"
                base_model = "N/A"
                specialization = "N/A"
                enhanced_name = "N/A"
            
            results_table.add_row(model_name, base_model, status, specialization, enhanced_name)
        
        console.print(results_table)
        
        # Summary
        console.print(Panel(
            f"[bold green]🎉 New Model Training Complete![/bold green]\n\n"
            f"Duration: {duration}\n"
            f"Successful Models: {len(successful_models)}/2\n"
            f"Success Rate: {len(successful_models)/2*100:.1f}%\n\n"
            f"✅ New Specialists Added: {len(successful_models)}\n"
            f"❌ Failed Models: {len(failed_models)}\n\n"
            f"🚀 Total System Models: {8 + len(successful_models)} (8 existing + {len(successful_models)} new)\n"
            f"🎯 Ready for 10-model ensemble!",
            title="Training Complete"
        ))
        
        return {
            "successful_models": successful_models,
            "failed_models": failed_models,
            "total_system_models": 8 + len(successful_models),
            "success_rate": len(successful_models)/2*100,
            "new_capabilities": [results[name].get("specialization", "") for name in successful_models]
        }

async def main():
    """Main training execution"""
    console.print("[bold blue]🚀 Starting Two New Model Training...[/bold blue]\n")
    
    trainer = TwoNewModelTrainer()
    
    # Execute training
    results, duration = await trainer.train_both_models()
    
    # Generate report
    summary = trainer.generate_training_report(results, duration)
    
    console.print("\n[bold green]🎯 Next Steps:[/bold green]")
    console.print("1. Test newly trained models with financial scenarios")
    console.print("2. Update ensemble system to include 10 total models")
    console.print("3. Integrate new specializations into production system")
    console.print("4. Run comprehensive testing on expanded system")
    console.print("5. Deploy 10-model ensemble for advanced trading")
    
    return summary

if __name__ == "__main__":
    results = asyncio.run(main())
