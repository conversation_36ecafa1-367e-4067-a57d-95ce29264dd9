{"builder_name": "parquet", "citation": "", "config_name": "default", "dataset_name": "oh_original_wo_dataforge_economics", "dataset_size": **********, "description": "", "download_checksums": {"hf://datasets/mlfoundations-dev/OH_original_wo_dataforge_economics@6a1e5210b1116dcd4ef6d5b8470acc66a786840b/data/train-00000-of-00004.parquet": {"num_bytes": 184499694, "checksum": null}, "hf://datasets/mlfoundations-dev/OH_original_wo_dataforge_economics@6a1e5210b1116dcd4ef6d5b8470acc66a786840b/data/train-00001-of-00004.parquet": {"num_bytes": 156234204, "checksum": null}, "hf://datasets/mlfoundations-dev/OH_original_wo_dataforge_economics@6a1e5210b1116dcd4ef6d5b8470acc66a786840b/data/train-00002-of-00004.parquet": {"num_bytes": 215795446, "checksum": null}, "hf://datasets/mlfoundations-dev/OH_original_wo_dataforge_economics@6a1e5210b1116dcd4ef6d5b8470acc66a786840b/data/train-00003-of-00004.parquet": {"num_bytes": 224224767, "checksum": null}}, "download_size": 780754111, "features": {"conversations": [{"from": {"dtype": "string", "_type": "Value"}, "value": {"dtype": "string", "_type": "Value"}, "weight": {"dtype": "float64", "_type": "Value"}}], "source_label_exact": {"feature": {"dtype": "string", "_type": "Value"}, "_type": "Sequence"}}, "homepage": "", "license": "", "size_in_bytes": **********, "splits": {"train": {"name": "train", "num_bytes": **********, "num_examples": 1000891, "shard_lengths": [312223, 344223, 300223, 44222], "dataset_name": "oh_original_wo_dataforge_economics"}}, "version": {"version_str": "0.0.0", "major": 0, "minor": 0, "patch": 0}}