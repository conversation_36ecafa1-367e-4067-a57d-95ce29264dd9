<img src="https://user-images.githubusercontent.com/46355364/220746807-669cdbc1-ac67-404c-b0bb-4a3d67d9931f.jpg" alt="Logo">

[![GitHub Sponsors](https://img.shields.io/badge/Sponsor_this_Project-grey?logo=github)](https://github.com/sponsors/JerBouma)
[![Buy Me a Coffee](https://img.shields.io/badge/Buy_Me_a_Coffee-grey?logo=buymeacoffee)](https://www.buymeacoffee.com/jerbouma)
[![LinkedIn](https://img.shields.io/badge/LinkedIn-grey?logo=Linkedin&logoColor=white)](https://www.linkedin.com/in/boumajeroen/)
[![Documentation](https://img.shields.io/badge/Documentation-grey?logo=readme)](https://www.jeroenbouma.com/projects/financedatabase)
[![Supported Python Versions](https://img.shields.io/pypi/pyversions/financedatabase)](https://pypi.org/project/financedatabase/)
[![PYPI Version](https://img.shields.io/pypi/v/financedatabase)](https://pypi.org/project/financedatabase/)
[![PYPI Downloads](https://static.pepy.tech/badge/financedatabase/month)](https://pepy.tech/project/financedatabase)

# Finance Database Parsers

This directory contains parsers for extracting financial data from various sources and formatting it for the FinanceDatabase. Each parser performs a specific role in data collection and processing.

___
> ### IMPORTANT NOTE
> These parsers are designed as a starting point for updating the Finance Database. **<u>A tremendous amount of curation happens after the parsers are done running</u>**. This includes correcting categories, removing invalid results, taking out undesired tickers and much more. While some components of the database are generated by these parsers, <u>a lot</u> of it is done manually before it ends up in the database.
___

Find an overview of each module below.

## Modules

- `category_controller.py`: Makes it possible to apply categorization e.g. for ETFs an Funds. The actual categoiries are handpicked.
- `codes_controller.py`: Assists in providing ISIN codes and similar although hardly works well.
- `desciptions_controller.py`: Provides descriptions for each instrument based on ticker and name.
- `industry_controller.py`: Determines the industry of a company based on ticker, name, summary, industry group and sector. Categorization is required to be verified before merging into the database, a pipeline will also prevent pushes to the database if it doesn't align with the sector and industry group.
- `industry_group_controller.py`: Determines the industry group of a company based on ticker, name, summary and sector. Categorization is required to be verified before merging into the database, a pipeline will also prevent pushes to the database if it doesn't align with the sector.
- `sectory_controller.py`: Determines the sector of a company based on ticker, name and summary. Categorization is required to be verified before merging into the database, a pipeline will also prevent pushes to the database if it doesn't align with the sector options.

