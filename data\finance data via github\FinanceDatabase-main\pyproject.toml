[tool.poetry]
name = "financedatabase"
version = "2.3.0"
description = "This is a database of 300.000+ symbols containing Equities, ETFs, Funds, Indices, Currencies, Cryptocurrencies and Money Markets."
license = "MIT"
authors = ["Jeroen Bouma"]
packages = [
    { include = "financedatabase" },
]
readme = "README.md"
homepage = "https://www.jeroenbouma.com/"
repository = "https://github.com/JerBouma/FinanceDatabase"
classifiers = [
    "Development Status :: 5 - Production/Stable",
    "Intended Audience :: Financial and Insurance Industry",
    "Topic :: Office/Business :: Financial :: Investment",
    "License :: OSI Approved :: MIT License",
    "Operating System :: OS Independent",
    "Programming Language :: Python :: 3.10",
    "Programming Language :: Python :: 3.11",
    "Programming Language :: Python :: 3.12",
    "Programming Language :: Python :: 3.13",
]

[tool.poetry.dependencies]
python = ">=3.10, <3.14"
financetoolkit = ">=2.0.0, <3.0.0"
pandas = {version = "^2.2", extras = ["computation", "performance", "plot", "excel"]}
requests = "^2.32.3"

[tool.poetry.group.dev.dependencies]
pytest = "^8.3.3"
pylint = "^3.3.0"
codespell = "^2.3.0"
black = "^25.1.0"
pytest-mock = "^3.14.0"
pytest-recording = "^0.13.2"
pytest-cov = "^5.0.0"
ruff = "^0.9.9"
pytest-timeout = "^2.3.1"
pytest-recorder = "^0.3.0"
ipykernel = "^6.29.5"
tqdm = "^4.67.1"

[build-system]
requires = ["setuptools<65.5.0", "poetry-core>=1.0.0"]
build-backend = "poetry.core.masonry.api"

[tool.ruff]
line-length = 122
lint.select = ["E", "W", "F", "Q", "W", "S", "UP", "I", "PD", "SIM", "PLC", "PLE", "PLR", "PLW"]
lint.ignore = ["S105", "S106", "S107", "PLR0913", "PLR0912", "PLR0911", "PLR0915", "PD010", "PD013", "S310", "S301"]
exclude = ["conftest.py", "examples", "compression"]

[tool.pylint]
max-line-length = 122
disable = [
    "R0913", # too-many-arguments
    "W1514", # using-import-outside-toplevel
    "R0911", # too-many-return-statements
    "R0912", # too-many-branches
    "R0915", # too-many-statements
    "R0801", # duplicate-code
    "W0221", # arguments-differ
    "C0103", # invalid-name
    "E1131", # unsupported-binary-operation
    "R0917", # too-many-positional-arguments
    "W0212", # protected-access
    "R0914", # too-many-locals
    "R1702", # too-many-nested-blocks
    ]

[tool.ruff.lint.isort]
combine-as-imports = true
force-wrap-aliases = true

[tool.lint.isort]
profile = "black"
line_length = 122
skip_gitignore = true
combine_as_imports = true

[tool.codespell]
ignore-words-list = 'te,hsi,amplitud,nam,tha,plaform, ois'
skip = '*.json,./.git,pyproject.toml,poetry.lock,examples'

[tool.mypy]
disable_error_code = "misc, valid-type, attr-defined, index"

[tool.pytest.ini_options]
filterwarnings = [
    "ignore::pytest.PytestAssertRewriteWarning:",
]