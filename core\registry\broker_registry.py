import os
import importlib
import inspect
import os
import yaml
from pathlib import Path
from typing import Dict, Type, List, Optional, Any
from dataclasses import dataclass
from datetime import datetime
import logging

from ..interfaces.broker_interface import UniversalBrokerInterface, AssetType, OrderType

logger = logging.getLogger(__name__)

@dataclass
class BrokerInfo:
    """Information about a registered broker"""
    name: str
    adapter_class: Type[UniversalBrokerInterface]
    config_path: str
    capabilities: List[str]
    asset_types: List[AssetType]
    order_types: List[OrderType]
    description: str
    version: str
    author: str
    is_active: bool = True
    last_health_check: Optional[datetime] = None
    health_status: str = "unknown"

class BrokerRegistry:
    """Central registry for all broker adapters"""
    
    def __init__(self, adapters_path: str = "adapters", config_path: str = "config/brokers"):
        self.adapters_path = Path(adapters_path)
        self.config_path = Path(config_path)
        self.brokers: Dict[str, BrokerInfo] = {}
        self.instances: Dict[str, UniversalBrokerInterface] = {}
        
        # Auto-discover brokers on initialization
        self.auto_discover_brokers()
    
    async def initialize(self) -> None:
        """Initialize the broker registry (async initialization tasks)"""
        logger.info("Initializing broker registry...")
        # Perform any async initialization tasks here
        # For now, just ensure auto-discovery has completed
        if not self.brokers:
            self.auto_discover_brokers()
        logger.info(f"Broker registry initialized with {len(self.brokers)} brokers")
    
    async def discover_brokers(self) -> List[str]:
        """Discover and return list of available brokers"""
        self.auto_discover_brokers()
        return self.list_active_brokers()
    
    def auto_discover_brokers(self) -> None:
        """Automatically discover and register broker adapters"""
        logger.info("Starting broker auto-discovery...")
        
        if not self.adapters_path.exists():
            logger.warning(f"Adapters path {self.adapters_path} does not exist")
            return
        
        # Scan for Python files in adapters directory
        for adapter_file in self.adapters_path.rglob("*.py"):
            if adapter_file.name.startswith("__"):
                continue
            
            try:
                self._load_broker_adapter(adapter_file)
            except Exception as e:
                logger.error(f"Failed to load adapter {adapter_file}: {e}")
        
        logger.info(f"Discovered {len(self.brokers)} broker adapters")
    
    def _load_broker_adapter(self, adapter_file: Path) -> None:
        """Load a single broker adapter from file"""
        # Convert file path to module path
        relative_path = adapter_file.relative_to(Path.cwd())
        module_path = str(relative_path.with_suffix('')).replace(os.sep, '.')
        
        try:
            # Import the module
            module = importlib.import_module(module_path)
            
            # Find classes that inherit from UniversalBrokerInterface
            for name, obj in inspect.getmembers(module, inspect.isclass):
                if (issubclass(obj, UniversalBrokerInterface) and 
                    obj != UniversalBrokerInterface and
                    not inspect.isabstract(obj)):
                    
                    # Get broker name from class or config
                    broker_name = getattr(obj, 'BROKER_NAME', name.lower().replace('adapter', ''))
                    
                    # Load broker configuration
                    config = self._load_broker_config(broker_name)
                    if config:
                        self._register_broker_from_config(broker_name, obj, config)
                    else:
                        logger.warning(f"No config found for broker {broker_name}")
        
        except Exception as e:
            logger.error(f"Error loading adapter from {adapter_file}: {e}")
    
    def _load_broker_config(self, broker_name: str) -> Optional[Dict[str, Any]]:
        """Load configuration for a broker"""
        config_file = self.config_path / f"{broker_name}.yaml"
        
        if not config_file.exists():
            # Try alternative naming
            config_file = self.config_path / f"{broker_name.replace('_', '-')}.yaml"
        
        if config_file.exists():
            try:
                with open(config_file, 'r') as f:
                    return yaml.safe_load(f)
            except Exception as e:
                logger.error(f"Error loading config for {broker_name}: {e}")
        
        return None
    
    def _register_broker_from_config(self, name: str, adapter_class: Type[UniversalBrokerInterface], config: Dict[str, Any]) -> None:
        """Register a broker using its configuration"""
        broker_config = config.get('broker', {})
        
        # Parse capabilities
        capabilities = broker_config.get('capabilities', [])
        asset_types = [AssetType(cap) for cap in capabilities if cap in [at.value for at in AssetType]]
        
        # Parse order types
        order_types_config = broker_config.get('order_types', [])
        order_types = [OrderType(ot) for ot in order_types_config if ot in [ot.value for ot in OrderType]]
        
        broker_info = BrokerInfo(
            name=name,
            adapter_class=adapter_class,
            config_path=str(self.config_path / f"{name}.yaml"),
            capabilities=capabilities,
            asset_types=asset_types,
            order_types=order_types,
            description=broker_config.get('description', f"{name} trading adapter"),
            version=broker_config.get('version', '1.0.0'),
            author=broker_config.get('author', 'Unknown')
        )
        
        self.brokers[name] = broker_info
        logger.info(f"Registered broker: {name}")
    
    def register_broker(self, name: str, adapter_class: Type[UniversalBrokerInterface], config: Dict[str, Any]) -> None:
        """Manually register a broker adapter"""
        self._register_broker_from_config(name, adapter_class, config)
    
    def get_broker_info(self, name: str) -> Optional[BrokerInfo]:
        """Get information about a registered broker"""
        return self.brokers.get(name)
    
    def get_broker_instance(self, name: str, credentials: Dict[str, str] = None) -> Optional[UniversalBrokerInterface]:
        """Get or create a broker instance"""
        if name not in self.brokers:
            logger.error(f"Broker {name} not found in registry")
            return None
        
        # Return existing instance if available
        if name in self.instances:
            return self.instances[name]
        
        # Create new instance
        try:
            broker_info = self.brokers[name]
            config = self._load_broker_config(name)
            
            if not config:
                logger.error(f"No configuration found for broker {name}")
                return None
            
            instance = broker_info.adapter_class(config)
            self.instances[name] = instance
            
            logger.info(f"Created instance for broker {name}")
            return instance
        
        except Exception as e:
            logger.error(f"Failed to create instance for broker {name}: {e}")
            return None
    
    def list_brokers(self) -> List[str]:
        """Get list of all registered broker names"""
        return list(self.brokers.keys())
    
    def list_active_brokers(self) -> List[str]:
        """Get list of active broker names"""
        return [name for name, info in self.brokers.items() if info.is_active]
    
    def get_brokers_by_asset_type(self, asset_type: AssetType) -> List[str]:
        """Get brokers that support a specific asset type"""
        return [
            name for name, info in self.brokers.items() 
            if asset_type in info.asset_types and info.is_active
        ]
    
    def get_brokers_by_capability(self, capability: str) -> List[str]:
        """Get brokers that support a specific capability"""
        return [
            name for name, info in self.brokers.items() 
            if capability in info.capabilities and info.is_active
        ]
    
    def supports_order_type(self, broker_name: str, order_type: OrderType) -> bool:
        """Check if a broker supports a specific order type"""
        broker_info = self.brokers.get(broker_name)
        return broker_info and order_type in broker_info.order_types
    
    def deactivate_broker(self, name: str) -> bool:
        """Deactivate a broker (mark as inactive)"""
        if name in self.brokers:
            self.brokers[name].is_active = False
            # Remove instance if exists
            if name in self.instances:
                del self.instances[name]
            logger.info(f"Deactivated broker {name}")
            return True
        return False
    
    def activate_broker(self, name: str) -> bool:
        """Activate a broker"""
        if name in self.brokers:
            self.brokers[name].is_active = True
            logger.info(f"Activated broker {name}")
            return True
        return False
    
    async def health_check_all(self) -> Dict[str, Dict[str, Any]]:
        """Perform health check on all active brokers"""
        results = {}
        
        for name in self.list_active_brokers():
            try:
                instance = self.get_broker_instance(name)
                if instance:
                    health = await instance.health_check()
                    results[name] = health
                    
                    # Update broker info
                    self.brokers[name].last_health_check = datetime.utcnow()
                    self.brokers[name].health_status = health.get('status', 'unknown')
                else:
                    results[name] = {
                        'status': 'error',
                        'error': 'Failed to create instance'
                    }
            except Exception as e:
                results[name] = {
                    'status': 'error',
                    'error': str(e)
                }
                logger.error(f"Health check failed for {name}: {e}")
        
        return results
    
    def get_registry_stats(self) -> Dict[str, Any]:
        """Get statistics about the registry"""
        total_brokers = len(self.brokers)
        active_brokers = len(self.list_active_brokers())
        
        asset_type_counts = {}
        for asset_type in AssetType:
            count = len(self.get_brokers_by_asset_type(asset_type))
            asset_type_counts[asset_type.value] = count
        
        return {
            'total_brokers': total_brokers,
            'active_brokers': active_brokers,
            'inactive_brokers': total_brokers - active_brokers,
            'asset_type_support': asset_type_counts,
            'brokers': {
                name: {
                    'active': info.is_active,
                    'capabilities': info.capabilities,
                    'health_status': info.health_status,
                    'last_health_check': info.last_health_check.isoformat() if info.last_health_check else None
                }
                for name, info in self.brokers.items()
            }
        }
    
    def export_registry_config(self) -> Dict[str, Any]:
        """Export registry configuration for backup/restore"""
        return {
            'brokers': {
                name: {
                    'adapter_class': f"{info.adapter_class.__module__}.{info.adapter_class.__name__}",
                    'config_path': info.config_path,
                    'capabilities': info.capabilities,
                    'description': info.description,
                    'version': info.version,
                    'author': info.author,
                    'is_active': info.is_active
                }
                for name, info in self.brokers.items()
            },
            'export_timestamp': datetime.utcnow().isoformat()
        }
    
    def clear_instances(self) -> None:
        """Clear all broker instances (force recreation)"""
        self.instances.clear()
        logger.info("Cleared all broker instances")
    
    def reload_broker(self, name: str) -> bool:
        """Reload a specific broker adapter"""
        if name in self.instances:
            del self.instances[name]
        
        if name in self.brokers:
            # Re-discover this specific broker
            try:
                config = self._load_broker_config(name)
                if config:
                    # The adapter class is already registered, just update config
                    self._register_broker_from_config(name, self.brokers[name].adapter_class, config)
                    logger.info(f"Reloaded broker {name}")
                    return True
            except Exception as e:
                logger.error(f"Failed to reload broker {name}: {e}")
        
        return False

# Global registry instance
_global_registry: Optional[BrokerRegistry] = None

def get_global_registry() -> BrokerRegistry:
    """Get the global broker registry instance"""
    global _global_registry
    if _global_registry is None:
        _global_registry = BrokerRegistry()
    return _global_registry

def set_global_registry(registry: BrokerRegistry) -> None:
    """Set the global broker registry instance"""
    global _global_registry
    _global_registry = registry