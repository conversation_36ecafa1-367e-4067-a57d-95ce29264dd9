OECD iLibrary (stats.oecd.org) API: No Separate “Keys” per Dataset
The OECD SDMX‑JSON API uses one public endpoint—you don’t register or obtain a new API key for each subject area. Instead, you:

Use the same base URL for all datasets
Specify different dataset IDs (e.g. “MEI” for Main Economic Indicators, “DP_LIVE” for regional statistics, etc.)
Pass dimension keys (location, subject, measure, etc.) in the URL
1. Base Endpoint
https://stats.oecd.org/SDMX-JSON/data
2. Discover Available Datasets
Fetch the list of dataset IDs (“dataflows”):

bash
Copy Code
curl "https://stats.oecd.org/SDMX-JSON/dataflow"
Look for entries like:
•  “MEI” – Main Economic Indicators
•  “EDU” – Education statistics
•  “LABOUR” – Labour force statistics
… and many more.

3. Query a Specific Dataset
General form:

GET https://stats.oecd.org/SDMX-JSON/data/{DATASET_ID}/{KEYS}/all
  ?startTime=YYYY&endTime=YYYY
• {DATASET_ID}: one of the codes from the dataflow list
• {KEYS}: dot‑separated dimension values in the order defined by the dataset
• all at the end tells the API “give me all remaining dimensions”

Example: Fetch annual CPI (measure CP00) for the US from 2010–2020 in the MEI dataset

bash
Copy Code
curl "https://stats.oecd.org/SDMX-JSON/data/MEI/USA.CP00+CPH/PT.A/all\
?startTime=2010&endTime=2020"
“MEI” is the dataset
“USA” = location, “CP00+CPH” = two CPI measures, “PT” = total, “A” = annual
startTime/endTime filter the time range
4. No API Key or Registration Needed
Unlike FRED (which requires you to register for an API key), the OECD iLibrary API is open. You simply:

Pick your dataset ID
Supply the dimension keys
(Optionally) add query parameters like startTime/endTime or detail=full
All calls go through the same endpoint.

Quick Steps for Your Developers
Call /dataflow to list dataset IDs.
Inspect the dataset’s structure via:
https://stats.oecd.org/SDMX-JSON/dataflow/{DATASET_ID}
Build your data URL using the pattern above.
Parse the returned JSON and ingest into your pipeline.