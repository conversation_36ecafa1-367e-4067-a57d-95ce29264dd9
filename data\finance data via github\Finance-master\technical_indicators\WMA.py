# Import dependencies
import numpy as np
import pandas as pd
import matplotlib.pyplot as plt
import yfinance as yf
import datetime as dt
yf.pdr_override()

# input
symbol = "AAPL"
start = dt.date.today() - dt.timedelta(days=365)
end = dt.date.today()

# Read data
df = yf.download(symbol, start, end)

def WMA(data, n):

    ws = np.zeros(data.shape[0])
    t_sum = sum(range(1, n + 1))

    for i in range(n - 1, data.shape[0]):
        ws[i] = sum(data[i - n + 1 : i + 1] * np.linspace(1, n, n)) / t_sum

    return ws

df["WMA"] = WMA(df["Adj Close"], 5)

# Line Chart
fig = plt.figure(figsize=(14, 7))
ax1 = plt.subplot(111)
ax1.plot(df.index, df["Adj Close"])
ax1.plot(df.index[4:], df["WMA"][4:])
ax1v = ax1.twinx()
ax1v.fill_between(df.index[0:], 0, df.Volume[0:], facecolor="#0079a3", alpha=0.4)
ax1v.axes.yaxis.set_ticklabels([])
ax1v.set_ylim(0, 3 * df.Volume.max())
ax1.set_title("Stock " + symbol + " Closing Price")
ax1.set_ylabel("Price")
ax1.set_xlabel("Date")
ax1.legend(loc="best")
plt.show()

# # Candlestick with WMA
from matplotlib import dates as mdates

dfc = df.copy()
dfc["VolumePositive"] = dfc["Open"] < dfc["Adj Close"]
# dfc = dfc.dropna()
dfc = dfc.reset_index()
dfc["Date"] = mdates.date2num(dfc["Date"].tolist())
dfc = dfc.iloc[4:]

from mplfinance.original_flavor import candlestick_ohlc

fig = plt.figure(figsize=(14, 7))
ax1 = plt.subplot(2, 1, 1)
candlestick_ohlc(ax1, dfc.values, width=0.5, colorup="g", colordown="r", alpha=1.0)
ax1.plot(dfc.Date, dfc["WMA"], label="Weighted Moving Average")
ax1.set_title("Stock " + symbol + " Closing Price")
ax1.set_ylabel("Price")
ax1.xaxis_date()
ax1.xaxis.set_major_formatter(mdates.DateFormatter("%d-%m-%Y"))
ax1.grid(True, which="both")
ax1.minorticks_on()
ax1v = ax1.twinx()
colors = dfc.VolumePositive.map({True: "g", False: "r"})
ax1v.bar(dfc.Date, dfc["Volume"], color=colors, alpha=0.4)
ax1v.axes.yaxis.set_ticklabels([])
ax1v.set_ylim(0, 3 * dfc.Volume.max())
ax1.set_title("Stock " + symbol + " Closing Price")
ax1.set_ylabel("Price")
ax1.set_xlabel("Date")
ax1.legend(loc="best")
plt.show()
