# Import dependencies
import numpy as np
import pandas as pd
import matplotlib.pyplot as plt
import yfinance as yf
import datetime as dt
yf.pdr_override()
import sys
import os
parent_dir = os.path.dirname(os.getcwd())
sys.path.append(parent_dir)
import ta_functions as ta

# input
symbol = "CRON"
start = dt.date.today() - dt.timedelta(days=365 * 2)
end = dt.date.today()

# Read data
df = yf.download(symbol, start, end)

n = 14  # Number of period
change = df["Adj Close"].diff(1)
df["Gain"] = change.mask(change < 0, 0)
df["Loss"] = abs(change.mask(change > 0, 0))
df["AVG_Gain"] = df.Gain.rolling(n).mean()
df["AVG_Loss"] = df.Loss.rolling(n).mean()
df["RS"] = df["AVG_Gain"] / df["AVG_Loss"]
df["RSI"] = 100 - (100 / (1 + df["RS"]))

# RSI
df["RSI_ta"] = ta.RSI(df["Adj Close"], timeperiod=14)

fig = plt.figure(figsize=(14, 7))
ax1 = plt.subplot(2, 1, 1)
ax1.plot(df["Adj Close"])
ax1.set_title(symbol + " Closing Price")
ax1.set_ylabel("Price")

ax2 = plt.subplot(2, 1, 2)
ax2.plot(df["RSI"], label="Relative Strengths Index")
ax2.text(s="Overbought", x=df.RSI.index[10], y=70, fontsize=12)
ax2.text(s="Oversold", x=df.RSI.index[10], y=30, fontsize=12)
ax2.axhline(y=70, color="red")
ax2.axhline(y=30, color="red")
ax2.grid()
ax2.set_ylabel("RSI")
ax2.set_xlabel("Date")
plt.show()

# ## Candlestick with RSI
from matplotlib import dates as mdates

dfc = df.copy()
dfc["VolumePositive"] = dfc["Open"] < dfc["Adj Close"]
# dfc = dfc.dropna()
dfc = dfc.reset_index()
dfc["Date"] = mdates.date2num(dfc["Date"].tolist())

from mplfinance.original_flavor import candlestick_ohlc

fig = plt.figure(figsize=(14, 7))
ax1 = plt.subplot(2, 1, 1)
candlestick_ohlc(ax1, dfc.values, width=0.5, colorup="g", colordown="r", alpha=1.0)
ax1.xaxis_date()
ax1.xaxis.set_major_formatter(mdates.DateFormatter("%d-%m-%Y"))
ax1.grid(True, which="both")
ax1.minorticks_on()
ax1v = ax1.twinx()
colors = dfc.VolumePositive.map({True: "g", False: "r"})
ax1v.bar(dfc.Date, dfc["Volume"], color=colors, alpha=0.4)
ax1v.axes.yaxis.set_ticklabels([])
ax1v.set_ylim(0, 3 * df.Volume.max())
ax1.set_title(symbol + " Closing Price")
ax1.set_ylabel("Price")

ax2 = plt.subplot(2, 1, 2)
ax2.plot(df["RSI"], label="Relative Strength Index")
ax2.text(s="Overbought", x=df.RSI.index[10], y=70, fontsize=12)
ax2.text(s="Oversold", x=df.RSI.index[10], y=30, fontsize=12)
ax2.axhline(y=70, color="red")
ax2.axhline(y=30, color="red")
ax2.grid()
ax2.set_ylabel("RSI")
ax2.set_xlabel("Date")
ax2.legend(loc="best")
plt.show()

fig = plt.figure(figsize=(14, 7))
ax1 = plt.subplot(2, 1, 1)
candlestick_ohlc(ax1, dfc.values, width=0.5, colorup="g", colordown="r", alpha=1.0)
ax1.xaxis_date()
ax1.xaxis.set_major_formatter(mdates.DateFormatter("%d-%m-%Y"))
ax1.grid(True, which="both")
ax1.minorticks_on()
ax1v = ax1.twinx()
colors = dfc.VolumePositive.map({True: "g", False: "r"})
ax1v.bar(dfc.Date, dfc["Volume"], color=colors, alpha=0.4)
ax1v.axes.yaxis.set_ticklabels([])
ax1v.set_ylim(0, 3 * df.Volume.max())
ax1.set_title(symbol + " Closing Price")
ax1.set_ylabel("Price")

ax2 = plt.subplot(2, 1, 2)
ax2.plot(df["RSI"], label="Relative Strength Index")
ax2.text(s="Overbought", x=df.RSI.index[10], y=70, fontsize=12)
ax2.text(s="Oversold", x=df.RSI.index[10], y=30, fontsize=12)
ax2.fill_between(df.index, y1=30, y2=70, color="#adccff", alpha=0.3)
ax2.axhline(y=70, color="red")
ax2.axhline(y=30, color="red")
ax2.grid()
ax2.set_ylabel("RSI")
ax2.set_xlabel("Date")
ax2.legend(loc="best")
plt.show()
