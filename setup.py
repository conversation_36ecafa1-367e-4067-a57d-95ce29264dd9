#!/usr/bin/env python3
"""
Noryon Trading AI System - Setup Script

This script helps users set up the Noryon Trading AI system quickly and easily.
"""

import os
import sys
import subprocess
import platform
from pathlib import Path


def print_banner():
    """Print the Noryon banner"""
    banner = """
    ███╗   ██╗ ██████╗ ██████╗ ██╗   ██╗ ██████╗ ███╗   ██╗
    ████╗  ██║██╔═══██╗██╔══██╗╚██╗ ██╔╝██╔═══██╗████╗  ██║
    ██╔██╗ ██║██║   ██║██████╔╝ ╚████╔╝ ██║   ██║██╔██╗ ██║
    ██║╚██╗██║██║   ██║██╔══██╗  ╚██╔╝  ██║   ██║██║╚██╗██║
    ██║ ╚████║╚██████╔╝██║  ██║   ██║   ╚██████╔╝██║ ╚████║
    ╚═╝  ╚═══╝ ╚═════╝ ╚═╝  ╚═╝   ╚═╝    ╚═════╝ ╚═╝  ╚═══╝
    
    🚀 Universal Trading AI System - Setup Script
    """
    print(banner)


def check_python_version():
    """Check if Python version is compatible"""
    print("🔍 Checking Python version...")
    
    if sys.version_info < (3, 9):
        print("❌ Error: Python 3.9 or higher is required.")
        print(f"   Current version: {sys.version}")
        print("   Please upgrade Python and try again.")
        sys.exit(1)
    
    print(f"✅ Python {sys.version.split()[0]} - Compatible")


def check_system_requirements():
    """Check system requirements"""
    print("\n🔍 Checking system requirements...")
    
    # Check operating system
    os_name = platform.system()
    print(f"   Operating System: {os_name} {platform.release()}")
    
    # Check available memory (basic check)
    try:
        import psutil
        memory_gb = psutil.virtual_memory().total / (1024**3)
        print(f"   Available Memory: {memory_gb:.1f} GB")
        
        if memory_gb < 4:
            print("⚠️  Warning: Less than 4GB RAM detected. AI models may run slowly.")
        elif memory_gb >= 8:
            print("✅ Sufficient memory for AI models")
    except ImportError:
        print("   Memory check skipped (psutil not available)")
    
    # Check disk space
    try:
        disk_usage = os.statvfs('.')
        free_space_gb = (disk_usage.f_frsize * disk_usage.f_bavail) / (1024**3)
        print(f"   Available Disk Space: {free_space_gb:.1f} GB")
        
        if free_space_gb < 5:
            print("⚠️  Warning: Less than 5GB disk space available.")
    except (AttributeError, OSError):
        # Windows doesn't have statvfs
        print("   Disk space check skipped")


def create_virtual_environment():
    """Create Python virtual environment"""
    print("\n🔧 Setting up virtual environment...")
    
    venv_path = Path("venv")
    
    if venv_path.exists():
        print("   Virtual environment already exists")
        return
    
    try:
        subprocess.run([sys.executable, "-m", "venv", "venv"], check=True)
        print("✅ Virtual environment created successfully")
    except subprocess.CalledProcessError as e:
        print(f"❌ Failed to create virtual environment: {e}")
        sys.exit(1)


def get_pip_command():
    """Get the appropriate pip command for the current platform"""
    if platform.system() == "Windows":
        return ["venv\\Scripts\\pip.exe"]
    else:
        return ["venv/bin/pip"]


def install_dependencies():
    """Install Python dependencies"""
    print("\n📦 Installing dependencies...")
    
    pip_cmd = get_pip_command()
    
    # Upgrade pip first
    try:
        subprocess.run(pip_cmd + ["install", "--upgrade", "pip"], check=True)
        print("   ✅ pip upgraded")
    except subprocess.CalledProcessError:
        print("   ⚠️  pip upgrade failed, continuing...")
    
    # Install requirements
    requirements_file = Path("requirements.txt")
    if not requirements_file.exists():
        print("❌ requirements.txt not found")
        sys.exit(1)
    
    try:
        print("   Installing packages (this may take a few minutes)...")
        subprocess.run(
            pip_cmd + ["install", "-r", "requirements.txt"],
            check=True,
            capture_output=False
        )
        print("✅ Dependencies installed successfully")
    except subprocess.CalledProcessError as e:
        print(f"❌ Failed to install dependencies: {e}")
        print("\n💡 Try installing manually:")
        if platform.system() == "Windows":
            print("   venv\\Scripts\\activate")
        else:
            print("   source venv/bin/activate")
        print("   pip install -r requirements.txt")
        sys.exit(1)


def create_directory_structure():
    """Create necessary directories"""
    print("\n📁 Creating directory structure...")
    
    directories = [
        "config/environments",
        "config/brokers",
        "config/risk",
        "config/ai",
        "config/credentials",
        "data",
        "logs",
        "models",
        "backups"
    ]
    
    for directory in directories:
        Path(directory).mkdir(parents=True, exist_ok=True)
        print(f"   ✅ {directory}")


def create_default_configs():
    """Create default configuration files"""
    print("\n⚙️  Creating default configuration files...")
    
    # Development environment config
    dev_config = """
# Noryon Trading AI - Development Environment Configuration

environment: development
debug: true
log_level: DEBUG

# Database settings
database:
  type: sqlite
  path: data/noryon_dev.db

# Redis settings (optional)
redis:
  host: localhost
  port: 6379
  db: 0

# API settings
api:
  host: 0.0.0.0
  port: 8000
  reload: true
"""
    
    dev_config_path = Path("config/environments/development.yaml")
    if not dev_config_path.exists():
        dev_config_path.write_text(dev_config)
        print("   ✅ development.yaml")
    
    # Trading configuration
    trading_config = """
# Noryon Trading AI - Trading Configuration

# Risk Management
max_position_size: 0.02      # 2% of account per position
max_daily_loss: 0.05         # 5% daily loss limit
max_drawdown: 0.10           # 10% maximum drawdown
risk_per_trade: 0.01         # 1% risk per trade
max_open_positions: 5        # Maximum concurrent positions

# Stop Loss & Take Profit
enable_stop_loss: true
enable_take_profit: true
default_stop_loss_pips: 50
default_take_profit_pips: 100

# Order Management
default_order_timeout: 300   # 5 minutes
max_slippage: 0.001         # 0.1%
enable_partial_fills: true

# Portfolio Management
rebalance_frequency: daily
max_correlation: 0.7
enable_hedging: false
"""
    
    trading_config_path = Path("config/risk/trading.yaml")
    if not trading_config_path.exists():
        trading_config_path.write_text(trading_config)
        print("   ✅ trading.yaml")
    
    # AI configuration
    ai_config = """
# Noryon Trading AI - AI Model Configuration

# Local Model Settings
local_model:
  name: qwen-2.5-7b
  path: models/qwen-2.5-7b
  device: auto  # auto, cpu, cuda
  max_tokens: 2048
  temperature: 0.7

# API Model Settings
api_models:
  openai:
    model: gpt-4
    max_tokens: 2048
    temperature: 0.7
  anthropic:
    model: claude-3-sonnet
    max_tokens: 2048
    temperature: 0.7

# Ensemble Settings
ensemble:
  enable: true
  confidence_threshold: 0.7
  voting_method: weighted  # simple, weighted, confidence
  min_models: 2

# Analysis Settings
analysis:
  technical_indicators: 50
  sentiment_analysis: true
  news_analysis: true
  social_media: false

# Performance Settings
max_api_calls_per_hour: 100
enable_caching: true
cache_ttl: 300  # 5 minutes
"""
    
    ai_config_path = Path("config/ai/models.yaml")
    if not ai_config_path.exists():
        ai_config_path.write_text(ai_config)
        print("   ✅ models.yaml")


def create_gitignore():
    """Create .gitignore file"""
    print("\n📝 Creating .gitignore...")
    
    gitignore_content = """
# Noryon Trading AI - Git Ignore File

# Python
__pycache__/
*.py[cod]
*$py.class
*.so
.Python
build/
develop-eggs/
dist/
downloads/
eggs/
.eggs/
lib/
lib64/
parts/
sdist/
var/
wheels/
*.egg-info/
.installed.cfg
*.egg
PIPFILE.lock

# Virtual Environment
venv/
env/
ENV/

# IDE
.vscode/
.idea/
*.swp
*.swo
*~

# Logs
logs/
*.log

# Data
data/
backups/

# Models
models/
!models/.gitkeep

# Configuration
config/credentials/
*.key
*.pem
*.p12

# Database
*.db
*.sqlite
*.sqlite3

# Environment Variables
.env
.env.local
.env.*.local

# OS
.DS_Store
.DS_Store?
._*
.Spotlight-V100
.Trashes
ehthumbs.db
Thumbs.db

# Temporary
*.tmp
*.temp
*.bak
*.backup

# Trading Data
*.csv
*.json
*.parquet

# API Keys and Secrets
api_keys.txt
secrets.txt
credentials.json
"""
    
    gitignore_path = Path(".gitignore")
    if not gitignore_path.exists():
        gitignore_path.write_text(gitignore_content)
        print("   ✅ .gitignore created")


def print_next_steps():
    """Print next steps for the user"""
    print("\n🎉 Setup completed successfully!")
    print("\n📋 Next Steps:")
    print("\n1. Activate the virtual environment:")
    
    if platform.system() == "Windows":
        print("   venv\\Scripts\\activate")
    else:
        print("   source venv/bin/activate")
    
    print("\n2. Run the setup wizard:")
    print("   python main.py --setup")
    
    print("\n3. Configure your brokers:")
    print("   - Add API credentials")
    print("   - Set trading parameters")
    print("   - Configure risk limits")
    
    print("\n4. Start the trading system:")
    print("   python main.py")
    
    print("\n📚 Documentation:")
    print("   - README.md - Complete system documentation")
    print("   - config/examples/ - Broker configuration examples")
    print("   - tests/ - Test suite and examples")
    
    print("\n🆘 Need Help?")
    print("   - GitHub Issues: https://github.com/your-org/noryon-trading-ai/issues")
    print("   - Discord: https://discord.gg/noryon")
    print("   - Email: <EMAIL>")
    
    print("\n⚠️  Important Security Notes:")
    print("   - Never commit API keys to version control")
    print("   - Use paper trading accounts for testing")
    print("   - Start with small position sizes")
    print("   - Always test thoroughly before live trading")


def main():
    """Main setup function"""
    print_banner()
    
    try:
        check_python_version()
        check_system_requirements()
        create_virtual_environment()
        install_dependencies()
        create_directory_structure()
        create_default_configs()
        create_gitignore()
        print_next_steps()
        
    except KeyboardInterrupt:
        print("\n\n❌ Setup interrupted by user")
        sys.exit(1)
    except Exception as e:
        print(f"\n\n❌ Setup failed with error: {e}")
        print("\nPlease check the error message and try again.")
        print("If the problem persists, please report it on GitHub.")
        sys.exit(1)


if __name__ == "__main__":
    main()