name: Run Tests

on:
    push:
        branches:
          - 'main'
    pull_request:
        branches:
          - 'main'
    workflow_dispatch:

jobs:
    test:
        runs-on: ubuntu-latest
        steps:
            - uses: actions/checkout@v3
            
            - name: Set up Python
              uses: actions/setup-python@v4
              with:
                python-version: '3.10'
                    
            - name: Install dependencies
              run: |
                    python -m pip install --upgrade pip
                    pip install pytest
                    if [ -f requirements.txt ]; then pip install -r requirements.txt; fi
                    
            - name: Run tests
              run: |
                    pytest tests/