# Import dependencies
import numpy as np
import pandas as pd
import matplotlib.pyplot as plt
import yfinance as yf
import datetime as dt
yf.pdr_override()

# input
symbol = "AAPL"
start = dt.date.today() - dt.timedelta(days=365 * 2)
end = dt.date.today()

# Read data
df = yf.download(symbol, start, end)

df["Upper_Channel_Line"] = pd.Series.rolling(df["High"], window=20).max()
df["Lower_Channel_Line"] = pd.Series.rolling(df["Low"], window=20).min()
df["Middle_Channel_Line"] = (df["Upper_Channel_Line"] + df["Lower_Channel_Line"]) / 2
df = df.dropna()

plt.figure(figsize=(16, 10))
plt.plot(df["Adj Close"])
plt.fill_between(
    df.index,
    df["Lower_Channel_Line"],
    df["Upper_Channel_Line"],
    color="lightblue",
    alpha=0.4,
)
plt.plot(df["Upper_Channel_Line"], c="darkred", linestyle="-", drawstyle="steps")
plt.plot(df["Lower_Channel_Line"], c="forestgreen", linestyle="-", drawstyle="steps")
plt.plot(df["Middle_Channel_Line"], c="blue", linestyle="-")
plt.title("Dochain Channel for Stock")
plt.legend(loc="best")
plt.xlabel("Price")
plt.ylabel("Date")
plt.show()

# ## Candlestick with Donchain Channel
from matplotlib import dates as mdates

dfc = df.copy()
dfc["VolumePositive"] = dfc["Open"] < dfc["Adj Close"]
# dfc = dfc.dropna()
dfc = dfc.reset_index()
dfc["Date"] = mdates.date2num(dfc["Date"].tolist())
from mplfinance.original_flavor import candlestick_ohlc

fig, ax1 = plt.subplots(figsize=(20, 12))
candlestick_ohlc(ax1, dfc.values, width=0.5, colorup="g", colordown="r", alpha=1.0)
# colors = ['red', 'green', 'blue']
# labels = ['Upper Channel Line', 'Lower Channel Line', 'Middle Channel Line']
for i in dfc[["Upper_Channel_Line", "Lower_Channel_Line", "Middle_Channel_Line"]]:
    ax1.plot(dfc["Date"], dfc[i])
ax1.xaxis_date()
ax1.xaxis.set_major_formatter(mdates.DateFormatter("%d-%m-%Y"))
ax1.grid(True, which="both")
ax1.minorticks_on()
ax1v = ax1.twinx()
colors = dfc.VolumePositive.map({True: "g", False: "r"})
ax1v.bar(dfc.Date, dfc["Volume"], color=colors, alpha=0.4)
ax1v.axes.yaxis.set_ticklabels([])
ax1v.set_ylim(0, 3 * df.Volume.max())
ax1.set_title("Stock " + symbol + " Closing Price")
ax1.set_ylabel("Price")
ax1.set_xlabel("Date")
ax1.legend(loc="best")
plt.show()
