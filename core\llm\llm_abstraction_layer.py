#!/usr/bin/env python3
"""
Noryon LLM Abstraction Layer

Unified interface for managing multiple LLM providers including:
- External APIs (OpenAI, Claude, DeepSeek, Gemini, etc.)
- Local models (Qwen, Llama, Mistral, etc.)
- Fine-tuned custom models

This layer provides:
- Provider-agnostic LLM interface
- Automatic failover and load balancing
- Cost optimization and usage tracking
- Response caching and rate limiting
- Model performance monitoring
- Support for both API and local inference
"""

import asyncio
import json
import logging
import time
from abc import ABC, abstractmethod
from dataclasses import dataclass, field
from datetime import datetime, timedelta
from enum import Enum
from typing import Dict, List, Optional, Any, Union, Callable, AsyncGenerator
import hashlib
import os
from pathlib import Path

import aiohttp
try:
    import redis
    REDIS_AVAILABLE = True
except ImportError:
    redis = None
    REDIS_AVAILABLE = False

# Optional prometheus_client import
try:
    from prometheus_client import Counter, Histogram, Gauge
    PROMETHEUS_AVAILABLE = True
except ImportError:
    Counter = Histogram = Gauge = None
    PROMETHEUS_AVAILABLE = False
import yaml

# Configure logging
logger = logging.getLogger(__name__)

# Metrics (conditional on prometheus availability)
if PROMETHEUS_AVAILABLE:
    llm_requests_total = Counter('llm_requests_total', 'Total LLM requests', ['provider', 'model'])
    llm_response_time = Histogram('llm_response_time_seconds', 'LLM response time', ['provider', 'model'])
    llm_tokens_used = Counter('llm_tokens_used_total', 'Total tokens used', ['provider', 'model', 'type'])
    llm_cost_total = Counter('llm_cost_total', 'Total LLM costs in USD', ['provider', 'model'])
    llm_errors_total = Counter('llm_errors_total', 'Total LLM errors', ['provider', 'model', 'error_type'])
    llm_cache_hits = Counter('llm_cache_hits_total', 'Total cache hits', ['provider'])
else:
    # Mock metrics when prometheus is not available
    class MockMetric:
        def inc(self, *args, **kwargs): pass
        def observe(self, *args, **kwargs): pass
        def set(self, *args, **kwargs): pass
        def labels(self, *args, **kwargs): return self
    
    llm_requests_total = MockMetric()
    llm_response_time = MockMetric()
    llm_tokens_used = MockMetric()
    llm_cost_total = MockMetric()
    llm_errors_total = MockMetric()
    llm_cache_hits = MockMetric()

class LLMProviderType(Enum):
    """Types of LLM providers"""
    API = "api"  # External API providers
    LOCAL = "local"  # Local inference servers
    CUSTOM = "custom"  # Custom fine-tuned models

class LLMProvider(Enum):
    """Supported LLM providers"""
    OPENAI = "openai"
    ANTHROPIC = "anthropic"
    DEEPSEEK = "deepseek"
    GEMINI = "gemini"
    GROQ = "groq"
    QWEN_LOCAL = "qwen_local"
    LLAMA_LOCAL = "llama_local"
    MISTRAL_LOCAL = "mistral_local"
    CUSTOM_LOCAL = "custom_local"

@dataclass
class LLMRequest:
    """Standardized LLM request format"""
    prompt: str
    system_prompt: Optional[str] = None
    max_tokens: Optional[int] = None
    temperature: float = 0.1
    top_p: float = 1.0
    frequency_penalty: float = 0.0
    presence_penalty: float = 0.0
    stop_sequences: Optional[List[str]] = None
    stream: bool = False
    metadata: Dict[str, Any] = field(default_factory=dict)
    priority: int = 1  # 1=highest, 5=lowest
    timeout: Optional[float] = None
    cache_ttl: Optional[int] = None  # Cache TTL in seconds

@dataclass
class LLMResponse:
    """Standardized LLM response format"""
    content: str
    provider: LLMProvider
    model: str
    tokens_used: Dict[str, int]  # {"input": X, "output": Y, "total": Z}
    cost: float  # Cost in USD
    latency: float  # Response time in seconds
    cached: bool = False
    metadata: Dict[str, Any] = field(default_factory=dict)
    timestamp: datetime = field(default_factory=datetime.now)
    request_id: Optional[str] = None

class LLMProviderInterface(ABC):
    """Abstract interface for all LLM providers"""
    
    def __init__(self, config: Dict[str, Any]):
        self.config = config
        self.provider_type = LLMProviderType(config.get('type', 'api'))
        self.enabled = config.get('enabled', True)
        self.rate_limiter = None
        self._setup_rate_limiting()
    
    def _setup_rate_limiting(self):
        """Setup rate limiting based on provider config"""
        rate_limit = self.config.get('rate_limit', {})
        if rate_limit:
            # Implementation would depend on chosen rate limiting library
            pass
    
    @abstractmethod
    async def generate(self, request: LLMRequest) -> LLMResponse:
        """Generate response from LLM"""
        pass
    
    @abstractmethod
    async def generate_stream(self, request: LLMRequest) -> AsyncGenerator[str, None]:
        """Generate streaming response from LLM"""
        pass
    
    @abstractmethod
    async def health_check(self) -> bool:
        """Check if provider is healthy and available"""
        pass
    
    def calculate_cost(self, tokens_used: Dict[str, int]) -> float:
        """Calculate cost based on token usage"""
        cost_config = self.config.get('cost_per_1k_tokens', {})
        input_cost = cost_config.get('input', 0.0)
        output_cost = cost_config.get('output', 0.0)
        
        total_cost = (
            (tokens_used.get('input', 0) / 1000) * input_cost +
            (tokens_used.get('output', 0) / 1000) * output_cost
        )
        return total_cost

class OpenAIProvider(LLMProviderInterface):
    """OpenAI API provider"""
    
    def __init__(self, config: Dict[str, Any]):
        super().__init__(config)
        self.api_key = config['api_key']
        self.base_url = config.get('base_url', 'https://api.openai.com/v1')
        self.model = config['model']
        self.session = None
    
    async def _get_session(self):
        if not self.session:
            self.session = aiohttp.ClientSession(
                headers={'Authorization': f'Bearer {self.api_key}'},
                timeout=aiohttp.ClientTimeout(total=self.config.get('timeout', 30))
            )
        return self.session
    
    async def generate(self, request: LLMRequest) -> LLMResponse:
        start_time = time.time()
        session = await self._get_session()
        
        messages = []
        if request.system_prompt:
            messages.append({"role": "system", "content": request.system_prompt})
        messages.append({"role": "user", "content": request.prompt})
        
        payload = {
            "model": self.model,
            "messages": messages,
            "max_tokens": request.max_tokens or self.config.get('max_tokens', 4000),
            "temperature": request.temperature,
            "top_p": request.top_p,
            "frequency_penalty": request.frequency_penalty,
            "presence_penalty": request.presence_penalty,
        }
        
        if request.stop_sequences:
            payload["stop"] = request.stop_sequences
        
        try:
            async with session.post(f"{self.base_url}/chat/completions", json=payload) as resp:
                if resp.status != 200:
                    error_text = await resp.text()
                    raise Exception(f"OpenAI API error {resp.status}: {error_text}")
                
                data = await resp.json()
                
                content = data['choices'][0]['message']['content']
                usage = data.get('usage', {})
                tokens_used = {
                    "input": usage.get('prompt_tokens', 0),
                    "output": usage.get('completion_tokens', 0),
                    "total": usage.get('total_tokens', 0)
                }
                
                latency = time.time() - start_time
                cost = self.calculate_cost(tokens_used)
                
                # Update metrics
                llm_requests_total.labels(provider='openai', model=self.model).inc()
                llm_response_time.labels(provider='openai', model=self.model).observe(latency)
                llm_tokens_used.labels(provider='openai', model=self.model, type='input').inc(tokens_used['input'])
                llm_tokens_used.labels(provider='openai', model=self.model, type='output').inc(tokens_used['output'])
                llm_cost_total.labels(provider='openai', model=self.model).inc(cost)
                
                return LLMResponse(
                    content=content,
                    provider=LLMProvider.OPENAI,
                    model=self.model,
                    tokens_used=tokens_used,
                    cost=cost,
                    latency=latency,
                    metadata=data
                )
        
        except Exception as e:
            llm_errors_total.labels(provider='openai', model=self.model, error_type=type(e).__name__).inc()
            logger.error(f"OpenAI API error: {e}")
            raise
    
    async def generate_stream(self, request: LLMRequest) -> AsyncGenerator[str, None]:
        # Implementation for streaming responses
        session = await self._get_session()
        
        messages = []
        if request.system_prompt:
            messages.append({"role": "system", "content": request.system_prompt})
        messages.append({"role": "user", "content": request.prompt})
        
        payload = {
            "model": self.model,
            "messages": messages,
            "max_tokens": request.max_tokens or self.config.get('max_tokens', 4000),
            "temperature": request.temperature,
            "stream": True
        }
        
        async with session.post(f"{self.base_url}/chat/completions", json=payload) as resp:
            async for line in resp.content:
                if line.startswith(b'data: '):
                    data_str = line[6:].decode('utf-8').strip()
                    if data_str == '[DONE]':
                        break
                    try:
                        data = json.loads(data_str)
                        if 'choices' in data and data['choices']:
                            delta = data['choices'][0].get('delta', {})
                            if 'content' in delta:
                                yield delta['content']
                    except json.JSONDecodeError:
                        continue
    
    async def health_check(self) -> bool:
        try:
            session = await self._get_session()
            async with session.get(f"{self.base_url}/models") as resp:
                return resp.status == 200
        except:
            return False

class LocalModelProvider(LLMProviderInterface):
    """Provider for locally hosted models (Qwen, Llama, etc.)"""
    
    def __init__(self, config: Dict[str, Any]):
        super().__init__(config)
        self.model_path = config['model_path']
        self.inference_url = config.get('inference_url', 'http://localhost:8000')
        self.model_name = config['model_name']
        self.session = None
    
    async def _get_session(self):
        if not self.session:
            self.session = aiohttp.ClientSession(
                timeout=aiohttp.ClientTimeout(total=self.config.get('timeout', 60))
            )
        return self.session
    
    async def generate(self, request: LLMRequest) -> LLMResponse:
        start_time = time.time()
        session = await self._get_session()
        
        # Format prompt for local model
        full_prompt = request.prompt
        if request.system_prompt:
            full_prompt = f"System: {request.system_prompt}\n\nUser: {request.prompt}"
        
        payload = {
            "prompt": full_prompt,
            "max_tokens": request.max_tokens or self.config.get('max_tokens', 4000),
            "temperature": request.temperature,
            "top_p": request.top_p,
            "stop": request.stop_sequences or []
        }
        
        try:
            async with session.post(f"{self.inference_url}/generate", json=payload) as resp:
                if resp.status != 200:
                    error_text = await resp.text()
                    raise Exception(f"Local model error {resp.status}: {error_text}")
                
                data = await resp.json()
                
                content = data.get('text', '')
                tokens_used = {
                    "input": data.get('input_tokens', 0),
                    "output": data.get('output_tokens', 0),
                    "total": data.get('total_tokens', 0)
                }
                
                latency = time.time() - start_time
                cost = 0.0  # Local models have no API cost
                
                # Update metrics
                provider_name = self.config.get('provider_name', 'local')
                llm_requests_total.labels(provider=provider_name, model=self.model_name).inc()
                llm_response_time.labels(provider=provider_name, model=self.model_name).observe(latency)
                llm_tokens_used.labels(provider=provider_name, model=self.model_name, type='input').inc(tokens_used['input'])
                llm_tokens_used.labels(provider=provider_name, model=self.model_name, type='output').inc(tokens_used['output'])
                
                return LLMResponse(
                    content=content,
                    provider=LLMProvider.QWEN_LOCAL,  # This should be dynamic based on config
                    model=self.model_name,
                    tokens_used=tokens_used,
                    cost=cost,
                    latency=latency,
                    metadata=data
                )
        
        except Exception as e:
            provider_name = self.config.get('provider_name', 'local')
            llm_errors_total.labels(provider=provider_name, model=self.model_name, error_type=type(e).__name__).inc()
            logger.error(f"Local model error: {e}")
            raise
    
    async def generate_stream(self, request: LLMRequest) -> AsyncGenerator[str, None]:
        # Implementation for streaming from local models
        session = await self._get_session()
        
        full_prompt = request.prompt
        if request.system_prompt:
            full_prompt = f"System: {request.system_prompt}\n\nUser: {request.prompt}"
        
        payload = {
            "prompt": full_prompt,
            "max_tokens": request.max_tokens or self.config.get('max_tokens', 4000),
            "temperature": request.temperature,
            "stream": True
        }
        
        async with session.post(f"{self.inference_url}/generate_stream", json=payload) as resp:
            async for line in resp.content:
                try:
                    data = json.loads(line.decode('utf-8'))
                    if 'text' in data:
                        yield data['text']
                except json.JSONDecodeError:
                    continue
    
    async def health_check(self) -> bool:
        try:
            session = await self._get_session()
            async with session.get(f"{self.inference_url}/health") as resp:
                return resp.status == 200
        except:
            return False

class LLMCache:
    """Redis-based caching for LLM responses"""
    
    def __init__(self, redis_url: str = "redis://localhost:6379", ttl: int = 3600):
        self.ttl = ttl
        self.redis_client = None
        self.enabled = False
        
        if REDIS_AVAILABLE:
            try:
                self.redis_client = redis.from_url(redis_url, decode_responses=True)
                # Test connection
                self.redis_client.ping()
                self.enabled = True
            except Exception as e:
                logger.warning(f"Redis connection failed, caching disabled: {e}")
                self.redis_client = None
                self.enabled = False
        else:
            logger.warning("Redis not available, caching disabled")
    
    def _generate_cache_key(self, request: LLMRequest, provider: str, model: str) -> str:
        """Generate cache key from request parameters"""
        cache_data = {
            'prompt': request.prompt,
            'system_prompt': request.system_prompt,
            'temperature': request.temperature,
            'max_tokens': request.max_tokens,
            'provider': provider,
            'model': model
        }
        cache_str = json.dumps(cache_data, sort_keys=True)
        return f"llm_cache:{hashlib.md5(cache_str.encode()).hexdigest()}"
    
    async def get(self, request: LLMRequest, provider: str, model: str) -> Optional[LLMResponse]:
        """Get cached response"""
        if not self.enabled or not request.cache_ttl:
            return None
        
        try:
            cache_key = self._generate_cache_key(request, provider, model)
            cached_data = self.redis_client.get(cache_key)
            
            if cached_data:
                data = json.loads(cached_data)
                response = LLMResponse(**data)
                response.cached = True
                llm_cache_hits.labels(provider=provider).inc()
                return response
        except Exception as e:
            logger.warning(f"Cache get error: {e}")
        
        return None
    
    async def set(self, request: LLMRequest, response: LLMResponse, provider: str, model: str):
        """Cache response"""
        if not request.cache_ttl:
            return
        
        try:
            cache_key = self._generate_cache_key(request, provider, model)
            # Convert response to dict for JSON serialization
            response_dict = {
                'content': response.content,
                'provider': response.provider.value,
                'model': response.model,
                'tokens_used': response.tokens_used,
                'cost': response.cost,
                'latency': response.latency,
                'metadata': response.metadata,
                'timestamp': response.timestamp.isoformat()
            }
            
            self.redis_client.setex(
                cache_key,
                request.cache_ttl,
                json.dumps(response_dict)
            )
        except Exception as e:
            logger.warning(f"Cache set error: {e}")

class LLMAbstractionLayer:
    """Main LLM abstraction layer managing all providers"""
    
    def __init__(self, config_path: str):
        self.config = self._load_config(config_path)
        self.providers: Dict[str, LLMProviderInterface] = {}
        self.cache = LLMCache(self.config.get('redis_url', 'redis://localhost:6379'))
        self.fallback_order = self.config.get('fallback_order', [])
        self._initialize_providers()
    
    def _load_config(self, config_path: str) -> Dict[str, Any]:
        """Load configuration from YAML file"""
        with open(config_path, 'r') as f:
            return yaml.safe_load(f)
    
    def _initialize_providers(self):
        """Initialize all configured providers"""
        providers_config = self.config.get('llm_providers', {})
        
        for provider_name, provider_config in providers_config.items():
            if not provider_config.get('enabled', True):
                continue
            
            try:
                if provider_name == 'openai':
                    self.providers[provider_name] = OpenAIProvider(provider_config)
                elif provider_name in ['qwen_local', 'llama_local', 'mistral_local', 'custom_local']:
                    self.providers[provider_name] = LocalModelProvider(provider_config)
                # Add more providers as needed
                
                logger.info(f"Initialized provider: {provider_name}")
            except Exception as e:
                logger.error(f"Failed to initialize provider {provider_name}: {e}")
    
    async def generate(self, 
                      request: LLMRequest, 
                      preferred_provider: Optional[str] = None,
                      use_fallback: bool = True) -> LLMResponse:
        """Generate response using specified or best available provider"""
        
        # Try cache first
        if preferred_provider and preferred_provider in self.providers:
            provider = self.providers[preferred_provider]
            cached_response = await self.cache.get(request, preferred_provider, provider.config.get('model', 'unknown'))
            if cached_response:
                return cached_response
        
        # Determine provider order
        provider_order = []
        if preferred_provider and preferred_provider in self.providers:
            provider_order.append(preferred_provider)
        
        if use_fallback:
            for fallback_provider in self.fallback_order:
                if fallback_provider not in provider_order and fallback_provider in self.providers:
                    provider_order.append(fallback_provider)
        
        # Try providers in order
        last_error = None
        for provider_name in provider_order:
            provider = self.providers[provider_name]
            
            try:
                # Check if provider is healthy
                if not await provider.health_check():
                    logger.warning(f"Provider {provider_name} failed health check")
                    continue
                
                response = await provider.generate(request)
                
                # Cache the response
                await self.cache.set(request, response, provider_name, provider.config.get('model', 'unknown'))
                
                return response
            
            except Exception as e:
                last_error = e
                logger.warning(f"Provider {provider_name} failed: {e}")
                continue
        
        # If all providers failed
        if last_error:
            raise last_error
        else:
            raise Exception("No available providers")
    
    async def generate_stream(self, 
                             request: LLMRequest, 
                             preferred_provider: Optional[str] = None) -> AsyncGenerator[str, None]:
        """Generate streaming response"""
        provider_name = preferred_provider or self.fallback_order[0]
        
        if provider_name not in self.providers:
            raise ValueError(f"Provider {provider_name} not available")
        
        provider = self.providers[provider_name]
        
        async for chunk in provider.generate_stream(request):
            yield chunk
    
    async def get_provider_status(self) -> Dict[str, bool]:
        """Get health status of all providers"""
        status = {}
        for provider_name, provider in self.providers.items():
            try:
                status[provider_name] = await provider.health_check()
            except:
                status[provider_name] = False
        return status
    
    def get_cost_estimate(self, request: LLMRequest, provider_name: str) -> float:
        """Estimate cost for a request"""
        if provider_name not in self.providers:
            return 0.0
        
        provider = self.providers[provider_name]
        
        # Rough token estimation (this could be improved with actual tokenization)
        estimated_input_tokens = len(request.prompt.split()) * 1.3  # Rough approximation
        estimated_output_tokens = request.max_tokens or 1000
        
        estimated_tokens = {
            'input': int(estimated_input_tokens),
            'output': int(estimated_output_tokens)
        }
        
        return provider.calculate_cost(estimated_tokens)
    
    async def close(self):
        """Close all provider sessions"""
        for provider in self.providers.values():
            if hasattr(provider, 'session') and provider.session:
                await provider.session.close()

# Example usage and testing functions
async def test_llm_abstraction():
    """Test function for the LLM abstraction layer"""
    # Initialize the abstraction layer
    llm = LLMAbstractionLayer('config/llm_brain_config.yaml')
    
    # Test request
    request = LLMRequest(
        prompt="Analyze the current market conditions for AAPL stock.",
        system_prompt="You are a professional financial analyst.",
        max_tokens=500,
        temperature=0.1,
        cache_ttl=300  # Cache for 5 minutes
    )
    
    try:
        # Test with preferred provider
        response = await llm.generate(request, preferred_provider='openai')
        print(f"Response: {response.content[:100]}...")
        print(f"Cost: ${response.cost:.4f}")
        print(f"Latency: {response.latency:.2f}s")
        print(f"Cached: {response.cached}")
        
        # Test provider status
        status = await llm.get_provider_status()
        print(f"Provider status: {status}")
        
    except Exception as e:
        print(f"Error: {e}")
    
    finally:
        await llm.close()

if __name__ == "__main__":
    asyncio.run(test_llm_abstraction())