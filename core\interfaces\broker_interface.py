from abc import ABC, abstractmethod
from typing import Dict, List, Optional, Any, Callable
from dataclasses import dataclass
from enum import Enum
from datetime import datetime

# Enums for standardized types
class OrderSide(Enum):
    BUY = "buy"
    SELL = "sell"

class OrderType(Enum):
    MARKET = "market"
    LIMIT = "limit"
    STOP = "stop"
    STOP_LIMIT = "stop_limit"
    TRAILING_STOP = "trailing_stop"

class OrderStatus(Enum):
    PENDING = "pending"
    FILLED = "filled"
    PARTIALLY_FILLED = "partially_filled"
    CANCELLED = "cancelled"
    REJECTED = "rejected"

class TimeInForce(Enum):
    GTC = "gtc"  # Good Till Cancelled
    IOC = "ioc"  # Immediate Or Cancel
    FOK = "fok"  # Fill Or Kill
    DAY = "day"  # Day Order

class AssetType(Enum):
    FOREX = "forex"
    CRYPTO = "crypto"
    STOCK = "stock"
    FUTURES = "futures"
    OPTIONS = "options"
    COMMODITY = "commodity"

# Universal Data Models
@dataclass
class UniversalTick:
    """Standardized tick data across all brokers"""
    symbol: str
    timestamp: datetime
    bid: float
    ask: float
    last: float
    volume: float
    asset_type: AssetType
    broker_name: str
    broker_specific: Dict[str, Any] = None
    
    @property
    def spread(self) -> float:
        return self.ask - self.bid
    
    @property
    def mid_price(self) -> float:
        return (self.bid + self.ask) / 2

@dataclass
class UniversalOrder:
    """Standardized order format across all brokers"""
    symbol: str
    side: OrderSide
    type: OrderType
    quantity: float
    price: Optional[float] = None
    stop_price: Optional[float] = None
    time_in_force: TimeInForce = TimeInForce.GTC
    client_order_id: Optional[str] = None
    broker_specific: Dict[str, Any] = None
    
    def validate(self) -> bool:
        """Validate order parameters"""
        if self.quantity <= 0:
            return False
        if self.type in [OrderType.LIMIT, OrderType.STOP_LIMIT] and self.price is None:
            return False
        if self.type in [OrderType.STOP, OrderType.STOP_LIMIT] and self.stop_price is None:
            return False
        return True

@dataclass
class OrderResult:
    """Result of order placement"""
    success: bool
    order_id: Optional[str] = None
    error_message: Optional[str] = None
    broker_response: Dict[str, Any] = None
    timestamp: datetime = None
    
    def __post_init__(self):
        if self.timestamp is None:
            self.timestamp = datetime.utcnow()

@dataclass
class Position:
    """Standardized position data"""
    symbol: str
    quantity: float
    average_price: float
    current_price: float
    unrealized_pnl: float
    realized_pnl: float
    asset_type: AssetType
    broker_name: str
    timestamp: datetime
    broker_specific: Dict[str, Any] = None
    
    @property
    def market_value(self) -> float:
        return self.quantity * self.current_price
    
    @property
    def is_long(self) -> bool:
        return self.quantity > 0
    
    @property
    def is_short(self) -> bool:
        return self.quantity < 0

@dataclass
class AccountInfo:
    """Standardized account information"""
    account_id: str
    balance: float
    equity: float
    margin_used: float
    margin_available: float
    currency: str
    broker_name: str
    timestamp: datetime
    broker_specific: Dict[str, Any] = None
    
    @property
    def margin_level(self) -> float:
        if self.margin_used == 0:
            return float('inf')
        return self.equity / self.margin_used

@dataclass
class MarketData:
    """Standardized market data"""
    symbol: str
    bid: float
    ask: float
    last: float
    volume: float
    high_24h: Optional[float] = None
    low_24h: Optional[float] = None
    change_24h: Optional[float] = None
    timestamp: datetime = None
    broker_specific: Dict[str, Any] = None
    
    def __post_init__(self):
        if self.timestamp is None:
            self.timestamp = datetime.utcnow()

@dataclass
class HistoricalData:
    """Standardized historical data"""
    symbol: str
    timeframe: str  # 1m, 5m, 1h, 1d, etc.
    data: List[Dict[str, Any]]  # OHLCV data
    start_time: datetime
    end_time: datetime
    broker_name: str

class DataStream:
    """Base class for real-time data streams"""
    def __init__(self, symbols: List[str], callback: Callable[[UniversalTick], None]):
        self.symbols = symbols
        self.callback = callback
        self.is_active = False
    
    def start(self):
        self.is_active = True
    
    def stop(self):
        self.is_active = False
    
    def add_symbol(self, symbol: str):
        if symbol not in self.symbols:
            self.symbols.append(symbol)
    
    def remove_symbol(self, symbol: str):
        if symbol in self.symbols:
            self.symbols.remove(symbol)

# Main Interface
class UniversalBrokerInterface(ABC):
    """Universal interface that all broker adapters must implement"""
    
    def __init__(self, config: Dict[str, Any]):
        self.config = config
        self.broker_name = config.get('name', 'unknown')
        self.is_connected = False
        self.capabilities = config.get('capabilities', [])
        self.rate_limits = config.get('rate_limits', {})
    
    @abstractmethod
    async def connect(self, credentials: Dict[str, str]) -> bool:
        """Establish connection to broker"""
        pass
    
    @abstractmethod
    async def disconnect(self) -> bool:
        """Close connection to broker"""
        pass
    
    @abstractmethod
    async def get_account_info(self) -> AccountInfo:
        """Get account information"""
        pass
    
    @abstractmethod
    async def get_positions(self) -> List[Position]:
        """Get all open positions"""
        pass
    
    @abstractmethod
    async def get_position(self, symbol: str) -> Optional[Position]:
        """Get position for specific symbol"""
        pass
    
    @abstractmethod
    async def get_market_data(self, symbol: str) -> MarketData:
        """Get current market data for symbol"""
        pass
    
    @abstractmethod
    async def get_historical_data(
        self, 
        symbol: str, 
        timeframe: str, 
        start_time: datetime, 
        end_time: datetime
    ) -> HistoricalData:
        """Get historical data"""
        pass
    
    @abstractmethod
    async def place_order(self, order: UniversalOrder) -> OrderResult:
        """Place a new order"""
        pass
    
    @abstractmethod
    async def cancel_order(self, order_id: str) -> bool:
        """Cancel an existing order"""
        pass
    
    @abstractmethod
    async def get_order_status(self, order_id: str) -> OrderStatus:
        """Get status of an order"""
        pass
    
    @abstractmethod
    async def get_open_orders(self) -> List[Dict[str, Any]]:
        """Get all open orders"""
        pass
    
    @abstractmethod
    async def subscribe_to_data(self, symbols: List[str], callback: Callable[[UniversalTick], None]) -> DataStream:
        """Subscribe to real-time data"""
        pass
    
    @abstractmethod
    async def get_supported_symbols(self) -> List[str]:
        """Get list of supported trading symbols"""
        pass
    
    # Optional methods with default implementations
    def supports_asset_type(self, asset_type: AssetType) -> bool:
        """Check if broker supports specific asset type"""
        return asset_type.value in self.capabilities
    
    def supports_order_type(self, order_type: OrderType) -> bool:
        """Check if broker supports specific order type"""
        supported_orders = self.config.get('order_types', [])
        return order_type.value in supported_orders
    
    def get_rate_limit(self, operation: str) -> Optional[int]:
        """Get rate limit for specific operation"""
        return self.rate_limits.get(operation)
    
    def validate_symbol(self, symbol: str) -> bool:
        """Validate if symbol is supported (override in adapter)"""
        return True
    
    def normalize_symbol(self, symbol: str) -> str:
        """Normalize symbol format for this broker (override in adapter)"""
        return symbol.upper()
    
    def denormalize_symbol(self, symbol: str) -> str:
        """Convert from broker format to universal format (override in adapter)"""
        return symbol
    
    async def health_check(self) -> Dict[str, Any]:
        """Check broker connection health"""
        try:
            account_info = await self.get_account_info()
            return {
                'status': 'healthy',
                'connected': self.is_connected,
                'broker': self.broker_name,
                'timestamp': datetime.utcnow(),
                'account_id': account_info.account_id if account_info else None
            }
        except Exception as e:
            return {
                'status': 'unhealthy',
                'connected': False,
                'broker': self.broker_name,
                'error': str(e),
                'timestamp': datetime.utcnow()
            }

# Error Classes
class BrokerError(Exception):
    """Base broker error"""
    def __init__(self, message: str, broker_name: str, error_code: str = None):
        self.broker_name = broker_name
        self.error_code = error_code
        super().__init__(f"[{broker_name}] {message}")

class ConnectionError(BrokerError):
    """Connection related errors"""
    pass

class AuthenticationError(BrokerError):
    """Authentication related errors"""
    pass

class InsufficientFundsError(BrokerError):
    """Insufficient funds for order"""
    pass

class InvalidSymbolError(BrokerError):
    """Invalid or unsupported symbol"""
    pass

class RateLimitError(BrokerError):
    """Rate limit exceeded"""
    pass

class OrderError(BrokerError):
    """Order related errors"""
    pass