# NORYONAI DATA MANIFEST
## Comprehensive Guide to 1.2TB+ Financial & AI Training Data

**Last Updated:** Agent Mode Analysis - Phase 0
**Total Estimated Size:** 1.2TB+

---

## 📊 CORE FINANCIAL MARKET DATA

### **High-Frequency Trading Data**
- **`paperswithbacktestStocks-1Min-Price/`** ⭐ **PRIMARY TRAINING DATA**
  - **Format:** Arrow files (5 files, ~1.9GB total)
  - **Content:** 1-minute OHLCV data for US stocks
  - **Structure:** train/ subdirectory with data-00000 to data-00004.arrow
  - **Use Case:** High-frequency trading model training, Phase 1 target
  - **Status:** ✅ Ready for ML training

### **Daily Market Data**
- **`paperswithbacktestStocks-Quarterly-BalanceSheet/`**
  - **Content:** Quarterly balance sheet data for stocks
  - **Use Case:** Fundamental analysis integration
  
- **`paperswithbacktestStocks-Quarterly-Earnings/`**
  - **Content:** Quarterly earnings reports and metrics
  - **Use Case:** Earnings-based trading strategies

- **`paperswithbacktestETFs-Daily-Price/`**
  - **Content:** Daily ETF price data
  - **Use Case:** ETF trading and sector analysis

- **`paperswithbacktestIndices-Daily-Price/`**
  - **Content:** Daily index price data (S&P 500, NASDAQ, etc.)
  - **Use Case:** Market regime detection, correlation analysis

- **`paperswithbacktestBonds-Daily-Price/`**
  - **Content:** Daily bond price data
  - **Use Case:** Fixed income trading, yield curve analysis

### **Forex & Currency Data**
- **`paperswithbacktestForex-Daily-Price/`**
  - **Content:** Daily forex pair prices
  - **Use Case:** Currency trading models
  
- **`forex data folder/`**
  - **Content:** Additional forex datasets
  - **Use Case:** Forex strategy development

### **Cryptocurrency Data**
- **`0xMakatrading-candles-subset-sc-format/`**
  - **Content:** Crypto candlestick data (smart contract format)
  - **Use Case:** DeFi and crypto trading

- **`0xMakatrading-candles-subset-qa-format/`**
  - **Content:** Crypto candlestick data (Q&A format)
  - **Use Case:** Crypto market analysis

- **`horenresearchsolana-pairs-history/`**
  - **Content:** Solana trading pair historical data
  - **Use Case:** Solana ecosystem trading

### **Specialized Financial Data**
- **`Kiceldaily-stocks/`**
  - **Content:** Daily stock data (alternative source)
  - **Use Case:** Cross-validation, data redundancy

- **`pmoe7SP_500_Stocks_Data-ratios_news_price_10_yrs/`**
  - **Content:** 10-year S&P 500 data with ratios and news
  - **Use Case:** Long-term analysis, news sentiment integration

### **News & Sentiment Data**
- **`paperswithbacktestAll-Daily-News/`**
  - **Content:** Daily market news aggregation
  - **Use Case:** News-based trading signals

- **`sp500_news_290k_articles.csv`** (38MB)
  - **Content:** 290,000 S&P 500 related news articles
  - **Use Case:** Sentiment analysis training

- **`NickyNickyfinance-financialmodelingprep-stock-news-sentiments-rss-feed/`**
  - **Content:** Stock news with sentiment scores
  - **Use Case:** Real-time sentiment integration

### **Fundamental Data**
- **`sp500_daily_ratios_20yrs.zip`** (13MB)
  - **Content:** 20 years of S&P 500 financial ratios
  - **Use Case:** Fundamental analysis models

---

## 🤖 LLM TRAINING & FINE-TUNING DATA

### **Finance-Specific LLM Data**
- **`BAAIIndustryCorpus_finance/`**
  - **Content:** Finance industry corpus for LLM training
  - **Use Case:** Qwen finance fine-tuning

- **`BAAIIndustryCorpus2_finance_economics/`**
  - **Content:** Extended finance and economics corpus
  - **Use Case:** Advanced financial reasoning training

- **`BAAIIndustryInstruction_Finance-Economics/`**
  - **Content:** Instruction-tuned finance and economics data
  - **Use Case:** Qwen instruction following for finance

- **`JosephgflowersFinance-Instruct-500k/`**
  - **Content:** 500k finance instruction examples
  - **Use Case:** Large-scale finance instruction tuning

- **`jan-hqfinance_mixed_50_binarized/`**
  - **Content:** High-quality finance data (binarized)
  - **Use Case:** Efficient finance model training

### **Reasoning & Mathematical Data**
- **`reasoningv1/`**, **`reasoningmix/`**, **`ourreasoningdata/`**
  - **Content:** Various reasoning datasets
  - **Use Case:** Enhancing Qwen's logical reasoning for trading

- **`openr1OpenR1Math220k/`**, **`openmathreasoning/`**
  - **Content:** Mathematical reasoning datasets
  - **Use Case:** Quantitative analysis capabilities

- **`deepmath103k/`**
  - **Content:** Deep mathematical problem solving
  - **Use Case:** Advanced quantitative reasoning

- **`ChristophSchuhmannbasic-math-problems-with-step-by-step-solutions/`**
  - **Content:** Step-by-step math solutions
  - **Use Case:** Teaching systematic problem solving

### **Advanced AI Training Data**
- **`MagpieAlignMagpieReasoningV1150KCoTQwQ/`**
- **`MagpieAlignMagpieReasoningV2250KCoTQwQ/`**
  - **Content:** Chain-of-thought reasoning data
  - **Use Case:** Advanced reasoning capabilities

- **`BAAIOpenSeekSyntheticReasoningData/`**
  - **Content:** Synthetic reasoning examples
  - **Use Case:** Augmenting reasoning training

- **`nvidiaLlama-NeMoTronPost-Training-Datase/`**
  - **Content:** NVIDIA's post-training dataset
  - **Use Case:** Advanced model alignment

### **General Instruction & Conversation Data**
- **`BAAIInfinityinstruct/`**
  - **Content:** Large-scale instruction dataset
  - **Use Case:** General instruction following

- **`allenaiTulu2SFT-mixture/`**
  - **Content:** Supervised fine-tuning mixture
  - **Use Case:** General capability enhancement

---

## 🏗️ SYSTEM & INFRASTRUCTURE DATA

### **Model Storage**
- **`models/`**
  - **Content:** Trained model checkpoints and parameters
  - **Use Case:** Model persistence and deployment

### **Processing & Cache**
- **`raw/`**, **`processed/`**, **`checkpoints/`**, **`parameters/`**
  - **Content:** Data processing pipeline artifacts
  - **Use Case:** Efficient data handling and caching

- **`hf_cache/`**
  - **Content:** HuggingFace model cache
  - **Use Case:** LLM model storage and loading

### **External Data Sources**
- **`imfdataapi/`**
  - **Content:** IMF economic data
  - **Use Case:** Macroeconomic analysis

- **`workldbankjsoncode/`**
  - **Content:** World Bank data
  - **Use Case:** Global economic indicators

- **`finance data via github/`**
  - **Content:** GitHub-sourced financial datasets
  - **Use Case:** Community-driven data sources

### **Specialized Datasets**
- **`BEE-spoke-dataconsumer-finance-complaints/`**
  - **Content:** Financial consumer complaints data
  - **Use Case:** Risk assessment, regulatory analysis

- **`sovaiinstitutional_trading/`**
  - **Content:** Institutional trading patterns
  - **Use Case:** Institutional behavior modeling

---

## 📋 RECOMMENDED USAGE PRIORITIES

### **Phase 1 (Immediate):**
1. **`paperswithbacktestStocks-1Min-Price/`** - Primary training target
2. **`sp500_news_290k_articles.csv`** - News sentiment integration
3. **`pmoe7SP_500_Stocks_Data-ratios_news_price_10_yrs/`** - Comprehensive analysis

### **Phase 2 (Qwen Fine-tuning):**
1. **`JosephgflowersFinance-Instruct-500k/`** - Large instruction set
2. **`BAAIIndustryInstruction_Finance-Economics/`** - Specialized instructions
3. **`reasoningv1/`** + **`reasoningmix/`** - Reasoning enhancement

### **Phase 3 (Expansion):**
1. **`paperswithbacktestForex-Daily-Price/`** - Forex markets
2. **`0xMakatrading-candles-subset-*`** - Crypto markets
3. **`paperswithbacktestETFs-Daily-Price/`** - ETF strategies

---

## ⚠️ IMPORTANT NOTES

- **File Formats:** Primarily Arrow, Parquet, CSV, and JSON
- **Size Considerations:** Some datasets are very large; use sampling for initial development
- **Data Quality:** Validate data integrity before training
- **Licensing:** Ensure compliance with data usage terms
- **Updates:** Some datasets may need periodic refreshing

---

**Status:** ✅ Manifest Complete - Ready for Phase 1 Implementation 