#!/usr/bin/env python3
"""
Noryon AI Trading System - Complete Model Training Pipeline

This script handles the training of all local AI models (Mi<PERSON><PERSON>, DeepSeek-R1, Qwen3)
with finance-specific data for autonomous trading decisions.

Usage:
    python train_all_models.py --parallel --gpu-count 2
    python train_all_models.py --model mistral --quick-train
    python train_all_models.py --validate-only
"""

import os
import sys
import json
import yaml
import argparse
import asyncio
import multiprocessing
from pathlib import Path
from typing import Dict, List, Optional, Tuple, Any
import logging
from datetime import datetime
import torch
from transformers import (
    AutoTokenizer, AutoModelForCausalLM, TrainingArguments, Trainer,
    DataCollatorForLanguageModeling, EarlyStoppingCallback
)
from datasets import Dataset, DatasetDict, load_dataset
from peft import LoraConfig, get_peft_model, TaskType
import pandas as pd
import numpy as np
from rich.console import Console
from rich.panel import Panel
from rich.progress import Progress, SpinnerColumn, TextColumn, BarColumn
from rich.table import Table
import mlflow
import mlflow.pytorch
from sklearn.model_selection import train_test_split

# Add project root to path
project_root = Path(__file__).parent
sys.path.insert(0, str(project_root))

# --- BEGIN SSL CERTIFICATE FIX ---
import os
import certifi # Ensure certifi is installed (often a dependency of requests/httpx)

try:
    cacert_path = certifi.where()
    os.environ['SSL_CERT_FILE'] = cacert_path
    os.environ['REQUESTS_CA_BUNDLE'] = cacert_path
    print(f"INFO: SSL_CERT_FILE and REQUESTS_CA_BUNDLE set to: {cacert_path}")
except ImportError:
    print("WARNING: certifi package not found. SSL fix cannot be applied. Please install it.")
except Exception as e:
    print(f"WARNING: Could not set SSL certificate paths using certifi: {e}")
# --- END SSL CERTIFICATE FIX ---

console = Console()

class FinanceModelTrainer:
    """Base class for training finance-specific AI models"""
    
    def __init__(self, model_name: str, output_dir: str, config: Dict[str, Any]):
        self.model_name = model_name
        self.output_dir = Path(output_dir)
        self.config = config
        self.device = torch.device("cuda" if torch.cuda.is_available() else "cpu")
        
        # Setup logging
        logging.basicConfig(level=logging.INFO)
        self.logger = logging.getLogger(f"trainer_{model_name.split('/')[-1]}")
        
        # Create output directory
        self.output_dir.mkdir(parents=True, exist_ok=True)
        
        # Initialize MLflow
        mlflow.set_tracking_uri("file:./mlruns")
        mlflow.set_experiment(f"noryon_finance_{model_name.split('/')[-1]}")
    
    def load_finance_datasets(self) -> Dataset:
        """Load and combine all finance datasets"""
        datasets = []
        
        # Load various finance datasets
        dataset_paths = [
            "data/JosephgflowersFinance-Instruct-500k",
            "data/BAAIIndustryCorpus_finance",
            "data/BAAIIndustryInstruction_Finance-Economics",
            "data/sovaiinstitutional_trading",
            "data/jan-hqfinance_mixed_50_binarized"
        ]
        
        for dataset_path in dataset_paths:
            full_path = project_root / dataset_path
            if full_path.exists():
                try:
                    dataset = load_dataset(str(full_path))
                    if isinstance(dataset, DatasetDict):
                        datasets.append(dataset['train'])
                    else:
                        datasets.append(dataset)
                    self.logger.info(f"Loaded dataset: {dataset_path}")
                except Exception as e:
                    self.logger.warning(f"Failed to load {dataset_path}: {e}")
        
        # Load CSV datasets
        csv_files = [
            "data/sp500_news_290k_articles.csv",
            "data/paperswithbacktestStocks-Daily-Price",
            "data/paperswithbacktestForex-Daily-Price"
        ]
        
        for csv_file in csv_files:
            csv_path = project_root / csv_file
            if csv_path.exists() and csv_path.suffix == '.csv':
                try:
                    df = pd.read_csv(csv_path)
                    # Convert to instruction format
                    formatted_data = self.format_csv_to_instructions(df, csv_file)
                    datasets.append(Dataset.from_pandas(formatted_data))
                    self.logger.info(f"Loaded CSV dataset: {csv_file}")
                except Exception as e:
                    self.logger.warning(f"Failed to load {csv_file}: {e}")
        
        # Combine all datasets
        if datasets:
            combined_dataset = datasets[0]
            for dataset in datasets[1:]:
                try:
                    combined_dataset = combined_dataset.concatenate(dataset)
                except Exception as e:
                    self.logger.warning(f"Failed to concatenate dataset: {e}")
            
            self.logger.info(f"Combined dataset size: {len(combined_dataset)}")
            return combined_dataset
        else:
            raise ValueError("No datasets found to train on")
    
    def format_csv_to_instructions(self, df: pd.DataFrame, source: str) -> pd.DataFrame:
        """Convert CSV data to instruction-following format"""
        instructions = []
        
        if "news" in source.lower():
            # Format news data
            for _, row in df.iterrows():
                if 'headline' in row and 'content' in row:
                    instruction = f"Analyze this financial news and provide trading insights: {row['headline']}"
                    response = f"Based on the news '{row['headline']}', here's the analysis: {row.get('content', '')[:500]}..."
                    instructions.append({
                        'instruction': instruction,
                        'input': '',
                        'output': response
                    })
        
        elif "price" in source.lower():
            # Format price data
            for _, row in df.iterrows():
                if 'symbol' in row and 'close' in row:
                    instruction = f"Analyze the price movement for {row['symbol']} and suggest trading action."
                    response = f"For {row['symbol']}, current price is {row['close']}. Based on technical analysis..."
                    instructions.append({
                        'instruction': instruction,
                        'input': '',
                        'output': response
                    })
        
        return pd.DataFrame(instructions)
    
    def create_training_prompts(self, dataset: Dataset) -> Dataset:
        """Create training prompts in the correct format"""
        def format_prompt(example):
            if 'instruction' in example and 'output' in example:
                # Alpaca format
                prompt = f"### Instruction:\n{example['instruction']}\n\n"
                if example.get('input', ''):
                    prompt += f"### Input:\n{example['input']}\n\n"
                prompt += f"### Response:\n{example['output']}"
            elif 'question' in example and 'answer' in example:
                # Q&A format
                prompt = f"### Question:\n{example['question']}\n\n### Answer:\n{example['answer']}"
            elif 'text' in example:
                # Raw text format
                prompt = example['text']
            else:
                # Fallback
                prompt = str(example)
            
            return {'text': prompt}
        
        return dataset.map(format_prompt)
    
    def setup_lora_config(self) -> LoraConfig:
        """Setup LoRA configuration for efficient fine-tuning"""
        return LoraConfig(
            task_type=TaskType.CAUSAL_LM,
            inference_mode=False,
            r=16,
            lora_alpha=32,
            lora_dropout=0.1,
            target_modules=["q_proj", "v_proj", "k_proj", "o_proj", "gate_proj", "up_proj", "down_proj"]
        )
    
    def train_model(self, dataset: Dataset) -> str:
        """Train the model with the given dataset"""
        with mlflow.start_run(run_name=f"{self.model_name.split('/')[-1]}_training"):
            # Log parameters
            mlflow.log_params(self.config)
            
            # Load tokenizer and model
            self.logger.info(f"Loading model: {self.model_name}")
            tokenizer = AutoTokenizer.from_pretrained(self.model_name)
            
            # Add padding token if not present
            if tokenizer.pad_token is None:
                tokenizer.pad_token = tokenizer.eos_token
            
            model = AutoModelForCausalLM.from_pretrained(
                self.model_name,
                torch_dtype=torch.float16 if torch.cuda.is_available() else torch.float32,
                device_map="auto" if torch.cuda.is_available() else None,
                trust_remote_code=True
            )
            
            # Apply LoRA
            if self.config.get('use_lora', True):
                lora_config = self.setup_lora_config()
                model = get_peft_model(model, lora_config)
                model.print_trainable_parameters()
            
            # Prepare dataset
            formatted_dataset = self.create_training_prompts(dataset)
            
            def tokenize_function(examples):
                return tokenizer(
                    examples['text'],
                    truncation=True,
                    padding=True,
                    max_length=self.config.get('max_length', 2048)
                )
            
            tokenized_dataset = formatted_dataset.map(
                tokenize_function,
                batched=True,
                remove_columns=formatted_dataset.column_names
            )
            
            # Split dataset
            train_dataset, eval_dataset = train_test_split(
                tokenized_dataset,
                test_size=0.1,
                random_state=42
            )
            
            # Training arguments
            training_args = TrainingArguments(
                output_dir=str(self.output_dir),
                num_train_epochs=self.config.get('epochs', 3),
                per_device_train_batch_size=self.config.get('batch_size', 4),
                per_device_eval_batch_size=self.config.get('eval_batch_size', 4),
                gradient_accumulation_steps=self.config.get('gradient_accumulation_steps', 4),
                learning_rate=self.config.get('learning_rate', 2e-5),
                weight_decay=self.config.get('weight_decay', 0.01),
                logging_steps=self.config.get('logging_steps', 100),
                eval_steps=self.config.get('eval_steps', 500),
                save_steps=self.config.get('save_steps', 1000),
                evaluation_strategy="steps",
                save_strategy="steps",
                load_best_model_at_end=True,
                metric_for_best_model="eval_loss",
                greater_is_better=False,
                fp16=torch.cuda.is_available(),
                dataloader_pin_memory=False,
                remove_unused_columns=False,
                report_to="mlflow"
            )
            
            # Data collator
            data_collator = DataCollatorForLanguageModeling(
                tokenizer=tokenizer,
                mlm=False
            )
            
            # Trainer
            trainer = Trainer(
                model=model,
                args=training_args,
                train_dataset=train_dataset,
                eval_dataset=eval_dataset,
                data_collator=data_collator,
                callbacks=[EarlyStoppingCallback(early_stopping_patience=3)]
            )
            
            # Train
            self.logger.info("Starting training...")
            trainer.train()
            
            # Save model
            trainer.save_model()
            tokenizer.save_pretrained(self.output_dir)
            
            # Log model
            mlflow.pytorch.log_model(model, "model")
            
            # Evaluate
            eval_results = trainer.evaluate()
            mlflow.log_metrics(eval_results)
            
            self.logger.info(f"Training completed. Model saved to {self.output_dir}")
            return str(self.output_dir)

class MistralFinanceTrainer(FinanceModelTrainer):
    """Mistral 7B Finance Trainer"""
    
    def __init__(self, config: Dict[str, Any]):
        super().__init__(
            model_name="mistralai/Mistral-7B-Instruct-v0.2",
            output_dir="models/mistral-finance-v1",
            config=config
        )

class DeepSeekFinanceTrainer(FinanceModelTrainer):
    """DeepSeek-R1 Finance Trainer"""
    
    def __init__(self, config: Dict[str, Any]):
        super().__init__(
            model_name="deepseek-ai/deepseek-r1-distill-llama-8b",
            output_dir="models/deepseek-finance-v1",
            config=config
        )

class Qwen3FinanceTrainer(FinanceModelTrainer):
    """Qwen3 Finance Trainer"""
    
    def __init__(self, config: Dict[str, Any]):
        super().__init__(
            model_name="Qwen/Qwen2.5-7B-Instruct",
            output_dir="models/qwen3-finance-v1",
            config=config
        )

class ModelTrainingPipeline:
    """Complete model training pipeline"""
    
    def __init__(self, config_path: str = "config/training_config.yaml"):
        self.config_path = Path(config_path)
        self.load_config()
        
        # Setup logging
        logging.basicConfig(level=logging.INFO)
        self.logger = logging.getLogger("training_pipeline")
        
        # Available trainers
        self.trainers = {
            'mistral': MistralFinanceTrainer,
            'deepseek': DeepSeekFinanceTrainer,
            'qwen3': Qwen3FinanceTrainer
        }
        
        # Setup model configurations - use HuggingFace models for training
        self.model_configs = {
            'mistral': {
                'model_name': 'mistralai/Mistral-7B-Instruct-v0.1',
                'output_dir': 'models/mistral-finance-v1'
            },
            'deepseek': {
                'model_name': 'deepseek-ai/DeepSeek-R1-Distill-Llama-8B',
                'output_dir': 'models/deepseek-finance-v1'
            },
            'qwen3': {
                'model_name': 'Qwen/Qwen2.5-8B-Instruct',
                'output_dir': 'models/qwen3-finance-v1'
            }
        }
    
    def load_config(self):
        """Load training configuration"""
        if self.config_path.exists():
            with open(self.config_path, 'r') as f:
                self.config = yaml.safe_load(f)
        else:
            # Default configuration
            self.config = {
                'training': {
                    'epochs': 3,
                    'batch_size': 4,
                    'eval_batch_size': 4,
                    'gradient_accumulation_steps': 4,
                    'learning_rate': 2e-5,
                    'weight_decay': 0.01,
                    'max_length': 2048,
                    'use_lora': True,
                    'logging_steps': 100,
                    'eval_steps': 500,
                    'save_steps': 1000
                },
                'models': {
                    'mistral': {'enabled': True},
                    'deepseek': {'enabled': True},
                    'qwen3': {'enabled': True}
                },
                'parallel_training': False,
                'gpu_count': 1
            }
            self.save_config()
    
    def save_config(self):
        """Save configuration to file"""
        self.config_path.parent.mkdir(parents=True, exist_ok=True)
        with open(self.config_path, 'w') as f:
            yaml.dump(self.config, f, default_flow_style=False)
    
    def train_single_model(self, model_name: str) -> bool:
        """Train a single model"""
        try:
            if model_name not in self.trainers:
                self.logger.error(f"Unknown model: {model_name}")
                return False
            
            # Get model configuration
            model_config = self.model_configs[model_name]
            
            # Update trainer configuration with local model path
            training_config = self.config['training'].copy()
            training_config['model_path'] = model_config.get('model_path')
            training_config['model_name'] = model_config.get('model_name')
            
            trainer_class = self.trainers[model_name]
            trainer = trainer_class(training_config)
            
            # Override model name with local path if available
            if model_config.get('model_path') and os.path.exists(model_config['model_path']):
                trainer.model_name = model_config['model_path']
            
            # Load dataset
            dataset = trainer.load_finance_datasets()
            
            # Train model
            output_path = trainer.train_model(dataset)
            
            self.logger.info(f"Successfully trained {model_name} -> {output_path}")
            return True
            
        except Exception as e:
            self.logger.error(f"Failed to train {model_name}: {e}")
            return False
    
    def train_all_models(self, parallel: bool = False) -> Dict[str, bool]:
        """Train all enabled models"""
        enabled_models = [
            model for model, config in self.config['models'].items()
            if config.get('enabled', True)
        ]
        
        results = {}
        
        if parallel and len(enabled_models) > 1:
            # Parallel training
            with multiprocessing.Pool(processes=min(len(enabled_models), self.config.get('gpu_count', 1))) as pool:
                training_results = pool.map(self.train_single_model, enabled_models)
                results = dict(zip(enabled_models, training_results))
        else:
            # Sequential training
            for model_name in enabled_models:
                results[model_name] = self.train_single_model(model_name)
        
        return results
    
    def validate_models(self) -> Dict[str, bool]:
        """Validate trained models"""
        validation_results = {}
        
        for model_name in self.trainers.keys():
            model_path = Path(f"models/{model_name}-finance-v1")
            
            # Check if model files exist
            model_files_exist = (
                (model_path / "pytorch_model.bin").exists() or
                (model_path / "model.safetensors").exists() or
                (model_path / "adapter_model.bin").exists()
            )
            
            config_exists = (model_path / "config.json").exists()
            tokenizer_exists = (model_path / "tokenizer.json").exists()
            
            validation_results[model_name] = model_files_exist and config_exists and tokenizer_exists
        
        return validation_results
    
    def display_training_summary(self, results: Dict[str, bool]):
        """Display training results summary"""
        table = Table(title="Model Training Results")
        table.add_column("Model", style="cyan")
        table.add_column("Status", style="green")
        table.add_column("Output Path", style="blue")
        
        for model_name, success in results.items():
            status = "✅ SUCCESS" if success else "❌ FAILED"
            output_path = f"models/{model_name}-finance-v1" if success else "N/A"
            table.add_row(model_name.upper(), status, output_path)
        
        console.print(table)
        
        # Summary statistics
        successful = sum(results.values())
        total = len(results)
        
        summary_text = f"""
[bold green]Training Summary[/bold green]

• Models trained: {successful}/{total}
• Success rate: {(successful/total)*100:.1f}%
• Output directory: ./models/
• MLflow tracking: ./mlruns/

[yellow]Next Steps:[/yellow]
1. Validate models: python train_all_models.py --validate-only
2. Test models: python test_models.py
3. Deploy models: python deploy_models.py
4. Start trading: python main.py --paper-trading
"""
        
        console.print(Panel(summary_text, title="Training Complete"))

def main():
    parser = argparse.ArgumentParser(description="Noryon AI Model Training Pipeline")
    parser.add_argument("--model", choices=["mistral", "deepseek", "qwen3", "all"], default="all",
                       help="Model to train")
    parser.add_argument("--parallel", action="store_true",
                       help="Train models in parallel")
    parser.add_argument("--gpu-count", type=int, default=1,
                       help="Number of GPUs to use")
    parser.add_argument("--quick-train", action="store_true",
                       help="Quick training with reduced epochs")
    parser.add_argument("--validate-only", action="store_true",
                       help="Only validate existing models")
    parser.add_argument("--config", default="config/training_config.yaml",
                       help="Training configuration file")
    
    args = parser.parse_args()
    
    # Initialize pipeline
    pipeline = ModelTrainingPipeline(args.config)
    
    # Modify config for quick training
    if args.quick_train:
        pipeline.config['training']['epochs'] = 1
        pipeline.config['training']['eval_steps'] = 100
        pipeline.config['training']['save_steps'] = 200
    
    # Set parallel training
    if args.parallel:
        pipeline.config['parallel_training'] = True
        pipeline.config['gpu_count'] = args.gpu_count
    
    if args.validate_only:
        # Validate models only
        validation_results = pipeline.validate_models()
        pipeline.display_training_summary(validation_results)
    else:
        # Train models
        console.print(Panel(
            "[bold blue]Noryon AI Model Training Pipeline[/bold blue]\n\n"
            f"Training mode: {'Parallel' if args.parallel else 'Sequential'}\n"
            f"Models: {args.model}\n"
            f"Quick training: {args.quick_train}\n"
            f"GPU count: {args.gpu_count}",
            title="Training Configuration"
        ))
        
        if args.model == "all":
            results = pipeline.train_all_models(args.parallel)
        else:
            results = {args.model: pipeline.train_single_model(args.model)}
        
        pipeline.display_training_summary(results)

if __name__ == "__main__":
    main()