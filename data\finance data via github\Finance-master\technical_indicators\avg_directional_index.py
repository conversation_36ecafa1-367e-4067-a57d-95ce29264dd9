# Import dependencies
import numpy as np
import pandas as pd
import matplotlib.pyplot as plt
import yfinance as yf
import datetime as dt
yf.pdr_override()
import sys
import os
parent_dir = os.path.dirname(os.getcwd())
sys.path.append(parent_dir)
import ta_functions as ta

# input
symbol = "AAPL"
start = dt.date.today() - dt.timedelta(days=365 * 4)
end = dt.date.today()

# Read data
df = yf.download(symbol, start, end)

# Simple Line Chart
plt.figure(figsize=(14, 7))
plt.plot(df["Adj Close"])
plt.legend(loc="best")
plt.title("Stock " + symbol + " Closing Price")
plt.xlabel("Date")
plt.ylabel("Price")
plt.show()

adx = ta.ADX(df["High"], df["Low"], df["Adj Close"], timeperiod=14)
adx = adx.dropna()

# Line Chart
fig = plt.figure(figsize=(14, 7))
ax1 = plt.subplot(2, 1, 1)
ax1.plot(df["Adj Close"])
# ax1.grid(True, which='both')
ax1.grid(which="minor", linestyle="-", linewidth="0.5", color="black")
ax1.grid(which="major", linestyle="-", linewidth="0.5", color="red")
ax1.minorticks_on()
ax1.legend(loc="best")
ax1.set_title("Stock " + symbol + " Closing Price")
ax1.set_ylabel("Price")

ax2 = plt.subplot(2, 1, 2)
ax2.plot(adx, "-", label="ADX")
ax2.text(s="Strong Trend", x=adx.index[0], y=50, fontsize=14)
ax2.text(s="Weak Trend", x=adx.index[0], y=20, fontsize=14)
ax2.axhline(y=50, color="r")
ax2.axhline(y=20, color="r")
ax2.set_xlabel("Date")
ax2.legend(loc="best")
plt.show()

# ## Candlestick with ADX
# Candlestick
dfc = df.copy()

from matplotlib import dates as mdates

dfc["ADX"] = ta.ADX(dfc["High"], dfc["Low"], dfc["Adj Close"], timeperiod=14)
dfc = dfc.dropna()
dfc = dfc.reset_index()
dfc["Date"] = mdates.date2num(dfc["Date"].tolist())
from mplfinance.original_flavor import candlestick_ohlc

fig = plt.figure(figsize=(14, 7))
ax1 = plt.subplot(2, 1, 1)
candlestick_ohlc(ax1, dfc.values, width=0.5, colorup="g", colordown="r", alpha=1.0)
ax1.xaxis_date()
ax1.xaxis.set_major_formatter(mdates.DateFormatter("%d-%m-%Y"))
ax1.grid(True, which="both")
# ax1.grid(which='minor', linestyle='-', linewidth='0.5', color='black')
# ax1.grid(which='major', linestyle='-', linewidth='0.5', color='red')
ax1.minorticks_on()
# ax1.legend(loc='best')
ax1.set_title("Stock " + symbol + " Closing Price")
ax1.set_ylabel("Price")

ax2 = plt.subplot(2, 1, 2)
ax2.plot(adx, "-", label="ADX")
ax2.text(s="Strong Trend", x=adx.index[0], y=50, fontsize=14)
ax2.text(s="Weak Trend", x=adx.index[0], y=20, fontsize=14)
ax2.axhline(y=50, color="r")
ax2.axhline(y=20, color="r")
ax2.set_xlabel("Date")
ax2.legend(loc="best")
plt.show()

fig = plt.figure(figsize=(14, 7))
ax1 = plt.subplot(2, 1, 1)
candlestick_ohlc(ax1, dfc.values, width=0.5, colorup="g", colordown="r", alpha=1.0)
ax1.xaxis_date()
ax1.xaxis.set_major_formatter(mdates.DateFormatter("%d-%m-%Y"))
ax1.grid(True, which="both")
ax1.minorticks_on()
ax1v = ax1.twinx()
dfc["VolumePositive"] = dfc["Open"] < dfc["Adj Close"]
colors = dfc.VolumePositive.map({True: "g", False: "r"})
ax1v.bar(dfc.Date, dfc["Volume"], color=colors, alpha=0.4)
ax1v.axes.yaxis.set_ticklabels([])
ax1v.set_ylim(0, 3 * df.Volume.max())
ax1.set_title("Stock " + symbol + " Closing Price")
ax1.set_ylabel("Price")

ax2 = plt.subplot(2, 1, 2)
ax2.plot(adx, "-", label="ADX")
ax2.text(s="Strong Trend", x=adx.index[0], y=50, fontsize=14)
ax2.text(s="Weak Trend", x=adx.index[0], y=20, fontsize=14)
ax2.axhline(y=50, color="r")
ax2.axhline(y=20, color="r")
ax2.set_xlabel("Date")
ax2.legend(loc="best")
plt.show()