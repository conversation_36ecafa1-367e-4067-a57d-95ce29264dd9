{"builder_name": "parquet", "citation": "", "config_name": "default", "dataset_name": "consumer-finance-complaints", "dataset_size": **********, "description": "", "download_checksums": {"hf://datasets/BEE-spoke-data/consumer-finance-complaints@e37cf1e8530c0142ab0750059cceaf1fbd7b0fd3/data/train-00000-of-00007.parquet": {"num_bytes": 126296039, "checksum": null}, "hf://datasets/BEE-spoke-data/consumer-finance-complaints@e37cf1e8530c0142ab0750059cceaf1fbd7b0fd3/data/train-00001-of-00007.parquet": {"num_bytes": 151263453, "checksum": null}, "hf://datasets/BEE-spoke-data/consumer-finance-complaints@e37cf1e8530c0142ab0750059cceaf1fbd7b0fd3/data/train-00002-of-00007.parquet": {"num_bytes": 163706331, "checksum": null}, "hf://datasets/BEE-spoke-data/consumer-finance-complaints@e37cf1e8530c0142ab0750059cceaf1fbd7b0fd3/data/train-00003-of-00007.parquet": {"num_bytes": 158697178, "checksum": null}, "hf://datasets/BEE-spoke-data/consumer-finance-complaints@e37cf1e8530c0142ab0750059cceaf1fbd7b0fd3/data/train-00004-of-00007.parquet": {"num_bytes": 176603799, "checksum": null}, "hf://datasets/BEE-spoke-data/consumer-finance-complaints@e37cf1e8530c0142ab0750059cceaf1fbd7b0fd3/data/train-00005-of-00007.parquet": {"num_bytes": 174401812, "checksum": null}, "hf://datasets/BEE-spoke-data/consumer-finance-complaints@e37cf1e8530c0142ab0750059cceaf1fbd7b0fd3/data/train-00006-of-00007.parquet": {"num_bytes": *********, "checksum": null}}, "download_size": 1061488683, "features": {"Date received": {"dtype": "string", "_type": "Value"}, "Product": {"dtype": "string", "_type": "Value"}, "Sub-product": {"dtype": "string", "_type": "Value"}, "Issue": {"dtype": "string", "_type": "Value"}, "Sub-issue": {"dtype": "string", "_type": "Value"}, "Consumer complaint narrative": {"dtype": "string", "_type": "Value"}, "Company public response": {"dtype": "string", "_type": "Value"}, "Company": {"dtype": "string", "_type": "Value"}, "State": {"dtype": "string", "_type": "Value"}, "ZIP code": {"dtype": "string", "_type": "Value"}, "Tags": {"dtype": "string", "_type": "Value"}, "Consumer consent provided?": {"dtype": "string", "_type": "Value"}, "Submitted via": {"dtype": "string", "_type": "Value"}, "Date sent to company": {"dtype": "string", "_type": "Value"}, "Company response to consumer": {"dtype": "string", "_type": "Value"}, "Timely response?": {"dtype": "string", "_type": "Value"}, "Consumer disputed?": {"dtype": "string", "_type": "Value"}, "Complaint ID": {"dtype": "int64", "_type": "Value"}}, "homepage": "", "license": "", "size_in_bytes": **********, "splits": {"train": {"name": "train", "num_bytes": **********, "num_examples": 4707579, "shard_lengths": [798512, 616512, 631511, 664511, 626000, 681511, 689022], "dataset_name": "consumer-finance-complaints"}}, "version": {"version_str": "0.0.0", "major": 0, "minor": 0, "patch": 0}}