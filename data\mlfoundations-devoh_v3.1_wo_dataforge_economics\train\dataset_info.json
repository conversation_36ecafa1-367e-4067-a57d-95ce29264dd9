{"builder_name": "parquet", "citation": "", "config_name": "default", "dataset_name": "oh_v3.1_wo_dataforge_economics", "dataset_size": **********, "description": "", "download_checksums": {"hf://datasets/mlfoundations-dev/oh_v3.1_wo_dataforge_economics@29637907cbeca166b32f7b08a21c3a7e14beebc8/data/train-00000-of-00004.parquet": {"num_bytes": 313266664, "checksum": null}, "hf://datasets/mlfoundations-dev/oh_v3.1_wo_dataforge_economics@29637907cbeca166b32f7b08a21c3a7e14beebc8/data/train-00001-of-00004.parquet": {"num_bytes": 283507566, "checksum": null}, "hf://datasets/mlfoundations-dev/oh_v3.1_wo_dataforge_economics@29637907cbeca166b32f7b08a21c3a7e14beebc8/data/train-00002-of-00004.parquet": {"num_bytes": 179832916, "checksum": null}, "hf://datasets/mlfoundations-dev/oh_v3.1_wo_dataforge_economics@29637907cbeca166b32f7b08a21c3a7e14beebc8/data/train-00003-of-00004.parquet": {"num_bytes": 271188947, "checksum": null}}, "download_size": **********, "features": {"conversations": [{"from": {"dtype": "string", "_type": "Value"}, "value": {"dtype": "string", "_type": "Value"}}], "source_label_exact": {"feature": {"dtype": "string", "_type": "Value"}, "_type": "Sequence"}}, "homepage": "", "license": "", "size_in_bytes": **********, "splits": {"train": {"name": "train", "num_bytes": **********, "num_examples": 1000891, "shard_lengths": [213000, 176223, 376446, 235222], "dataset_name": "oh_v3.1_wo_dataforge_economics"}}, "version": {"version_str": "0.0.0", "major": 0, "minor": 0, "patch": 0}}