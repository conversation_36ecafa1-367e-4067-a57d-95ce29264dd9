#!/usr/bin/env python3
"""
Test New Granite Vision Model
Comprehensive testing of the newly trained visual market analysis specialist
"""

import subprocess
import asyncio
from datetime import datetime
from rich.console import Console
from rich.panel import Panel
from rich.table import Table

console = Console()

class GraniteModelTester:
    """Test the newly trained Granite Vision model"""
    
    def __init__(self):
        self.model_name = "noryon-granite-vision-finance-v1:latest"
        self.specialization = "Visual Market Analysis"
        
        # Visual analysis test scenarios
        self.test_scenarios = [
            {
                "name": "Chart Pattern Recognition",
                "query": "Analyze a stock chart showing a head and shoulders pattern with declining volume. The left shoulder formed at $180, head at $195, right shoulder at $182. Current price is $175. Provide visual analysis and trading recommendation.",
                "expected_elements": ["head and shoulders", "pattern", "volume", "support", "resistance", "breakout", "target"]
            },
            {
                "name": "Technical Indicator Analysis",
                "query": "Analyze a chart where RSI shows bearish divergence (price making higher highs while RSI makes lower highs), MACD is below signal line, and price is testing 200-day moving average. Provide visual interpretation.",
                "expected_elements": ["rsi", "divergence", "macd", "moving average", "bearish", "signal", "trend"]
            },
            {
                "name": "Support and Resistance Levels",
                "query": "Identify key support and resistance levels on a chart where price has bounced off $150 three times (support) and failed to break $200 twice (resistance). Current price is $175. Provide visual analysis.",
                "expected_elements": ["support", "resistance", "levels", "bounce", "break", "price action", "key levels"]
            },
            {
                "name": "Volume Profile Analysis",
                "query": "Analyze a volume profile showing high volume node at $160, low volume gap between $170-$180, and volume spike at $185. Current price is $172. Provide visual trading strategy.",
                "expected_elements": ["volume", "profile", "node", "gap", "spike", "trading", "strategy"]
            },
            {
                "name": "Multi-Timeframe Visual Analysis",
                "query": "Perform multi-timeframe analysis: Daily chart shows uptrend, 4-hour shows consolidation triangle, 1-hour shows potential breakout setup. Provide comprehensive visual assessment.",
                "expected_elements": ["timeframe", "uptrend", "triangle", "consolidation", "breakout", "visual", "analysis"]
            }
        ]
    
    async def test_model_response(self, scenario):
        """Test model with a specific scenario"""
        try:
            result = subprocess.run([
                'ollama', 'run', self.model_name, scenario['query']
            ], capture_output=True, text=True, timeout=90)
            
            if result.returncode == 0:
                response = result.stdout.strip()
                quality_score = self._calculate_quality_score(response, scenario)
                
                return {
                    "success": True,
                    "response": response,
                    "response_length": len(response),
                    "quality_score": quality_score,
                    "elements_found": sum(1 for element in scenario['expected_elements'] 
                                        if element.lower() in response.lower())
                }
            else:
                return {
                    "success": False,
                    "error": result.stderr,
                    "quality_score": 0
                }
                
        except subprocess.TimeoutExpired:
            return {
                "success": False,
                "error": "Timeout",
                "quality_score": 0
            }
        except Exception as e:
            return {
                "success": False,
                "error": str(e),
                "quality_score": 0
            }
    
    def _calculate_quality_score(self, response, scenario):
        """Calculate response quality score for visual analysis"""
        score = 0
        
        # Length check (visual analysis should be detailed)
        if len(response) > 200:
            score += 20
        if len(response) > 500:
            score += 15
        
        # Expected elements check
        elements_found = sum(1 for element in scenario['expected_elements'] 
                           if element.lower() in response.lower())
        element_score = (elements_found / len(scenario['expected_elements'])) * 35
        score += element_score
        
        # Visual analysis terminology
        visual_terms = ["chart", "pattern", "visual", "level", "line", "trend", "breakout", 
                       "support", "resistance", "volume", "indicator", "signal"]
        visual_count = sum(1 for term in visual_terms if term.lower() in response.lower())
        score += min(visual_count * 2, 20)
        
        # Specific recommendations
        if any(word in response.lower() for word in ["recommend", "suggest", "target", "entry", "exit", "stop"]):
            score += 10
        
        return min(score, 100)
    
    async def comprehensive_test(self):
        """Run comprehensive testing of the Granite Vision model"""
        console.print(Panel(
            f"[bold blue]🧪 Testing New Granite Vision Model[/bold blue]\n\n"
            f"Model: {self.model_name}\n"
            f"Specialization: {self.specialization}\n\n"
            f"Testing {len(self.test_scenarios)} visual analysis scenarios:\n"
            f"• Chart Pattern Recognition\n"
            f"• Technical Indicator Analysis\n"
            f"• Support/Resistance Levels\n"
            f"• Volume Profile Analysis\n"
            f"• Multi-Timeframe Analysis",
            title="Granite Vision Testing"
        ))
        
        start_time = datetime.now()
        results = {}
        
        # Test each scenario
        for scenario in self.test_scenarios:
            console.print(f"[yellow]🔍 Testing: {scenario['name']}...[/yellow]")
            
            result = await self.test_model_response(scenario)
            results[scenario['name']] = result
            
            if result['success']:
                console.print(f"[green]✅ {scenario['name']}: Quality {result['quality_score']:.1f}/100[/green]")
            else:
                console.print(f"[red]❌ {scenario['name']}: Failed - {result.get('error', 'Unknown error')}[/red]")
        
        end_time = datetime.now()
        duration = end_time - start_time
        
        return results, duration
    
    def generate_test_report(self, results, duration):
        """Generate comprehensive test report"""
        
        # Calculate statistics
        total_tests = len(results)
        passed_tests = sum(1 for r in results.values() if r['success'])
        avg_quality = sum(r['quality_score'] for r in results.values()) / total_tests if total_tests > 0 else 0
        
        # Results table
        console.print("\n[bold green]📊 Granite Vision Model Test Results[/bold green]")
        
        results_table = Table(title="Visual Analysis Test Results")
        results_table.add_column("Test Scenario", style="cyan")
        results_table.add_column("Status", style="green")
        results_table.add_column("Quality Score", style="yellow")
        results_table.add_column("Response Length", style="blue")
        results_table.add_column("Elements Found", style="magenta")
        
        for scenario_name, result in results.items():
            if result['success']:
                status = "✅ Pass"
                quality = f"{result['quality_score']:.1f}/100"
                length = str(result['response_length'])
                elements = f"{result['elements_found']}/{len([s for s in self.test_scenarios if s['name'] == scenario_name][0]['expected_elements'])}"
            else:
                status = "❌ Fail"
                quality = "0/100"
                length = "0"
                elements = "0/0"
            
            results_table.add_row(scenario_name, status, quality, length, elements)
        
        console.print(results_table)
        
        # Sample response display
        best_result = max(results.values(), key=lambda x: x.get('quality_score', 0))
        if best_result['success']:
            best_scenario = [name for name, result in results.items() if result == best_result][0]
            
            console.print(f"\n[bold cyan]📋 Best Response Sample ({best_scenario}):[/bold cyan]")
            console.print(Panel(
                best_result['response'][:500] + ("..." if len(best_result['response']) > 500 else ""),
                title=f"Sample Response - Quality: {best_result['quality_score']:.1f}/100"
            ))
        
        # Overall assessment
        success_rate = (passed_tests / total_tests * 100) if total_tests > 0 else 0
        
        if success_rate == 100 and avg_quality > 80:
            status = "🏆 EXCELLENT"
        elif success_rate >= 80 and avg_quality > 70:
            status = "✅ VERY GOOD"
        elif success_rate >= 60:
            status = "👍 GOOD"
        else:
            status = "⚠️ NEEDS IMPROVEMENT"
        
        console.print(Panel(
            f"[bold green]🎉 Granite Vision Model Testing Complete![/bold green]\n\n"
            f"Testing Duration: {duration}\n"
            f"Tests Passed: {passed_tests}/{total_tests}\n"
            f"Success Rate: {success_rate:.1f}%\n"
            f"Average Quality: {avg_quality:.1f}/100\n"
            f"Model Status: {status}\n\n"
            f"✅ Visual Market Analysis Specialist: {'READY FOR PRODUCTION' if success_rate >= 80 else 'NEEDS REFINEMENT'}\n"
            f"🚀 Integration Status: {'APPROVED' if success_rate >= 80 else 'CONDITIONAL'}",
            title="Testing Summary"
        ))
        
        return {
            "total_tests": total_tests,
            "passed_tests": passed_tests,
            "success_rate": success_rate,
            "average_quality": avg_quality,
            "status": status,
            "production_ready": success_rate >= 80,
            "detailed_results": results
        }

async def main():
    """Main testing function"""
    console.print("[bold blue]🧪 Starting Granite Vision Model Testing...[/bold blue]\n")
    
    tester = GraniteModelTester()
    
    # Run comprehensive testing
    results, duration = await tester.comprehensive_test()
    
    # Generate report
    summary = tester.generate_test_report(results, duration)
    
    console.print("\n[bold green]🎯 Next Steps:[/bold green]")
    if summary["production_ready"]:
        console.print("1. ✅ Integrate Granite Vision into 9-model ensemble")
        console.print("2. 🔄 Update production deployment system")
        console.print("3. 🧪 Run full system integration tests")
        console.print("4. 🚀 Deploy enhanced visual analysis capabilities")
    else:
        console.print("1. 🔧 Review and improve model responses")
        console.print("2. 🔄 Consider additional training iterations")
        console.print("3. 🧪 Re-test before production integration")
    
    return summary

if __name__ == "__main__":
    results = asyncio.run(main())
