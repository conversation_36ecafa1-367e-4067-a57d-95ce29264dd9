import os
import sys
import json
import yaml
from pathlib import Path
from typing import Dict, List, Optional, Any, Tuple
import questionary
from rich.console import Console
from rich.table import Table
from rich.panel import Panel
from rich.text import Text
from rich.progress import Progress, SpinnerColumn, TextColumn
import asyncio
from datetime import datetime

# Add parent directory to path for imports
sys.path.append(str(Path(__file__).parent.parent.parent))

from core.config.config_manager import ConfigManager, BrokerCredentials, TradingConfig, AIConfig
from core.registry.broker_registry import BrokerRegistry

console = Console()

class SetupWizard:
    """Interactive setup wizard for the trading AI system"""
    
    def __init__(self):
        self.config_manager = None
        self.broker_registry = None
        self.setup_data = {}
        
        # Predefined broker templates
        self.broker_templates = {
            'binance': {
                'name': 'Binance',
                'type': 'crypto',
                'description': 'World\'s largest cryptocurrency exchange',
                'config': {
                    'broker': {
                        'name': 'binance',
                        'type': 'crypto',
                        'base_url': 'https://api.binance.com',
                        'websocket_url': 'wss://stream.binance.com:9443',
                        'authentication': {
                            'type': 'api_key',
                            'required_fields': ['api_key', 'secret_key']
                        },
                        'rate_limits': {
                            'requests_per_minute': 1200,
                            'orders_per_second': 10
                        },
                        'supported_assets': ['BTC', 'ETH', 'BNB', 'ADA', 'DOT'],
                        'supported_order_types': ['market', 'limit', 'stop_loss', 'take_profit']
                    }
                }
            },
            'coinbase': {
                'name': 'Coinbase Pro',
                'type': 'crypto',
                'description': 'Professional cryptocurrency trading platform',
                'config': {
                    'broker': {
                        'name': 'coinbase',
                        'type': 'crypto',
                        'base_url': 'https://api.exchange.coinbase.com',
                        'websocket_url': 'wss://ws-feed.exchange.coinbase.com',
                        'authentication': {
                            'type': 'api_key',
                            'required_fields': ['api_key', 'secret_key', 'passphrase']
                        },
                        'rate_limits': {
                            'requests_per_minute': 600,
                            'orders_per_second': 5
                        },
                        'supported_assets': ['BTC', 'ETH', 'LTC', 'BCH'],
                        'supported_order_types': ['market', 'limit', 'stop']
                    }
                }
            },
            'interactive_brokers': {
                'name': 'Interactive Brokers',
                'type': 'traditional',
                'description': 'Professional trading platform for stocks, forex, and derivatives',
                'config': {
                    'broker': {
                        'name': 'interactive_brokers',
                        'type': 'traditional',
                        'base_url': 'https://api.ibkr.com',
                        'authentication': {
                            'type': 'username_password',
                            'required_fields': ['username', 'password']
                        },
                        'rate_limits': {
                            'requests_per_minute': 300,
                            'orders_per_second': 2
                        },
                        'supported_assets': ['EUR/USD', 'GBP/USD', 'USD/JPY', 'AAPL', 'TSLA'],
                        'supported_order_types': ['market', 'limit', 'stop', 'stop_limit']
                    }
                }
            },
            'alpaca': {
                'name': 'Alpaca',
                'type': 'traditional',
                'description': 'Commission-free stock trading API',
                'config': {
                    'broker': {
                        'name': 'alpaca',
                        'type': 'traditional',
                        'base_url': 'https://paper-api.alpaca.markets',
                        'websocket_url': 'wss://stream.data.alpaca.markets',
                        'authentication': {
                            'type': 'api_key',
                            'required_fields': ['api_key', 'secret_key']
                        },
                        'rate_limits': {
                            'requests_per_minute': 200,
                            'orders_per_second': 1
                        },
                        'supported_assets': ['AAPL', 'GOOGL', 'MSFT', 'TSLA', 'AMZN'],
                        'supported_order_types': ['market', 'limit', 'stop', 'stop_limit']
                    }
                }
            },
            'oanda': {
                'name': 'OANDA',
                'type': 'forex',
                'description': 'Forex and CFD trading platform',
                'config': {
                    'broker': {
                        'name': 'oanda',
                        'type': 'forex',
                        'base_url': 'https://api-fxpractice.oanda.com',
                        'websocket_url': 'wss://stream-fxpractice.oanda.com',
                        'authentication': {
                            'type': 'api_key',
                            'required_fields': ['api_key', 'account_id']
                        },
                        'rate_limits': {
                            'requests_per_minute': 120,
                            'orders_per_second': 1
                        },
                        'supported_assets': ['EUR_USD', 'GBP_USD', 'USD_JPY', 'AUD_USD'],
                        'supported_order_types': ['market', 'limit', 'stop', 'market_if_touched']
                    }
                }
            }
        }
    
    async def run_setup(self) -> bool:
        """Run the complete setup wizard"""
        try:
            console.print(Panel.fit(
                "[bold blue]Trading AI System Setup Wizard[/bold blue]\n"
                "Welcome! This wizard will help you configure your trading AI system.",
                border_style="blue"
            ))
            
            # Step 1: Environment setup
            if not await self._setup_environment():
                return False
            
            # Step 2: Initialize config manager
            self._initialize_config_manager()
            
            # Step 3: Trading configuration
            if not await self._setup_trading_config():
                return False
            
            # Step 4: AI configuration
            if not await self._setup_ai_config():
                return False
            
            # Step 5: Broker setup
            if not await self._setup_brokers():
                return False
            
            # Step 6: Final validation and summary
            if not await self._finalize_setup():
                return False
            
            console.print(Panel.fit(
                "[bold green]✅ Setup completed successfully![/bold green]\n"
                "Your trading AI system is now configured and ready to use.",
                border_style="green"
            ))
            
            return True
        
        except KeyboardInterrupt:
            console.print("\n[yellow]Setup cancelled by user.[/yellow]")
            return False
        except Exception as e:
            console.print(f"\n[red]Setup failed: {e}[/red]")
            return False
    
    async def _setup_environment(self) -> bool:
        """Setup environment configuration"""
        console.print("\n[bold]Step 1: Environment Configuration[/bold]")
        
        environment = await questionary.select(
            "Select your environment:",
            choices=[
                "development",
                "staging", 
                "production"
            ],
            default="development"
        ).ask_async()
        
        if not environment:
            return False
        
        self.setup_data['environment'] = environment
        
        # Ask for project directory
        current_dir = os.getcwd()
        project_dir = await questionary.path(
            "Enter project directory:",
            default=current_dir,
            only_directories=True
        ).ask_async()
        
        if not project_dir:
            return False
        
        self.setup_data['project_dir'] = project_dir
        
        # Change to project directory
        os.chdir(project_dir)
        
        console.print(f"[green]✅ Environment: {environment}[/green]")
        console.print(f"[green]✅ Project directory: {project_dir}[/green]")
        
        return True
    
    def _initialize_config_manager(self):
        """Initialize the configuration manager"""
        self.config_manager = ConfigManager(
            environment=self.setup_data['environment']
        )
        self.broker_registry = BrokerRegistry()
    
    async def _setup_trading_config(self) -> bool:
        """Setup trading configuration"""
        console.print("\n[bold]Step 2: Trading Configuration[/bold]")
        
        # Risk management settings
        max_position_size = await questionary.text(
            "Maximum position size (as decimal, e.g., 0.02 for 2%):",
            default="0.02",
            validate=lambda x: self._validate_float(x, 0.001, 0.5)
        ).ask_async()
        
        if not max_position_size:
            return False
        
        risk_per_trade = await questionary.text(
            "Risk per trade (as decimal, e.g., 0.01 for 1%):",
            default="0.01",
            validate=lambda x: self._validate_float(x, 0.001, 0.1)
        ).ask_async()
        
        if not risk_per_trade:
            return False
        
        max_open_positions = await questionary.text(
            "Maximum open positions:",
            default="5",
            validate=lambda x: self._validate_int(x, 1, 50)
        ).ask_async()
        
        if not max_open_positions:
            return False
        
        # Update trading config
        self.config_manager.update_trading_config(
            max_position_size=float(max_position_size),
            risk_per_trade=float(risk_per_trade),
            max_open_positions=int(max_open_positions)
        )
        
        console.print("[green]✅ Trading configuration saved[/green]")
        return True
    
    async def _setup_ai_config(self) -> bool:
        """Setup AI configuration"""
        console.print("\n[bold]Step 3: AI Configuration[/bold]")
        
        # Local model setup
        use_local_model = await questionary.confirm(
            "Do you want to use a local AI model?",
            default=True
        ).ask_async()
        
        local_model_name = "qwen3-8b"
        local_model_path = "qwen3/Qwen3-8B-Q4_K_M.gguf"
        
        if use_local_model:
            local_model_name = await questionary.text(
                "Local model name:",
                default="qwen3-8b"
            ).ask_async()
            
            if not local_model_name:
                return False
            
            local_model_path = await questionary.path(
                "Local model path:",
                default="qwen3/Qwen3-8B-Q4_K_M.gguf"
            ).ask_async()
            
            if not local_model_path:
                return False
        
        # API models setup
        use_api_models = await questionary.confirm(
            "Do you want to use API-based AI models?",
            default=True
        ).ask_async()
        
        api_models = []
        if use_api_models:
            available_models = ["gpt-4", "gpt-3.5-turbo", "claude-3", "claude-2"]
            api_models = await questionary.checkbox(
                "Select API models to use:",
                choices=available_models
            ).ask_async()
            
            if not api_models:
                api_models = ["gpt-4"]
        
        # Confidence threshold
        confidence_threshold = await questionary.text(
            "AI confidence threshold (0.0-1.0):",
            default="0.7",
            validate=lambda x: self._validate_float(x, 0.0, 1.0)
        ).ask_async()
        
        if not confidence_threshold:
            return False
        
        # Update AI config
        self.config_manager.update_ai_config(
            local_model_name=local_model_name,
            local_model_path=local_model_path,
            api_models=api_models,
            confidence_threshold=float(confidence_threshold)
        )
        
        console.print("[green]✅ AI configuration saved[/green]")
        return True
    
    async def _setup_brokers(self) -> bool:
        """Setup broker configurations"""
        console.print("\n[bold]Step 4: Broker Configuration[/bold]")
        
        brokers_to_setup = []
        
        # Show available broker templates
        console.print("\n[bold]Available Broker Templates:[/bold]")
        table = Table(show_header=True, header_style="bold magenta")
        table.add_column("Name", style="cyan")
        table.add_column("Type", style="green")
        table.add_column("Description", style="white")
        
        for key, broker in self.broker_templates.items():
            table.add_row(broker['name'], broker['type'], broker['description'])
        
        console.print(table)
        
        # Select brokers to configure
        broker_choices = [f"{broker['name']} ({broker['type']})" for broker in self.broker_templates.values()]
        broker_choices.append("Custom broker")
        
        selected_brokers = await questionary.checkbox(
            "Select brokers to configure:",
            choices=broker_choices
        ).ask_async()
        
        if not selected_brokers:
            console.print("[yellow]No brokers selected. You can add them later.[/yellow]")
            return True
        
        # Configure each selected broker
        for selection in selected_brokers:
            if selection == "Custom broker":
                if not await self._setup_custom_broker():
                    return False
            else:
                # Find the broker template
                broker_name = selection.split(" (")[0]
                template_key = None
                for key, template in self.broker_templates.items():
                    if template['name'] == broker_name:
                        template_key = key
                        break
                
                if template_key:
                    if not await self._setup_template_broker(template_key):
                        return False
        
        console.print("[green]✅ Broker configuration completed[/green]")
        return True
    
    async def _setup_template_broker(self, template_key: str) -> bool:
        """Setup a broker from template"""
        template = self.broker_templates[template_key]
        broker_name = template_key
        
        console.print(f"\n[bold]Configuring {template['name']}[/bold]")
        
        # Create broker config
        self.config_manager.create_broker_config(broker_name, template['config'])
        
        # Setup credentials
        auth_config = template['config']['broker']['authentication']
        required_fields = auth_config['required_fields']
        
        console.print(f"\nEnter credentials for {template['name']}:")
        
        credentials_data = {'broker_name': broker_name}
        
        for field in required_fields:
            if field in ['password', 'secret_key', 'passphrase']:
                value = await questionary.password(f"{field.replace('_', ' ').title()}:").ask_async()
            else:
                value = await questionary.text(f"{field.replace('_', ' ').title()}:").ask_async()
            
            if not value:
                console.print(f"[red]❌ {field} is required[/red]")
                return False
            
            credentials_data[field] = value
        
        # Save credentials
        credentials = BrokerCredentials(**credentials_data)
        self.config_manager.save_broker_credentials(broker_name, credentials)
        
        console.print(f"[green]✅ {template['name']} configured successfully[/green]")
        return True
    
    async def _setup_custom_broker(self) -> bool:
        """Setup a custom broker"""
        console.print("\n[bold]Custom Broker Configuration[/bold]")
        
        broker_name = await questionary.text(
            "Broker name (lowercase, no spaces):",
            validate=lambda x: len(x) > 0 and x.islower() and '_' not in x
        ).ask_async()
        
        if not broker_name:
            return False
        
        broker_type = await questionary.select(
            "Broker type:",
            choices=["crypto", "forex", "stocks", "traditional", "other"]
        ).ask_async()
        
        if not broker_type:
            return False
        
        base_url = await questionary.text(
            "API base URL:",
            validate=lambda x: x.startswith('http')
        ).ask_async()
        
        if not base_url:
            return False
        
        # Create custom broker config
        custom_config = {
            'broker': {
                'name': broker_name,
                'type': broker_type,
                'base_url': base_url,
                'authentication': {
                    'type': 'api_key',
                    'required_fields': ['api_key', 'secret_key']
                },
                'rate_limits': {
                    'requests_per_minute': 100,
                    'orders_per_second': 1
                },
                'supported_order_types': ['market', 'limit']
            }
        }
        
        self.config_manager.create_broker_config(broker_name, custom_config)
        
        # Setup credentials
        setup_creds = await questionary.confirm(
            "Do you want to setup credentials now?",
            default=True
        ).ask_async()
        
        if setup_creds:
            api_key = await questionary.text("API Key:").ask_async()
            secret_key = await questionary.password("Secret Key:").ask_async()
            
            if api_key and secret_key:
                credentials = BrokerCredentials(
                    broker_name=broker_name,
                    api_key=api_key,
                    secret_key=secret_key
                )
                self.config_manager.save_broker_credentials(broker_name, credentials)
        
        console.print(f"[green]✅ Custom broker {broker_name} configured[/green]")
        return True
    
    async def _finalize_setup(self) -> bool:
        """Finalize setup and show summary"""
        console.print("\n[bold]Step 5: Final Validation[/bold]")
        
        # Validate configuration
        with Progress(
            SpinnerColumn(),
            TextColumn("[progress.description]{task.description}"),
            console=console
        ) as progress:
            task = progress.add_task("Validating configuration...", total=None)
            
            # Simulate validation time
            await asyncio.sleep(2)
            
            issues = self.config_manager.validate_configuration()
            progress.update(task, completed=True)
        
        # Show validation results
        if issues['errors']:
            console.print("\n[red]❌ Configuration Errors:[/red]")
            for error in issues['errors']:
                console.print(f"  • {error}")
            return False
        
        if issues['warnings']:
            console.print("\n[yellow]⚠️  Configuration Warnings:[/yellow]")
            for warning in issues['warnings']:
                console.print(f"  • {warning}")
            
            continue_setup = await questionary.confirm(
                "Continue with warnings?",
                default=True
            ).ask_async()
            
            if not continue_setup:
                return False
        
        # Show configuration summary
        console.print("\n[bold]Configuration Summary:[/bold]")
        summary = self.config_manager.get_config_summary()
        
        summary_table = Table(show_header=True, header_style="bold magenta")
        summary_table.add_column("Setting", style="cyan")
        summary_table.add_column("Value", style="white")
        
        summary_table.add_row("Environment", summary['environment'])
        summary_table.add_row("Brokers Configured", str(summary['brokers_configured']))
        summary_table.add_row("Brokers with Credentials", str(summary['brokers_with_credentials']))
        summary_table.add_row("Max Position Size", f"{summary['trading_config']['max_position_size']:.1%}")
        summary_table.add_row("Risk per Trade", f"{summary['trading_config']['risk_per_trade']:.1%}")
        summary_table.add_row("Max Open Positions", str(summary['trading_config']['max_open_positions']))
        summary_table.add_row("Local AI Model", summary['ai_config']['local_model'])
        summary_table.add_row("API Models", ", ".join(summary['ai_config']['api_models']))
        
        console.print(summary_table)
        
        # Final confirmation
        confirm = await questionary.confirm(
            "\nSave this configuration?",
            default=True
        ).ask_async()
        
        if not confirm:
            return False
        
        # Export configuration for backup
        export_data = self.config_manager.export_config(include_credentials=False)
        backup_file = Path("config_backup.json")
        
        with open(backup_file, 'w') as f:
            json.dump(export_data, f, indent=2)
        
        console.print(f"[green]✅ Configuration backed up to {backup_file}[/green]")
        
        return True
    
    def _validate_float(self, value: str, min_val: float, max_val: float) -> bool:
        """Validate float input"""
        try:
            val = float(value)
            return min_val <= val <= max_val
        except ValueError:
            return False
    
    def _validate_int(self, value: str, min_val: int, max_val: int) -> bool:
        """Validate integer input"""
        try:
            val = int(value)
            return min_val <= val <= max_val
        except ValueError:
            return False

async def main():
    """Main entry point for the setup wizard"""
    wizard = SetupWizard()
    success = await wizard.run_setup()
    
    if success:
        console.print("\n[bold green]🎉 Setup completed successfully![/bold green]")
        console.print("You can now start your trading AI system.")
        return 0
    else:
        console.print("\n[bold red]❌ Setup failed or was cancelled.[/bold red]")
        return 1

if __name__ == "__main__":
    import asyncio
    exit_code = asyncio.run(main())
    sys.exit(exit_code)