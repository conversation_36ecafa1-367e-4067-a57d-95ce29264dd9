# Noryon AI Trading System - Quick Start Guide

🚀 **Welcome to Noryon!** This guide will help you set up and run the AI-powered trading system in minutes.

## 📋 Prerequisites

- Python 3.8+ installed
- Git (optional, for cloning)
- API keys for LLM providers (optional, for AI features)
- Broker API credentials (for live trading)

## 🛠️ Installation & Setup

### Step 1: Install Dependencies

```bash
# Install required Python packages
pip install -r requirements.txt
```

### Step 2: Run Initial Setup

```bash
# Run the setup script to initialize the system
python setup_noryon.py

# For configuration-only setup (faster)
python setup_noryon.py --config-only

# For setup with comprehensive testing
python setup_noryon.py --test-mode
```

This will:
- Create necessary directory structure
- Initialize configuration files
- Set up default LLM providers
- Test system components

### Step 3: Configure LLM Providers (Optional)

To enable AI-powered trading decisions, add your LLM API keys:

```bash
# Add OpenAI API key
python -c "from core.config.config_manager import ConfigManager; cm = ConfigManager('config'); cm.save_llm_api_key('openai', 'your-api-key-here')"

# Add Anthropic API key
python -c "from core.config.config_manager import ConfigManager; cm = ConfigManager('config'); cm.save_llm_api_key('anthropic', 'your-api-key-here')"

# Add DeepSeek API key (cost-effective option)
python -c "from core.config.config_manager import ConfigManager; cm = ConfigManager('config'); cm.save_llm_api_key('deepseek', 'your-api-key-here')"
```

### Step 4: Configure Brokers (For Live Trading)

Run the setup wizard to configure your brokers:

```bash
python -m core.setup.setup_wizard
```

Supported brokers:
- Interactive Brokers (IBKR)
- Alpaca
- TD Ameritrade
- And more...

## 🚀 Running the System

### Development Mode (Recommended for Testing)

```bash
# Run in dry-run mode (no actual trades)
python main.py --dry-run

# Run in development environment
python main.py --environment development --dry-run
```

### Production Mode (Live Trading)

⚠️ **Warning**: Only use after thorough testing!

```bash
# Run in production mode
python main.py --environment production
```

## 🧪 Testing the System

### Run Comprehensive Tests

```bash
# Run all integration tests
python -m tests.test_llm_integration

# Run specific test suites
python -c "import asyncio; from tests.test_llm_integration import TestRunner; asyncio.run(TestRunner().run_all_tests())"
```

### Test Individual Components

```bash
# Test LLM layer
python -c "import asyncio; from core.llm.llm_abstraction_layer import LLMAbstractionLayer; from core.config.config_manager import ConfigManager; asyncio.run(LLMAbstractionLayer(ConfigManager('config')).initialize())"

# Test data manager
python -c "import asyncio; from core.data.data_manager import DataManager; from core.config.config_manager import ConfigManager; asyncio.run(DataManager(ConfigManager('config'), 'data/cache', 'data').initialize())"
```

## 📊 System Architecture Overview

```
┌─────────────────┐    ┌──────────────────┐    ┌─────────────────┐
│   Data Sources  │───▶│   Data Manager   │───▶│   LLM Brain     │
│ (Market, News)  │    │  (Processing)    │    │ (AI Decisions)  │
└─────────────────┘    └──────────────────┘    └─────────────────┘
                                                         │
                                                         ▼
┌─────────────────┐    ┌──────────────────┐    ┌─────────────────┐
│   Brokers       │◀───│  Portfolio Mgr   │◀───│  Risk Manager   │
│ (Execution)     │    │  (Coordination)  │    │ (Validation)    │
└─────────────────┘    └──────────────────┘    └─────────────────┘
```

## 🔧 Configuration Files

After setup, you'll find configuration files in:

```
config/
├── ai/
│   └── models.yaml          # AI model configurations
├── trading/
│   └── strategies.yaml      # Trading strategies
├── brokers/
│   └── connections.yaml     # Broker configurations
├── credentials/
│   ├── llm_providers.json   # Encrypted LLM API keys
│   └── brokers.json         # Encrypted broker credentials
└── llm_brain_config.yaml    # LLM Brain settings
```

## 🎯 Key Features

### 🤖 AI-Powered Decision Making
- Multiple LLM provider support (OpenAI, Anthropic, DeepSeek, Local models)
- Intelligent fallback and load balancing
- Cost optimization and rate limiting
- Real-time market analysis

### 📈 Multi-Broker Support
- Universal broker interface
- Automatic failover
- Portfolio aggregation across brokers
- Risk management across all positions

### 🛡️ Risk Management
- Position sizing algorithms
- Stop-loss and take-profit automation
- Portfolio-level risk controls
- Real-time monitoring

### 📊 Data Management
- Real-time market data
- News sentiment analysis
- Economic indicators
- Social media sentiment
- Historical backtesting data

## 🔍 Monitoring & Debugging

### View System Status

```bash
# Check configuration
python -c "from core.config.config_manager import ConfigManager; print(ConfigManager('config').get_config_summary())"

# View logs
tail -f logs/trading_ai_*.log

# Monitor performance
watch -n 5 "python -c 'from core.config.config_manager import ConfigManager; print(ConfigManager(\"config\").get_config_summary())'"
```

### Common Issues

1. **LLM API Errors**: Check API keys and rate limits
2. **Broker Connection Issues**: Verify credentials and network
3. **Data Feed Problems**: Check data source configurations
4. **Permission Errors**: Ensure proper file permissions

## 🚀 Next Steps

1. **Customize Trading Strategies**: Edit `config/trading/strategies.yaml`
2. **Add Custom Indicators**: Extend the data manager
3. **Implement Custom Risk Rules**: Modify risk management logic
4. **Set Up Monitoring**: Configure alerts and notifications
5. **Backtest Strategies**: Use historical data for validation

## 📚 Advanced Usage

### Using Local LLM Models

```bash
# Download and set up Qwen model (example)
mkdir -p models/local/qwen
# Download model files to models/local/qwen/

# Update configuration to use local model
python -c "from core.config.config_manager import ConfigManager; cm = ConfigManager('config'); cm.update_llm_provider('qwen_local', model_path='models/local/qwen', enabled=True)"
```

### Custom Data Sources

```python
# Add custom data source
from core.data.data_manager import DataManager

# Extend DataManager class with your custom sources
class CustomDataManager(DataManager):
    async def get_custom_data(self):
        # Your custom data logic here
        pass
```

### API Integration

```python
# Use Noryon components in your own code
from core.llm.llm_abstraction_layer import LLMAbstractionLayer
from core.config.config_manager import ConfigManager

async def my_trading_logic():
    config = ConfigManager('config')
    llm = LLMAbstractionLayer(config)
    await llm.initialize()
    
    response = await llm.generate(
        prompt="Analyze AAPL stock for trading opportunity",
        max_tokens=500
    )
    
    print(response.content)
```

## 🆘 Support

- **Documentation**: Check the `/docs` folder for detailed documentation
- **Logs**: System logs are in `/logs` directory
- **Configuration**: All configs are in `/config` directory
- **Tests**: Run tests to verify system health

## ⚠️ Important Notes

1. **Always test in dry-run mode first**
2. **Start with small position sizes**
3. **Monitor system performance closely**
4. **Keep API keys secure**
5. **Regular backups of configuration**
6. **Stay within API rate limits**
7. **Comply with broker terms of service**

---

🎉 **You're ready to start trading with AI!** 

Remember: Start small, test thoroughly, and gradually increase complexity as you become comfortable with the system.