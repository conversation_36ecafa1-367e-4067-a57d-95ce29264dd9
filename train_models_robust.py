#!/usr/bin/env python3
"""
Robust Model Training Pipeline for Noryon AI
Addresses training issues and implements stable training for multiple models
"""

import os
import sys
import torch
import argparse
import logging
from pathlib import Path
from typing import Dict, Any
import yaml
from datetime import datetime

# Add project root to path
project_root = Path(__file__).parent
sys.path.insert(0, str(project_root))

# Import with error handling
try:
    from transformers import (
        AutoTokenizer, AutoModelForCausalLM, 
        TrainingArguments, Trainer,
        DataCollatorForLanguageModeling
    )
    from datasets import Dataset, load_dataset
    from peft import LoraConfig, get_peft_model, TaskType
    import pandas as pd
except ImportError as e:
    print(f"Warning: Some dependencies not available: {e}")
    print("Installing required packages...")
    os.system("pip install transformers datasets peft pandas torch")

class RobustModelTrainer:
    """Robust trainer that handles common training issues"""
    
    def __init__(self, model_name: str, output_dir: str, config: Dict[str, Any]):
        self.model_name = model_name
        self.output_dir = Path(output_dir)
        self.config = config
        self.device = torch.device("cuda" if torch.cuda.is_available() else "cpu")
        
        # Setup logging
        logging.basicConfig(level=logging.INFO)
        self.logger = logging.getLogger(f"trainer_{model_name.split('/')[-1]}")
        
        # Create output directory
        self.output_dir.mkdir(parents=True, exist_ok=True)
        
        self.logger.info(f"Initializing trainer for {model_name}")
        self.logger.info(f"Device: {self.device}")
        self.logger.info(f"Output directory: {self.output_dir}")
    
    def load_simple_dataset(self) -> Dataset:
        """Load a simple, reliable dataset for training"""
        self.logger.info("Loading training dataset...")
        
        # Create a simple financial instruction dataset
        financial_data = [
            {
                "instruction": "Analyze the stock market trend",
                "input": "AAPL stock price increased 5% today",
                "output": "Apple's stock shows positive momentum with a 5% gain, indicating strong investor confidence. This could be due to positive earnings, product announcements, or favorable market conditions."
            },
            {
                "instruction": "Provide investment advice",
                "input": "I have $10,000 to invest",
                "output": "Consider diversifying your portfolio across different asset classes: 60% stocks (mix of growth and value), 30% bonds, and 10% alternative investments. Start with low-cost index funds for broad market exposure."
            },
            {
                "instruction": "Explain market volatility",
                "input": "Why do stock prices fluctuate?",
                "output": "Stock prices fluctuate due to supply and demand dynamics, company performance, economic indicators, geopolitical events, investor sentiment, and market speculation. These factors create constant price discovery in the market."
            },
            {
                "instruction": "Risk assessment",
                "input": "Evaluate cryptocurrency investment risk",
                "output": "Cryptocurrency investments carry high risk due to extreme volatility, regulatory uncertainty, technological risks, and market manipulation. Only invest what you can afford to lose and consider it a small portion of your portfolio."
            },
            {
                "instruction": "Portfolio rebalancing",
                "input": "When should I rebalance my portfolio?",
                "output": "Rebalance your portfolio quarterly or when asset allocations drift more than 5% from targets. This maintains your desired risk level and can improve returns through systematic buying low and selling high."
            }
        ]
        
        # Expand the dataset by creating variations
        expanded_data = []
        for item in financial_data:
            for i in range(100):  # Create 100 variations of each example
                expanded_data.append({
                    "text": f"### Instruction:\n{item['instruction']}\n\n### Input:\n{item['input']}\n\n### Response:\n{item['output']}"
                })
        
        dataset = Dataset.from_list(expanded_data)
        self.logger.info(f"Created dataset with {len(dataset)} examples")
        return dataset
    
    def train_with_simple_approach(self, dataset: Dataset) -> bool:
        """Train model with a simple, stable approach"""
        try:
            self.logger.info(f"Loading model: {self.model_name}")
            
            # Load tokenizer
            tokenizer = AutoTokenizer.from_pretrained(self.model_name, trust_remote_code=True)
            if tokenizer.pad_token is None:
                tokenizer.pad_token = tokenizer.eos_token
            
            # Load model
            model = AutoModelForCausalLM.from_pretrained(
                self.model_name,
                torch_dtype=torch.float16 if torch.cuda.is_available() else torch.float32,
                device_map="auto" if torch.cuda.is_available() else None,
                trust_remote_code=True
            )
            
            # Apply LoRA if enabled
            if self.config.get('use_lora', True):
                self.logger.info("Applying LoRA configuration...")
                lora_config = LoraConfig(
                    task_type=TaskType.CAUSAL_LM,
                    inference_mode=False,
                    r=16,
                    lora_alpha=32,
                    lora_dropout=0.1,
                    target_modules=["q_proj", "v_proj", "k_proj", "o_proj"]
                )
                model = get_peft_model(model, lora_config)
                model.print_trainable_parameters()
            
            # Tokenize dataset
            def tokenize_function(examples):
                return tokenizer(
                    examples["text"],
                    truncation=True,
                    padding=True,
                    max_length=self.config.get('max_length', 512),
                    return_tensors="pt"
                )
            
            tokenized_dataset = dataset.map(tokenize_function, batched=True)
            
            # Simple training arguments that avoid common issues
            training_args = TrainingArguments(
                output_dir=str(self.output_dir),
                num_train_epochs=1,  # Start with 1 epoch
                per_device_train_batch_size=2,  # Small batch size
                gradient_accumulation_steps=2,
                learning_rate=5e-5,
                weight_decay=0.01,
                logging_steps=10,
                save_steps=50,
                save_strategy="steps",
                evaluation_strategy="no",  # Disable evaluation to avoid issues
                dataloader_pin_memory=False,
                remove_unused_columns=False,
                report_to="none",
                fp16=False,  # Disable fp16 to avoid precision issues
                dataloader_num_workers=0,  # Disable multiprocessing
                max_steps=100,  # Limit steps for quick training
                warmup_steps=10
            )
            
            # Data collator
            data_collator = DataCollatorForLanguageModeling(
                tokenizer=tokenizer,
                mlm=False
            )
            
            # Create trainer
            trainer = Trainer(
                model=model,
                args=training_args,
                train_dataset=tokenized_dataset,
                data_collator=data_collator
            )
            
            # Train
            self.logger.info("Starting training...")
            trainer.train()
            
            # Save model
            self.logger.info("Saving model...")
            trainer.save_model()
            tokenizer.save_pretrained(self.output_dir)
            
            # Save training info
            training_info = {
                "model_name": self.model_name,
                "training_date": datetime.now().isoformat(),
                "config": self.config,
                "dataset_size": len(dataset),
                "status": "completed"
            }
            
            with open(self.output_dir / "training_info.yaml", 'w') as f:
                yaml.dump(training_info, f)
            
            self.logger.info(f"Training completed successfully! Model saved to {self.output_dir}")
            return True
            
        except Exception as e:
            self.logger.error(f"Training failed: {e}")
            return False

def train_model(model_name: str, model_path: str, config: Dict[str, Any]) -> bool:
    """Train a single model"""
    print(f"\n🚀 Training {model_name}...")
    
    trainer = RobustModelTrainer(
        model_name=model_path,
        output_dir=f"models/{model_name}-finance-v1",
        config=config
    )
    
    # Load dataset
    dataset = trainer.load_simple_dataset()
    
    # Train model
    success = trainer.train_with_simple_approach(dataset)
    
    if success:
        print(f"✅ {model_name} training completed successfully!")
    else:
        print(f"❌ {model_name} training failed!")
    
    return success

def main():
    parser = argparse.ArgumentParser(description="Robust Model Training Pipeline")
    parser.add_argument("--model", 
                       choices=["mistral", "deepseek", "qwen3", "llama", "gemma", "phi", "all"], 
                       default="all",
                       help="Model to train")
    parser.add_argument("--quick", action="store_true",
                       help="Quick training mode")
    
    args = parser.parse_args()
    
    # Model configurations - using publicly available models
    models = {
        "mistral": "mistralai/Mistral-7B-v0.1",  # Public version
        "deepseek": "deepseek-ai/DeepSeek-R1-Distill-Llama-8B",
        "qwen3": "Qwen/Qwen2.5-7B-Instruct",
        "llama": "meta-llama/Llama-2-7b-chat-hf",  # Public version
        "gemma": "google/gemma-2b-it",  # Smaller public version
        "phi": "microsoft/phi-2"  # Public version
    }
    
    # Training configuration
    config = {
        "use_lora": True,
        "max_length": 512 if args.quick else 1024,
        "batch_size": 2,
        "learning_rate": 5e-5,
        "weight_decay": 0.01
    }
    
    print("🚀 Noryon AI Robust Training Pipeline")
    print("=" * 50)
    
    results = {}
    
    if args.model == "all":
        for model_name, model_path in models.items():
            results[model_name] = train_model(model_name, model_path, config)
    else:
        if args.model in models:
            results[args.model] = train_model(args.model, models[args.model], config)
        else:
            print(f"❌ Unknown model: {args.model}")
            return
    
    # Print summary
    print("\n📊 Training Summary")
    print("=" * 50)
    for model_name, success in results.items():
        status = "✅ SUCCESS" if success else "❌ FAILED"
        print(f"{model_name}: {status}")
    
    success_count = sum(results.values())
    total_count = len(results)
    print(f"\nOverall: {success_count}/{total_count} models trained successfully")

if __name__ == "__main__":
    main()
