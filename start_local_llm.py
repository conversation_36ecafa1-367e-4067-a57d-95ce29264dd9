#!/usr/bin/env python3
"""
Local LLM Server for Noryon Trading System

This script starts a local LLM server using llama-cpp-python
with the Qwen3-8B model for testing purposes.
"""

import os
import sys
import argparse
from pathlib import Path

def check_model_exists(model_path):
    """Check if the model file exists"""
    if not os.path.exists(model_path):
        print(f"❌ Model file not found: {model_path}")
        return False
    print(f"✅ Model file found: {model_path}")
    return True

def start_llm_server(model_path, host="localhost", port=8000, n_ctx=4096):
    """Start the local LLM server"""
    try:
        print("🚀 Starting Local LLM Server...")
        print(f"📁 Model: {model_path}")
        print(f"🌐 Host: {host}")
        print(f"🔌 Port: {port}")
        print(f"📝 Context: {n_ctx} tokens")
        print("\n" + "="*50)
        
        # Import and start the server
        from llama_cpp.server.app import create_app
        from llama_cpp.server.settings import Settings
        import uvicorn
        
        # Create settings
        settings = Settings(
            model=model_path,
            host=host,
            port=port,
            n_ctx=n_ctx,
            verbose=True
        )
        
        # Create and run the app
        app = create_app(settings=settings)
        
        print(f"\n🎯 LLM Server starting at http://{host}:{port}")
        print("📖 API Documentation: http://localhost:8000/docs")
        print("🔄 Health Check: http://localhost:8000/health")
        print("\n⚡ Server is ready for requests!")
        print("\n🛑 Press Ctrl+C to stop the server")
        
        uvicorn.run(app, host=host, port=port)
        
    except ImportError as e:
        print(f"❌ Missing dependency: {e}")
        print("💡 Install with: pip install llama-cpp-python[server]")
        return False
    except Exception as e:
        print(f"❌ Error starting server: {e}")
        return False

def test_model_loading(model_path):
    """Test if the model can be loaded"""
    try:
        print("🧪 Testing model loading...")
        from llama_cpp import Llama
        
        # Load model with minimal context for testing
        llm = Llama(
            model_path=model_path,
            n_ctx=512,
            verbose=False
        )
        
        print("✅ Model loaded successfully!")
        
        # Test a simple generation
        print("🔄 Testing text generation...")
        response = llm(
            "Hello, I am",
            max_tokens=20,
            temperature=0.1,
            stop=["\n"]
        )
        
        print(f"🎯 Test response: {response['choices'][0]['text']}")
        print("✅ Model is working correctly!")
        return True
        
    except ImportError as e:
        print(f"❌ Missing dependency: {e}")
        print("💡 Install with: pip install llama-cpp-python")
        return False
    except Exception as e:
        print(f"❌ Error testing model: {e}")
        return False

def main():
    parser = argparse.ArgumentParser(description="Start Local LLM Server")
    parser.add_argument("--model", default="qwen3/Qwen3-8B-Q4_K_M.gguf", help="Path to model file")
    parser.add_argument("--host", default="localhost", help="Server host")
    parser.add_argument("--port", type=int, default=8000, help="Server port")
    parser.add_argument("--n-ctx", type=int, default=4096, help="Context size")
    parser.add_argument("--test-only", action="store_true", help="Only test model loading")
    
    args = parser.parse_args()
    
    print("🤖 Noryon Local LLM Server")
    print("="*30)
    
    # Check if model exists
    if not check_model_exists(args.model):
        print("\n📋 Available models in qwen3/:")
        qwen3_dir = Path("qwen3")
        if qwen3_dir.exists():
            for file in qwen3_dir.glob("*.gguf"):
                print(f"  📄 {file.name}")
        sys.exit(1)
    
    if args.test_only:
        # Test model loading only
        success = test_model_loading(args.model)
        sys.exit(0 if success else 1)
    else:
        # Start the server
        start_llm_server(args.model, args.host, args.port, args.n_ctx)

if __name__ == "__main__":
    main()