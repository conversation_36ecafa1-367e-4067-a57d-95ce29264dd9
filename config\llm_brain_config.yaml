# Noryon LLM-Brain Trading System Configuration
# Advanced AI Trading System Configuration File

# System Information
system:
  name: "Noryon LLM-Brain Trading System"
  version: "1.0.0"
  environment: "production"  # development, staging, production
  debug_mode: false
  log_level: "INFO"  # DEBUG, INFO, WARNING, ERROR, CRITICAL

# LLM Provider Configurations
llm_providers:
  # Primary LLM for complex decisions
  openai:
    enabled: false
    api_key: "${OPENAI_API_KEY}"  # Environment variable
    base_url: "https://api.openai.com/v1"
    model: "gpt-4-turbo-preview"
    max_tokens: 4000
    temperature: 0.1  # Low temperature for consistent trading decisions
    timeout: 30
    retry_attempts: 3
    retry_delay: 1.0
    rate_limit:
      requests_per_minute: 500
      tokens_per_minute: 150000
    cost_per_1k_tokens:
      input: 0.01
      output: 0.03
  
  # Secondary LLM for cost-effective decisions
  deepseek:
    enabled: false
    api_key: "${DEEPSEEK_API_KEY}"
    base_url: "https://api.deepseek.com/v1"
    model: "deepseek-chat"
    max_tokens: 4000
    temperature: 0.1
    timeout: 20
    retry_attempts: 3
    retry_delay: 0.5
    rate_limit:
      requests_per_minute: 1000
      tokens_per_minute: 300000
    cost_per_1k_tokens:
      input: 0.0014
      output: 0.0028
  
  # Tertiary LLM for safety and compliance
  anthropic:
    enabled: false
    api_key: "${ANTHROPIC_API_KEY}"
    base_url: "https://api.anthropic.com/v1"
    model: "claude-3-sonnet-20240229"
    max_tokens: 4000
    temperature: 0.1
    timeout: 25
    retry_attempts: 3
    retry_delay: 1.0
    rate_limit:
      requests_per_minute: 300
      tokens_per_minute: 100000
    cost_per_1k_tokens:
      input: 0.003
      output: 0.015
  
  # Fast LLM for real-time decisions
  qwen:
    enabled: false
    api_key: "${QWEN_API_KEY}"
    base_url: "https://dashscope.aliyuncs.com/api/v1"
    model: "qwen-turbo"
    max_tokens: 2000
    temperature: 0.1
    timeout: 10
    retry_attempts: 2
    retry_delay: 0.3
    rate_limit:
      requests_per_minute: 2000
      tokens_per_minute: 500000
    cost_per_1k_tokens:
      input: 0.0008
      output: 0.002
  
  # Local LLM for fallback
  local:
    enabled: true
    model_path: "qwen3/Qwen3-8B-Q4_K_M.gguf"
    device: "cpu"  # cuda, cpu
    max_tokens: 2000
    temperature: 0.1
    timeout: 15

# LLM Selection Strategy
llm_selection:
  primary_llm: "local"
  fallback_llm: "local"
  emergency_llm: "local"
  
  # Task-specific LLM assignment
  task_assignment:
    complex_strategy: "local"
    routine_decisions: "local"
    risk_analysis: "local"
    real_time_signals: "local"
    compliance_check: "local"
    performance_analysis: "local"
  
  # Selection criteria
  selection_criteria:
    cost_weight: 0.3
    performance_weight: 0.4
    latency_weight: 0.2
    reliability_weight: 0.1
  
  # Budget management
  budget:
    daily_limit: 100.0  # USD
    hourly_limit: 10.0  # USD
    cost_alert_threshold: 80.0  # Percentage of daily limit
    emergency_budget: 20.0  # USD for emergency decisions

# Data Sources Configuration
data_sources:
  # Market data providers
  market_data:
    primary_provider: "binance"
    backup_providers: ["coinbase", "kraken"]
    update_frequency: 1  # seconds
    historical_data_retention: 365  # days
  
  # News and sentiment data
  news_sources:
    enabled: true
    providers:
      - name: "alpha_vantage"
        api_key: "${ALPHA_VANTAGE_API_KEY}"
        update_frequency: 300  # seconds
      - name: "news_api"
        api_key: "${NEWS_API_KEY}"
        update_frequency: 600  # seconds
    sentiment_analysis:
      provider: "textblob"  # textblob, vader, transformers
      confidence_threshold: 0.6
  
  # Social media sentiment
  social_media:
    enabled: true
    sources: ["twitter", "reddit"]
    update_frequency: 900  # seconds
    sentiment_weight: 0.2

# Database Configuration
database:
  # Primary database for historical data
  postgresql:
    host: "${POSTGRES_HOST}"
    port: 5432
    database: "noryon_llm_brain"
    username: "${POSTGRES_USER}"
    password: "${POSTGRES_PASSWORD}"
    pool_size: 20
    max_overflow: 30
    pool_timeout: 30
    pool_recycle: 3600
  
  # Redis for real-time caching
  redis:
    host: "${REDIS_HOST}"
    port: 6379
    db: 0
    password: "${REDIS_PASSWORD}"
    max_connections: 50
    socket_timeout: 5
    socket_connect_timeout: 5
    decode_responses: true
    
    # Cache TTL settings
    cache_ttl:
      market_data: 60  # seconds
      decisions: 3600  # seconds
      performance_metrics: 300  # seconds
      news_sentiment: 1800  # seconds

# Risk Management Configuration
risk_management:
  # Position limits
  position_limits:
    max_position_size: 0.1  # 10% of portfolio
    max_single_asset_exposure: 0.3  # 30% of portfolio
    max_sector_exposure: 0.5  # 50% of portfolio
    max_correlation_exposure: 0.4  # 40% for correlated assets
  
  # Loss limits
  loss_limits:
    max_daily_loss: 0.02  # 2% of portfolio
    max_weekly_loss: 0.05  # 5% of portfolio
    max_monthly_loss: 0.1  # 10% of portfolio
    max_drawdown: 0.15  # 15% maximum drawdown
  
  # Risk metrics
  risk_metrics:
    var_confidence: 0.95  # 95% VaR
    var_horizon: 1  # 1 day
    stress_test_scenarios: 10
    correlation_threshold: 0.7
  
  # Emergency procedures
  emergency_procedures:
    auto_liquidate_threshold: 0.08  # 8% daily loss
    position_reduction_threshold: 0.05  # 5% daily loss
    trading_halt_threshold: 0.12  # 12% daily loss
    manual_review_threshold: 0.03  # 3% daily loss

# Trading Configuration
trading:
  # Supported asset classes
  asset_classes:
    crypto:
      enabled: true
      exchanges: ["binance", "coinbase", "kraken"]
      base_currencies: ["USDT", "USD", "EUR"]
      min_trade_size: 10.0  # USD
      max_trade_size: 10000.0  # USD
    
    forex:
      enabled: true
      brokers: ["oanda", "interactive_brokers"]
      base_currencies: ["USD", "EUR", "GBP"]
      min_trade_size: 1000.0  # USD
      max_trade_size: 100000.0  # USD
    
    stocks:
      enabled: false  # To be implemented
      brokers: ["interactive_brokers"]
      min_trade_size: 100.0  # USD
      max_trade_size: 50000.0  # USD
  
  # Order management
  order_management:
    default_order_type: "limit"
    order_timeout: 300  # seconds
    slippage_tolerance: 0.001  # 0.1%
    partial_fill_threshold: 0.8  # 80% fill required
    
    # Stop loss and take profit
    stop_loss:
      enabled: true
      default_percentage: 0.02  # 2%
      trailing_stop: true
      trailing_distance: 0.01  # 1%
    
    take_profit:
      enabled: true
      default_percentage: 0.04  # 4%
      partial_profit_taking: true
      profit_levels: [0.02, 0.04, 0.06]  # 2%, 4%, 6%

# Performance Monitoring
monitoring:
  # Metrics collection
  metrics:
    enabled: true
    collection_interval: 60  # seconds
    retention_period: 90  # days
    
    # Prometheus configuration
    prometheus:
      enabled: true
      port: 9090
      metrics_path: "/metrics"
    
    # Custom metrics
    custom_metrics:
      - name: "llm_decision_latency"
        type: "histogram"
        description: "Time taken for LLM to make decisions"
      - name: "trading_performance_ratio"
        type: "gauge"
        description: "Current trading performance ratio"
      - name: "risk_utilization"
        type: "gauge"
        description: "Current risk utilization percentage"
  
  # Alerting
  alerting:
    enabled: true
    channels:
      email:
        enabled: true
        smtp_server: "${SMTP_SERVER}"
        smtp_port: 587
        username: "${SMTP_USERNAME}"
        password: "${SMTP_PASSWORD}"
        recipients: ["<EMAIL>", "<EMAIL>"]
      
      slack:
        enabled: true
        webhook_url: "${SLACK_WEBHOOK_URL}"
        channel: "#trading-alerts"
      
      discord:
        enabled: false
        webhook_url: "${DISCORD_WEBHOOK_URL}"
    
    # Alert rules
    alert_rules:
      - name: "High Daily Loss"
        condition: "daily_loss > 0.015"  # 1.5%
        severity: "warning"
        cooldown: 300  # seconds
      
      - name: "Critical Daily Loss"
        condition: "daily_loss > 0.025"  # 2.5%
        severity: "critical"
        cooldown: 60  # seconds
      
      - name: "LLM API Failure"
        condition: "llm_error_rate > 0.1"  # 10%
        severity: "warning"
        cooldown: 180  # seconds
      
      - name: "System Latency High"
        condition: "decision_latency > 10"  # seconds
        severity: "warning"
        cooldown: 300  # seconds

# Security Configuration
security:
  # API security
  api_security:
    rate_limiting:
      enabled: true
      requests_per_minute: 1000
      burst_limit: 100
    
    authentication:
      method: "jwt"  # jwt, api_key, oauth
      token_expiry: 3600  # seconds
      refresh_token_expiry: 86400  # seconds
    
    encryption:
      algorithm: "AES-256-GCM"
      key_rotation_interval: 2592000  # 30 days
  
  # Data security
  data_security:
    encryption_at_rest: true
    encryption_in_transit: true
    data_retention_policy: 2555  # days (7 years)
    pii_anonymization: true
  
  # Access control
  access_control:
    rbac_enabled: true
    roles:
      - name: "admin"
        permissions: ["*"]
      - name: "trader"
        permissions: ["read_portfolio", "execute_trades", "view_performance"]
      - name: "analyst"
        permissions: ["read_data", "view_performance", "generate_reports"]
      - name: "risk_manager"
        permissions: ["read_portfolio", "modify_risk_limits", "halt_trading"]

# Backtesting Configuration
backtesting:
  enabled: true
  
  # Data configuration
  data:
    start_date: "2023-01-01"
    end_date: "2024-01-01"
    frequency: "1h"  # 1m, 5m, 15m, 1h, 4h, 1d
    assets: ["BTCUSD", "ETHUSD", "ADAUSD"]
  
  # Simulation parameters
  simulation:
    initial_capital: 100000.0  # USD
    commission: 0.001  # 0.1%
    slippage: 0.0005  # 0.05%
    market_impact: 0.0001  # 0.01%
  
  # Performance metrics
  metrics:
    benchmark: "BTCUSD"  # Benchmark for comparison
    risk_free_rate: 0.02  # 2% annual
    calculate_metrics:
      - "total_return"
      - "sharpe_ratio"
      - "max_drawdown"
      - "win_rate"
      - "profit_factor"
      - "calmar_ratio"

# Optimization Configuration
optimization:
  # Continuous optimization
  continuous_optimization:
    enabled: true
    optimization_interval: 3600  # seconds (1 hour)
    performance_window: 86400  # seconds (24 hours)
    min_trades_for_optimization: 10
  
  # Parameter optimization
  parameter_optimization:
    method: "bayesian"  # grid_search, random_search, bayesian
    max_iterations: 100
    convergence_threshold: 0.001
    
    # Parameters to optimize
    parameters:
      - name: "confidence_threshold"
        type: "float"
        range: [0.5, 0.9]
        step: 0.05
      
      - name: "position_size_multiplier"
        type: "float"
        range: [0.5, 2.0]
        step: 0.1
      
      - name: "stop_loss_percentage"
        type: "float"
        range: [0.01, 0.05]
        step: 0.005
  
  # Strategy adaptation
  strategy_adaptation:
    enabled: true
    adaptation_frequency: 86400  # seconds (daily)
    market_regime_detection: true
    volatility_adjustment: true
    correlation_adjustment: true

# Logging Configuration
logging:
  level: "INFO"
  format: "%(asctime)s - %(name)s - %(levelname)s - %(message)s"
  
  # File logging
  file_logging:
    enabled: true
    log_file: "logs/noryon_llm_brain.log"
    max_file_size: "100MB"
    backup_count: 10
    rotation: "daily"
  
  # Structured logging
  structured_logging:
    enabled: true
    format: "json"
    include_fields:
      - "timestamp"
      - "level"
      - "message"
      - "module"
      - "function"
      - "line_number"
      - "request_id"
      - "user_id"
  
  # Log aggregation
  log_aggregation:
    enabled: true
    service: "elasticsearch"  # elasticsearch, splunk, datadog
    endpoint: "${ELASTICSEARCH_ENDPOINT}"
    index_pattern: "noryon-llm-brain-*"

# Development and Testing
development:
  # Development mode settings
  debug_mode: false
  mock_llm_responses: false
  mock_market_data: false
  
  # Testing configuration
  testing:
    unit_tests:
      coverage_threshold: 0.8  # 80%
      test_timeout: 30  # seconds
    
    integration_tests:
      test_environment: "staging"
      test_data_retention: 7  # days
    
    load_testing:
      max_concurrent_requests: 1000
      test_duration: 300  # seconds
      ramp_up_time: 60  # seconds

# Feature Flags
feature_flags:
  # Core features
  multi_llm_ensemble: true
  real_time_adaptation: true
  advanced_risk_management: true
  sentiment_analysis: true
  
  # Experimental features
  reinforcement_learning: false
  quantum_optimization: false
  cross_market_arbitrage: false
  defi_integration: false
  
  # Beta features
  voice_commands: false
  mobile_notifications: false
  social_trading: false
  copy_trading: false

# Environment Variables Template
# Copy this section to your .env file and fill in the actual values
environment_variables:
  # LLM API Keys
  OPENAI_API_KEY: "your-openai-api-key-here"
  DEEPSEEK_API_KEY: "your-deepseek-api-key-here"
  ANTHROPIC_API_KEY: "your-anthropic-api-key-here"
  QWEN_API_KEY: "your-qwen-api-key-here"
  
  # Database Credentials
  POSTGRES_HOST: "localhost"
  POSTGRES_USER: "noryon_user"
  POSTGRES_PASSWORD: "your-postgres-password"
  REDIS_HOST: "localhost"
  REDIS_PASSWORD: "your-redis-password"
  
  # External API Keys
  ALPHA_VANTAGE_API_KEY: "your-alpha-vantage-key"
  NEWS_API_KEY: "your-news-api-key"
  
  # Notification Services
  SMTP_SERVER: "smtp.gmail.com"
  SMTP_USERNAME: "<EMAIL>"
  SMTP_PASSWORD: "your-email-password"
  SLACK_WEBHOOK_URL: "your-slack-webhook-url"
  DISCORD_WEBHOOK_URL: "your-discord-webhook-url"
  
  # Monitoring
  ELASTICSEARCH_ENDPOINT: "http://localhost:9200"
  
  # Security
  JWT_SECRET_KEY: "your-jwt-secret-key"
  ENCRYPTION_KEY: "your-encryption-key"
  
  # Broker API Keys
  BINANCE_API_KEY: "your-binance-api-key"
  BINANCE_SECRET_KEY: "your-binance-secret-key"
  OANDA_API_KEY: "your-oanda-api-key"
  IB_CLIENT_ID: "your-ib-client-id"
  IB_GATEWAY_HOST: "localhost"
  IB_GATEWAY_PORT: "7497"