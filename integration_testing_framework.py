#!/usr/bin/env python3
"""
Noryon AI Integration Testing Framework
Comprehensive testing before production deployment
"""

import asyncio
import json
import time
import subprocess
from datetime import datetime, timedelta
from typing import Dict, List, Optional
from dataclasses import dataclass
from rich.console import Console
from rich.panel import Panel
from rich.table import Table
from rich.progress import Progress, SpinnerColumn, TextColumn, BarColumn

console = Console()

@dataclass
class TestResult:
    """Test result with detailed metrics"""
    test_name: str
    passed: bool
    execution_time: float
    details: Dict
    error_message: Optional[str] = None

class IntegrationTestSuite:
    """Comprehensive integration testing for Noryon AI system"""
    
    def __init__(self):
        self.test_results = []
        self.models_to_test = [
            "noryon-phi-4-9b-finance:latest",
            "noryon-gemma-3-12b-finance:latest", 
            "noryon-phi-4-9b-enhanced-enhanced:latest",
            "noryon-gemma-3-12b-enhanced-enhanced:latest",
            "noryon-qwen3-finance-v2:latest",
            "noryon-cogito-finance-v2:latest",
            "noryon-marco-o1-finance-v2:latest",
            "noryon-deepscaler-finance-v2:latest"
        ]
    
    async def test_model_availability(self) -> TestResult:
        """Test that all 8 models are available and responsive"""
        start_time = time.time()
        
        try:
            # Check Ollama is running
            result = subprocess.run(['ollama', 'list'], capture_output=True, text=True, timeout=10)
            
            if result.returncode != 0:
                return TestResult(
                    "Model Availability",
                    False,
                    time.time() - start_time,
                    {},
                    "Ollama not running or accessible"
                )
            
            available_models = result.stdout
            missing_models = []
            
            for model in self.models_to_test:
                model_name = model.split(":")[0]
                if model_name not in available_models:
                    missing_models.append(model)
            
            if missing_models:
                return TestResult(
                    "Model Availability",
                    False,
                    time.time() - start_time,
                    {"missing_models": missing_models},
                    f"Missing models: {missing_models}"
                )
            
            return TestResult(
                "Model Availability",
                True,
                time.time() - start_time,
                {"available_models": len(self.models_to_test)}
            )
            
        except Exception as e:
            return TestResult(
                "Model Availability",
                False,
                time.time() - start_time,
                {},
                str(e)
            )
    
    async def test_model_response_quality(self) -> TestResult:
        """Test response quality from all models"""
        start_time = time.time()
        
        test_query = "Analyze AAPL stock at $180 with RSI 65. Provide specific trading recommendation with entry/exit points."
        
        try:
            model_responses = {}
            failed_models = []
            
            for model in self.models_to_test:
                try:
                    result = subprocess.run([
                        'ollama', 'run', model, test_query
                    ], capture_output=True, text=True, timeout=60)
                    
                    if result.returncode == 0:
                        response = result.stdout.strip()
                        quality_score = self._assess_response_quality(response)
                        model_responses[model] = {
                            "response_length": len(response),
                            "quality_score": quality_score,
                            "has_recommendation": any(word in response.lower() for word in ["buy", "sell", "hold", "recommend"])
                        }
                    else:
                        failed_models.append(model)
                        
                except subprocess.TimeoutExpired:
                    failed_models.append(f"{model} (timeout)")
                except Exception as e:
                    failed_models.append(f"{model} ({str(e)})")
            
            avg_quality = sum(r["quality_score"] for r in model_responses.values()) / len(model_responses) if model_responses else 0
            
            success = len(failed_models) == 0 and avg_quality >= 70
            
            return TestResult(
                "Model Response Quality",
                success,
                time.time() - start_time,
                {
                    "successful_models": len(model_responses),
                    "failed_models": failed_models,
                    "average_quality": avg_quality,
                    "model_details": model_responses
                },
                f"Failed models: {failed_models}" if failed_models else None
            )
            
        except Exception as e:
            return TestResult(
                "Model Response Quality",
                False,
                time.time() - start_time,
                {},
                str(e)
            )
    
    def _assess_response_quality(self, response: str) -> float:
        """Assess quality of model response"""
        score = 0
        
        # Length check
        if len(response) > 100:
            score += 20
        if len(response) > 300:
            score += 10
        
        # Financial keywords
        financial_keywords = ["price", "target", "risk", "analysis", "market", "trading", "investment"]
        keyword_count = sum(1 for keyword in financial_keywords if keyword.lower() in response.lower())
        score += min(keyword_count * 5, 30)
        
        # Specific recommendations
        recommendation_words = ["buy", "sell", "hold", "recommend", "suggest", "target", "stop"]
        rec_count = sum(1 for word in recommendation_words if word.lower() in response.lower())
        score += min(rec_count * 5, 25)
        
        # Numbers (prices, percentages)
        import re
        numbers = re.findall(r'\$?\d+\.?\d*%?', response)
        score += min(len(numbers) * 2, 15)
        
        return min(score, 100)
    
    async def test_ensemble_consensus(self) -> TestResult:
        """Test ensemble decision-making process"""
        start_time = time.time()
        
        try:
            from ensemble_voting_system import AdvancedEnsembleVoting, ModelVote, ActionType
            
            # Create sample votes for testing
            test_votes = [
                ModelVote("noryon-phi-4-9b-finance", "risk_assessment", ActionType.BUY, 0.85, 185.0, 175.0, 0.03, "Test reasoning", 0.15, 2.0),
                ModelVote("noryon-gemma-3-12b-finance", "market_analysis", ActionType.BUY, 0.78, 187.0, 177.0, 0.04, "Test reasoning", 0.15, 2.2),
                ModelVote("noryon-qwen3-finance-v2", "multilingual_analysis", ActionType.HOLD, 0.72, 182.0, 175.0, 0.02, "Test reasoning", 0.13, 3.0),
                ModelVote("noryon-cogito-finance-v2", "cognitive_analysis", ActionType.BUY, 0.88, 190.0, 180.0, 0.03, "Test reasoning", 0.13, 2.5)
            ]
            
            ensemble = AdvancedEnsembleVoting()
            decision = ensemble.calculate_weighted_consensus(test_votes)
            
            # Validate decision structure
            required_fields = ["final_action", "ensemble_confidence", "consensus_strength", "price_target", "stop_loss"]
            missing_fields = [field for field in required_fields if not hasattr(decision, field)]
            
            if missing_fields:
                return TestResult(
                    "Ensemble Consensus",
                    False,
                    time.time() - start_time,
                    {"missing_fields": missing_fields},
                    f"Missing decision fields: {missing_fields}"
                )
            
            # Validate decision quality
            valid_decision = (
                decision.ensemble_confidence > 0.5 and
                decision.consensus_strength > 0.0 and
                decision.price_target > 0 and
                decision.stop_loss > 0
            )
            
            return TestResult(
                "Ensemble Consensus",
                valid_decision,
                time.time() - start_time,
                {
                    "final_action": decision.final_action.value,
                    "ensemble_confidence": decision.ensemble_confidence,
                    "consensus_strength": decision.consensus_strength,
                    "participating_models": decision.participating_models
                }
            )
            
        except Exception as e:
            return TestResult(
                "Ensemble Consensus",
                False,
                time.time() - start_time,
                {},
                str(e)
            )
    
    async def test_risk_management(self) -> TestResult:
        """Test risk management systems"""
        start_time = time.time()
        
        try:
            from production_deployment_system import NoryonProductionSystem
            
            system = NoryonProductionSystem()
            
            # Test risk limits
            risk_limits = system.risk_limits
            required_limits = ["max_position_size", "max_portfolio_risk", "max_daily_loss", "min_confidence"]
            
            missing_limits = [limit for limit in required_limits if limit not in risk_limits]
            
            if missing_limits:
                return TestResult(
                    "Risk Management",
                    False,
                    time.time() - start_time,
                    {"missing_limits": missing_limits},
                    f"Missing risk limits: {missing_limits}"
                )
            
            # Test risk validation
            from production_deployment_system import TradingSignal
            
            # Create test signal that should pass
            valid_signal = TradingSignal(
                symbol="AAPL",
                action="BUY",
                confidence=0.85,
                price_target=185.0,
                stop_loss=175.0,
                position_size=0.03,
                reasoning="Test signal",
                model_source="test",
                timestamp=datetime.now()
            )
            
            # Create test signal that should fail
            invalid_signal = TradingSignal(
                symbol="AAPL",
                action="BUY",
                confidence=0.50,  # Below threshold
                price_target=185.0,
                stop_loss=175.0,
                position_size=0.10,  # Too large
                reasoning="Test signal",
                model_source="test",
                timestamp=datetime.now()
            )
            
            valid_passes = system.validate_signal_against_risk_limits(valid_signal)
            invalid_fails = not system.validate_signal_against_risk_limits(invalid_signal)
            
            return TestResult(
                "Risk Management",
                valid_passes and invalid_fails,
                time.time() - start_time,
                {
                    "valid_signal_passes": valid_passes,
                    "invalid_signal_fails": invalid_fails,
                    "risk_limits_count": len(risk_limits)
                }
            )
            
        except Exception as e:
            return TestResult(
                "Risk Management",
                False,
                time.time() - start_time,
                {},
                str(e)
            )
    
    async def test_performance_monitoring(self) -> TestResult:
        """Test performance monitoring capabilities"""
        start_time = time.time()
        
        try:
            from production_deployment_system import PerformanceMonitor, TradingSignal
            
            monitor = PerformanceMonitor()
            
            # Create test signal and outcome
            test_signal = TradingSignal(
                symbol="AAPL",
                action="BUY",
                confidence=0.85,
                price_target=185.0,
                stop_loss=175.0,
                position_size=0.03,
                reasoning="Test signal",
                model_source="test",
                timestamp=datetime.now()
            )
            
            test_outcome = {
                "pnl": 0.05,  # 5% profit
                "direction_correct": True,
                "execution_time": 1.2
            }
            
            # Test tracking
            monitor.track_signal_performance(test_signal, test_outcome)
            
            # Verify metrics updated
            metrics_updated = (
                monitor.daily_pnl == 0.05 and
                monitor.win_rate == 1.0 and
                len(monitor.performance_db) == 1
            )
            
            return TestResult(
                "Performance Monitoring",
                metrics_updated,
                time.time() - start_time,
                {
                    "daily_pnl": monitor.daily_pnl,
                    "win_rate": monitor.win_rate,
                    "tracked_signals": len(monitor.performance_db)
                }
            )
            
        except Exception as e:
            return TestResult(
                "Performance Monitoring",
                False,
                time.time() - start_time,
                {},
                str(e)
            )
    
    async def run_comprehensive_tests(self) -> Dict:
        """Run all integration tests"""
        console.print(Panel(
            "[bold blue]🧪 Noryon AI Integration Testing[/bold blue]\n\n"
            "Running comprehensive tests before production deployment:\n"
            "• Model Availability & Responsiveness\n"
            "• Response Quality Assessment\n"
            "• Ensemble Decision Making\n"
            "• Risk Management Systems\n"
            "• Performance Monitoring",
            title="Integration Testing"
        ))
        
        tests = [
            ("Model Availability", self.test_model_availability),
            ("Response Quality", self.test_model_response_quality),
            ("Ensemble Consensus", self.test_ensemble_consensus),
            ("Risk Management", self.test_risk_management),
            ("Performance Monitoring", self.test_performance_monitoring)
        ]
        
        with Progress(
            SpinnerColumn(),
            TextColumn("[progress.description]{task.description}"),
            BarColumn(),
            TextColumn("[progress.percentage]{task.percentage:>3.0f}%"),
        ) as progress:
            
            for test_name, test_func in tests:
                task = progress.add_task(f"[cyan]{test_name}", total=100)
                progress.update(task, completed=20)
                
                result = await test_func()
                self.test_results.append(result)
                
                progress.update(task, completed=100)
                
                if result.passed:
                    console.print(f"[green]✅ {test_name} PASSED[/green]")
                else:
                    console.print(f"[red]❌ {test_name} FAILED: {result.error_message}[/red]")
        
        return self._generate_test_report()
    
    def _generate_test_report(self) -> Dict:
        """Generate comprehensive test report"""
        passed_tests = sum(1 for result in self.test_results if result.passed)
        total_tests = len(self.test_results)
        success_rate = passed_tests / total_tests if total_tests > 0 else 0
        
        # Results table
        results_table = Table(title="Integration Test Results")
        results_table.add_column("Test", style="cyan")
        results_table.add_column("Status", style="green")
        results_table.add_column("Duration", style="yellow")
        results_table.add_column("Details", style="blue")
        
        for result in self.test_results:
            status = "✅ PASS" if result.passed else "❌ FAIL"
            duration = f"{result.execution_time:.2f}s"
            details = result.error_message if not result.passed else "All checks passed"
            
            results_table.add_row(result.test_name, status, duration, details)
        
        console.print(results_table)
        
        # Summary
        status = "🎯 READY FOR PRODUCTION" if success_rate == 1.0 else "⚠️ NEEDS ATTENTION" if success_rate >= 0.8 else "❌ NOT READY"
        
        console.print(Panel(
            f"[bold green]Integration Testing Complete![/bold green]\n\n"
            f"Tests Passed: {passed_tests}/{total_tests}\n"
            f"Success Rate: {success_rate:.1%}\n"
            f"Status: {status}\n\n"
            f"{'🚀 System ready for production deployment!' if success_rate == 1.0 else '🔧 Address failed tests before deployment'}",
            title="Test Summary"
        ))
        
        return {
            "passed_tests": passed_tests,
            "total_tests": total_tests,
            "success_rate": success_rate,
            "ready_for_production": success_rate == 1.0,
            "test_results": self.test_results
        }

async def main():
    """Run integration testing"""
    console.print("[bold blue]🧪 Starting Noryon AI Integration Testing...[/bold blue]\n")
    
    test_suite = IntegrationTestSuite()
    report = await test_suite.run_comprehensive_tests()
    
    return report

if __name__ == "__main__":
    asyncio.run(main())
