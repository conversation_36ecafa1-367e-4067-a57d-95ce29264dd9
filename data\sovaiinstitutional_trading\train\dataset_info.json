{"builder_name": "parquet", "citation": "", "config_name": "default", "dataset_name": "institutional_trading", "dataset_size": 123750854, "description": "", "download_checksums": {"hf://datasets/sovai/institutional_trading@fe180a750732f93e38fb1557c976a2ecbd344772/institutional_trading.parquet": {"num_bytes": 96958235, "checksum": null}}, "download_size": 96958235, "features": {"ticker": {"dtype": "string", "_type": "Value"}, "date": {"dtype": "timestamp[ns]", "_type": "Value"}, "std_percentoftotal_fund_median": {"dtype": "float64", "_type": "Value"}, "std_put_call_ratio_fund_median": {"dtype": "float64", "_type": "Value"}, "std_derivative_ratio_fund_median": {"dtype": "float64", "_type": "Value"}, "derivative_ratio_fund_median": {"dtype": "float64", "_type": "Value"}, "put_call_ratio_fund_median": {"dtype": "float64", "_type": "Value"}, "fund_ratio_fund_median": {"dtype": "float64", "_type": "Value"}, "debt_ratio_fund_median": {"dtype": "float64", "_type": "Value"}, "preferred_ratio_fund_median": {"dtype": "float64", "_type": "Value"}, "totalvalue_median": {"dtype": "float64", "_type": "Value"}, "percentoftotal_median": {"dtype": "float64", "_type": "Value"}, "shrholdings_growth_median": {"dtype": "float64", "_type": "Value"}, "totalvalue_growth_median": {"dtype": "float64", "_type": "Value"}, "put_call_ratio_fund_growth_median": {"dtype": "float64", "_type": "Value"}, "derivative_ratio_fund_growth_median": {"dtype": "float64", "_type": "Value"}, "market_tilt_pca_median": {"dtype": "float64", "_type": "Value"}, "sector_tilt_pca_median": {"dtype": "float64", "_type": "Value"}, "strategy_tilt_pca_median": {"dtype": "float64", "_type": "Value"}, "quantitative_tilt_pca_median": {"dtype": "float64", "_type": "Value"}, "instrument_tilt_pca_median": {"dtype": "float64", "_type": "Value"}, "weight_variability_median": {"dtype": "float64", "_type": "Value"}, "weight_mean_median": {"dtype": "float64", "_type": "Value"}, "weight_coff_variance_median": {"dtype": "float64", "_type": "Value"}, "weight_max_median": {"dtype": "float64", "_type": "Value"}, "weight_kurtosis_median": {"dtype": "float64", "_type": "Value"}, "weight_skew_median": {"dtype": "float64", "_type": "Value"}, "num_investments_median": {"dtype": "float64", "_type": "Value"}, "new_investments_median": {"dtype": "float64", "_type": "Value"}, "divestments_median": {"dtype": "float64", "_type": "Value"}, "new_investments_to_divestments_median": {"dtype": "float64", "_type": "Value"}, "portfolio_turnover_median": {"dtype": "float64", "_type": "Value"}, "net_change_to_investments_median": {"dtype": "float64", "_type": "Value"}, "uncorrelated_percentile_median": {"dtype": "float64", "_type": "Value"}, "flow_percentage_mean_median": {"dtype": "float64", "_type": "Value"}, "performance_value_mean_median": {"dtype": "float64", "_type": "Value"}, "fund_return_quarter_median": {"dtype": "float64", "_type": "Value"}, "fund_flows_percent_quarter_median": {"dtype": "float64", "_type": "Value"}, "std_percentoftotal_fund_std": {"dtype": "float64", "_type": "Value"}, "std_put_call_ratio_fund_std": {"dtype": "float64", "_type": "Value"}, "std_derivative_ratio_fund_std": {"dtype": "float64", "_type": "Value"}, "derivative_ratio_fund_std": {"dtype": "float64", "_type": "Value"}, "put_call_ratio_fund_std": {"dtype": "float64", "_type": "Value"}, "fund_ratio_fund_std": {"dtype": "float64", "_type": "Value"}, "debt_ratio_fund_std": {"dtype": "float64", "_type": "Value"}, "preferred_ratio_fund_std": {"dtype": "float64", "_type": "Value"}, "totalvalue_std": {"dtype": "float64", "_type": "Value"}, "percentoftotal_std": {"dtype": "float64", "_type": "Value"}, "shrholdings_growth_std": {"dtype": "float64", "_type": "Value"}, "totalvalue_growth_std": {"dtype": "float64", "_type": "Value"}, "put_call_ratio_fund_growth_std": {"dtype": "float64", "_type": "Value"}, "derivative_ratio_fund_growth_std": {"dtype": "float64", "_type": "Value"}, "market_tilt_pca_std": {"dtype": "float64", "_type": "Value"}, "sector_tilt_pca_std": {"dtype": "float64", "_type": "Value"}, "strategy_tilt_pca_std": {"dtype": "float64", "_type": "Value"}, "quantitative_tilt_pca_std": {"dtype": "float64", "_type": "Value"}, "instrument_tilt_pca_std": {"dtype": "float64", "_type": "Value"}, "weight_variability_std": {"dtype": "float64", "_type": "Value"}, "weight_mean_std": {"dtype": "float64", "_type": "Value"}, "weight_coff_variance_std": {"dtype": "float64", "_type": "Value"}, "weight_max_std": {"dtype": "float64", "_type": "Value"}, "weight_kurtosis_std": {"dtype": "float64", "_type": "Value"}, "weight_skew_std": {"dtype": "float64", "_type": "Value"}, "num_investments_std": {"dtype": "float64", "_type": "Value"}, "new_investments_std": {"dtype": "float64", "_type": "Value"}, "divestments_std": {"dtype": "float64", "_type": "Value"}, "new_investments_to_divestments_std": {"dtype": "float64", "_type": "Value"}, "portfolio_turnover_std": {"dtype": "float64", "_type": "Value"}, "net_change_to_investments_std": {"dtype": "float64", "_type": "Value"}, "uncorrelated_percentile_std": {"dtype": "float64", "_type": "Value"}, "flow_percentage_mean_std": {"dtype": "float64", "_type": "Value"}, "performance_value_mean_std": {"dtype": "float64", "_type": "Value"}, "fund_return_quarter_std": {"dtype": "float64", "_type": "Value"}, "fund_flows_percent_quarter_std": {"dtype": "float64", "_type": "Value"}, "totalvalue": {"dtype": "float64", "_type": "Value"}, "total_derivatives": {"dtype": "float64", "_type": "Value"}, "percentoftotal": {"dtype": "float64", "_type": "Value"}, "growth_totalvalue": {"dtype": "float64", "_type": "Value"}, "growth_shrholders": {"dtype": "float64", "_type": "Value"}, "growth_shrvalue": {"dtype": "float64", "_type": "Value"}, "growth_percentoftotal": {"dtype": "float64", "_type": "Value"}, "growth_shrholder_value_divergence": {"dtype": "float64", "_type": "Value"}, "diversification_score_ticker": {"dtype": "float64", "_type": "Value"}, "derivative_ratio_ticker": {"dtype": "float64", "_type": "Value"}, "derivative_holder_ratio": {"dtype": "float64", "_type": "Value"}, "derivative_holder_value_divergence": {"dtype": "float64", "_type": "Value"}, "short_interest": {"dtype": "float64", "_type": "Value"}, "security_concentration": {"dtype": "float64", "_type": "Value"}, "put_call_ratio_ticker": {"dtype": "float64", "_type": "Value"}, "put_call_holder_ratio": {"dtype": "float64", "_type": "Value"}, "put_holder_value_sentiment_divergence": {"dtype": "float64", "_type": "Value"}, "value_per_holder": {"dtype": "float64", "_type": "Value"}, "debt_equity_ratio": {"dtype": "float64", "_type": "Value"}, "historical_high_sharevalue": {"dtype": "float64", "_type": "Value"}, "percentage_from_high_sharevalue": {"dtype": "float64", "_type": "Value"}, "previous_high_sharevalue": {"dtype": "float64", "_type": "Value"}, "percentage_above_previous_high": {"dtype": "float64", "_type": "Value"}, "overweight": {"dtype": "float64", "_type": "Value"}, "allocation_pressure_percentile": {"dtype": "float64", "_type": "Value"}, "net_flows_sum": {"dtype": "float64", "_type": "Value"}, "net_flows_max": {"dtype": "float64", "_type": "Value"}, "net_flows_std": {"dtype": "float64", "_type": "Value"}, "net_flows_mean": {"dtype": "float64", "_type": "Value"}, "net_flows_inflow_outflow_value_ratio": {"dtype": "float64", "_type": "Value"}, "net_flows_inflow_outflow_count_ratio": {"dtype": "float64", "_type": "Value"}, "appreciation_value_sum": {"dtype": "float64", "_type": "Value"}, "new_value_sum": {"dtype": "float64", "_type": "Value"}, "turnover_percentage_median": {"dtype": "float64", "_type": "Value"}, "quarter_return": {"dtype": "float64", "_type": "Value"}, "quarter_flows": {"dtype": "float64", "_type": "Value"}, "derivative_overloaded": {"dtype": "float64", "_type": "Value"}, "put_overloaded": {"dtype": "float64", "_type": "Value"}}, "homepage": "", "license": "", "size_in_bytes": 220709089, "splits": {"train": {"name": "train", "num_bytes": 123750854, "num_examples": 136081, "dataset_name": "institutional_trading"}}, "version": {"version_str": "0.0.0", "major": 0, "minor": 0, "patch": 0}}