# Import dependencies
import numpy as np
import pandas as pd
import matplotlib.pyplot as plt
import yfinance as yf
import datetime as dt
yf.pdr_override()

# input
symbol = "AAPL"
start = dt.date.today() - dt.timedelta(days=365 * 2)
end = dt.date.today()

# Read data
df = yf.download(symbol, start, end)

df["MA_High"] = df["High"].rolling(10).mean()
df["MA_Low"] = df["Low"].rolling(10).mean()
df = df.dropna()

plt.figure(figsize=(16, 10))
plt.plot(df["Adj Close"])
plt.plot(df["MA_High"])
plt.plot(df["MA_Low"])
plt.title("Moving Average of High and Low for Stock")
plt.legend(loc="best")
plt.xlabel("Price")
plt.ylabel("Date")
plt.show()

# # Candlestick with Moving Averages High and Low
from matplotlib import dates as mdates

df["VolumePositive"] = df["Open"] < df["Adj Close"]
df = df.dropna()
df = df.reset_index()
df["Date"] = mdates.date2num(df["Date"].tolist())

from mplfinance.original_flavor import candlestick_ohlc

fig = plt.figure(figsize=(20, 16))
ax1 = plt.subplot(2, 1, 1)
candlestick_ohlc(ax1, df.values, width=0.5, colorup="g", colordown="r", alpha=1.0)
ax1.plot(df.Date, df["MA_High"], label="MA High")
ax1.plot(df.Date, df["MA_Low"], label="MA Low")
ax1.xaxis_date()
ax1.xaxis.set_major_formatter(mdates.DateFormatter("%d-%m-%Y"))
# ax1.axhline(y=dfc['Adj Close'].mean(),color='r')
ax1v = ax1.twinx()
colors = df.VolumePositive.map({True: "g", False: "r"})
ax1v.bar(df.Date, df["Volume"], color=colors, alpha=0.4)
ax1v.axes.yaxis.set_ticklabels([])
ax1v.set_ylim(0, 3 * df.Volume.max())
ax1.set_title("Stock " + symbol + " Closing Price")
ax1.set_ylabel("Price")
ax1.set_xlabel("Date")
ax1.legend(loc="best")
plt.show()