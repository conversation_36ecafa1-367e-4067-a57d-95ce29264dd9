#!/usr/bin/env python3
"""
Noryon Trading AI System
Main application entry point

A flexible, broker-agnostic trading AI system that supports multiple
brokers, exchanges, and asset types through a universal interface.
"""

import asyncio
import logging
import signal
import sys
import argparse
from pathlib import Path
from typing import Optional, Dict, Any
from datetime import datetime

# Add project root to Python path
project_root = Path(__file__).parent
sys.path.insert(0, str(project_root))

from core.config.config_manager import ConfigManager, get_global_config_manager
from core.registry.broker_registry import BrokerRegistry
from core.portfolio.universal_portfolio_manager import UniversalPortfolioManager
from core.setup.setup_wizard import SetupWizard
from core.llm.llm_abstraction_layer import LLMAbstractionLayer
from core.data.data_manager import DataManager
from llm_brain_architecture import LLMBrainTradingSystem

# Rich console for beautiful output
from rich.console import Console
from rich.panel import Panel
from rich.table import Table
from rich.live import Live
from rich.layout import Layout
from rich.text import Text
from rich.progress import Progress, SpinnerColumn, TextColumn

console = Console()

class TradingAISystem:
    """Main trading AI system orchestrator"""
    
    def __init__(self, config_path: str = "config", environment: str = "development"):
        self.config_manager = ConfigManager(config_path, environment)
        self.broker_registry = BrokerRegistry()
        self.portfolio_manager = UniversalPortfolioManager(self.config_manager)
        
        # LLM and AI components
        self.llm_layer: Optional[LLMAbstractionLayer] = None
        self.data_manager: Optional[DataManager] = None
        self.llm_brain: Optional[LLMBrainTradingSystem] = None
        
        # System state
        self.is_running = False
        self.shutdown_event = asyncio.Event()
        
        # Performance tracking
        self.start_time: Optional[datetime] = None
        self.last_update: Optional[datetime] = None
        
        # Setup logging
        self._setup_logging()
        
        logger = logging.getLogger(__name__)
        logger.info(f"Trading AI System initialized - Environment: {environment}")
    
    def _setup_logging(self):
        """Setup logging configuration"""
        log_level = getattr(logging, self.config_manager.system_config.log_level.upper())
        
        # Create logs directory
        logs_dir = Path("logs")
        logs_dir.mkdir(exist_ok=True)
        
        # Configure logging
        logging.basicConfig(
            level=log_level,
            format='%(asctime)s - %(name)s - %(levelname)s - %(message)s',
            handlers=[
                logging.FileHandler(logs_dir / f"trading_ai_{datetime.now().strftime('%Y%m%d')}.log"),
                logging.StreamHandler(sys.stdout)
            ]
        )
        
        # Set third-party loggers to WARNING to reduce noise
        logging.getLogger('urllib3').setLevel(logging.WARNING)
        logging.getLogger('requests').setLevel(logging.WARNING)
        logging.getLogger('websockets').setLevel(logging.WARNING)
    
    async def initialize(self) -> bool:
        """Initialize the trading system"""
        try:
            logger = logging.getLogger(__name__)
            
            with Progress(
                SpinnerColumn(),
                TextColumn("[progress.description]{task.description}"),
                console=console
            ) as progress:
                
                # Step 1: Validate configuration
                task1 = progress.add_task("Validating configuration...", total=None)
                issues = self.config_manager.validate_configuration()
                
                if issues['errors']:
                    console.print("\n[red]❌ Configuration Errors:[/red]")
                    for error in issues['errors']:
                        console.print(f"  • {error}")
                    return False
                
                if issues['warnings']:
                    console.print("\n[yellow]⚠️  Configuration Warnings:[/yellow]")
                    for warning in issues['warnings']:
                        console.print(f"  • {warning}")
                
                progress.update(task1, completed=True)
                
                # Step 2: Initialize broker registry
                task2 = progress.add_task("Initializing broker registry...", total=None)
                await self.broker_registry.initialize()
                progress.update(task2, completed=True)
                
                # Step 3: Discover and load brokers
                task3 = progress.add_task("Discovering brokers...", total=None)
                discovered_brokers = await self.broker_registry.discover_brokers()
                
                if not discovered_brokers:
                    console.print("[yellow]⚠️  No brokers discovered. Run setup wizard first.[/yellow]")
                    return False
                
                progress.update(task3, completed=True)
                
                # Step 4: Connect to brokers
                task4 = progress.add_task("Connecting to brokers...", total=None)
                connected_brokers = await self._connect_brokers(discovered_brokers)
                
                if not connected_brokers:
                    console.print("[red]❌ Failed to connect to any brokers[/red]")
                    return False
                
                progress.update(task4, completed=True)
                
                # Step 5: Initialize portfolio manager
                task5 = progress.add_task("Initializing portfolio manager...", total=None)
                
                # Register connected brokers with portfolio manager
                for broker_name, broker in connected_brokers.items():
                    self.portfolio_manager.register_broker(broker_name, broker)
                
                await self.portfolio_manager.initialize()
                progress.update(task5, completed=True)
                
                # Step 6: Initialize LLM layer
                task6 = progress.add_task("Initializing LLM layer...", total=None)
                await self._initialize_llm_layer()
                progress.update(task6, completed=True)
                
                # Step 7: Initialize data manager
                task7 = progress.add_task("Initializing data manager...", total=None)
                await self._initialize_data_manager()
                progress.update(task7, completed=True)
                
                # Step 8: Initialize LLM Brain
                task8 = progress.add_task("Initializing LLM Brain...", total=None)
                await self._initialize_llm_brain()
                progress.update(task8, completed=True)
            
            self.start_time = datetime.utcnow()
            logger.info(f"Trading AI System initialized successfully with {len(connected_brokers)} brokers and LLM capabilities")
            
            return True
        
        except Exception as e:
            logger = logging.getLogger(__name__)
            logger.error(f"Failed to initialize trading system: {e}")
            console.print(f"[red]❌ Initialization failed: {e}[/red]")
            return False
    
    async def _connect_brokers(self, broker_configs: Dict[str, Any]) -> Dict[str, Any]:
        """Connect to all configured brokers"""
        connected_brokers = {}
        logger = logging.getLogger(__name__)
        
        for broker_name, config in broker_configs.items():
            try:
                # Get credentials
                credentials = self.config_manager.get_broker_credentials(broker_name)
                if not credentials:
                    console.print(f"[yellow]⚠️  No credentials for {broker_name}, skipping[/yellow]")
                    continue
                
                # Create broker instance
                broker = await self.broker_registry.create_broker_instance(
                    broker_name, config, credentials
                )
                
                if broker:
                    # Test connection
                    if await broker.connect():
                        connected_brokers[broker_name] = broker
                        console.print(f"[green]✅ Connected to {broker_name}[/green]")
                    else:
                        console.print(f"[red]❌ Failed to connect to {broker_name}[/red]")
                else:
                    console.print(f"[red]❌ Failed to create {broker_name} instance[/red]")
            
            except Exception as e:
                logger.error(f"Error connecting to {broker_name}: {e}")
                console.print(f"[red]❌ Error connecting to {broker_name}: {e}[/red]")
        
        return connected_brokers
    
    async def _initialize_llm_layer(self):
        """Initialize LLM abstraction layer"""
        logger = logging.getLogger(__name__)
        
        try:
            self.llm_layer = LLMAbstractionLayer(
                config_manager=self.config_manager
            )
            
            # Initialize all configured providers
            await self.llm_layer.initialize()
            
            # Test LLM connectivity
            test_response = await self.llm_layer.generate(
                prompt="System initialization test. Respond with 'OK' if functioning.",
                max_tokens=10
            )
            
            if test_response.success:
                console.print("[green]✅ LLM layer initialized and tested[/green]")
                logger.info("LLM layer initialized successfully")
            else:
                console.print(f"[yellow]⚠️ LLM test failed: {test_response.error}[/yellow]")
                logger.warning(f"LLM test failed: {test_response.error}")
            
        except Exception as e:
            logger.error(f"Failed to initialize LLM layer: {e}")
            console.print(f"[yellow]⚠️ LLM layer initialization failed: {e}[/yellow]")
            # Don't raise - system can work without LLM initially
    
    async def _initialize_data_manager(self):
        """Initialize data management system"""
        logger = logging.getLogger(__name__)
        
        try:
            config_path = Path(self.config_manager.config_dir)
            
            self.data_manager = DataManager(
                config_manager=self.config_manager,
                cache_dir=config_path.parent / 'data' / 'cache',
                storage_dir=config_path.parent / 'data'
            )
            
            # Initialize data sources
            await self.data_manager.initialize()
            
            console.print("[green]✅ Data manager initialized[/green]")
            logger.info("Data manager initialized successfully")
            
        except Exception as e:
            logger.error(f"Failed to initialize data manager: {e}")
            console.print(f"[yellow]⚠️ Data manager initialization failed: {e}[/yellow]")
            # Don't raise - system can work without data manager initially
    
    async def _initialize_llm_brain(self):
        """Initialize LLM Brain trading system"""
        logger = logging.getLogger(__name__)
        
        try:
            if not self.llm_layer or not self.data_manager:
                console.print("[yellow]⚠️ Skipping LLM Brain - dependencies not available[/yellow]")
                return
            
            # Load LLM Brain configuration
            brain_config_path = Path(self.config_manager.config_dir) / 'llm_brain_config.yaml'
            
            self.llm_brain = LLMBrainTradingSystem(
                config_path=brain_config_path,
                llm_layer=self.llm_layer,
                data_manager=self.data_manager,
                dry_run=True  # Start in dry-run mode for safety
            )
            
            # Initialize the brain
            await self.llm_brain.initialize()
            
            console.print("[green]✅ LLM Brain initialized[/green]")
            logger.info("LLM Brain initialized successfully")
            
        except Exception as e:
            logger.error(f"Failed to initialize LLM Brain: {e}")
            console.print(f"[yellow]⚠️ LLM Brain initialization failed: {e}[/yellow]")
            # Don't raise - system can work without LLM Brain initially
    
    async def run(self):
        """Run the main trading system loop"""
        if not await self.initialize():
            return False
        
        self.is_running = True
        logger = logging.getLogger(__name__)
        
        # Setup signal handlers for graceful shutdown
        for sig in [signal.SIGINT, signal.SIGTERM]:
            signal.signal(sig, self._signal_handler)
        
        console.print(Panel.fit(
            "[bold green]🚀 Trading AI System Started[/bold green]\n"
            "System is now running and monitoring markets.\n"
            "Press Ctrl+C to stop gracefully.",
            border_style="green"
        ))
        
        try:
            # Create dashboard layout
            layout = self._create_dashboard_layout()
            
            with Live(layout, refresh_per_second=1, console=console) as live:
                while self.is_running and not self.shutdown_event.is_set():
                    try:
                        # Update portfolio metrics
                        await self.portfolio_manager.update_portfolio_metrics()
                        
                        # Update dashboard
                        self._update_dashboard(layout)
                        
                        # Main trading logic would go here
                        # For now, we'll just monitor
                        
                        # Wait before next iteration
                        await asyncio.sleep(1)
                        
                        self.last_update = datetime.utcnow()
                    
                    except asyncio.CancelledError:
                        break
                    except Exception as e:
                        logger.error(f"Error in main loop: {e}")
                        await asyncio.sleep(5)  # Wait before retrying
        
        except KeyboardInterrupt:
            console.print("\n[yellow]Shutdown requested by user[/yellow]")
        
        finally:
            await self.shutdown()
        
        return True
    
    def _signal_handler(self, signum, frame):
        """Handle shutdown signals"""
        console.print(f"\n[yellow]Received signal {signum}, initiating graceful shutdown...[/yellow]")
        self.shutdown_event.set()
        self.is_running = False
    
    def _create_dashboard_layout(self) -> Layout:
        """Create the dashboard layout"""
        layout = Layout()
        
        layout.split_column(
            Layout(name="header", size=3),
            Layout(name="main"),
            Layout(name="footer", size=3)
        )
        
        layout["main"].split_row(
            Layout(name="left"),
            Layout(name="right")
        )
        
        layout["left"].split_column(
            Layout(name="portfolio"),
            Layout(name="positions")
        )
        
        layout["right"].split_column(
            Layout(name="brokers"),
            Layout(name="system")
        )
        
        return layout
    
    def _update_dashboard(self, layout: Layout):
        """Update the dashboard with current data"""
        # Header
        header_text = Text("Noryon Trading AI System", style="bold blue")
        if self.start_time:
            uptime = datetime.utcnow() - self.start_time
            header_text.append(f" | Uptime: {str(uptime).split('.')[0]}", style="green")
        
        layout["header"].update(Panel(header_text, border_style="blue"))
        
        # Portfolio summary
        portfolio_summary = self.portfolio_manager.get_portfolio_summary()
        if portfolio_summary:
            portfolio_table = Table(title="Portfolio Overview", show_header=True)
            portfolio_table.add_column("Metric", style="cyan")
            portfolio_table.add_column("Value", style="white")
            
            portfolio_table.add_row("Total Equity", f"${portfolio_summary.get('total_equity', 0):,.2f}")
            portfolio_table.add_row("Total P&L", f"${portfolio_summary.get('total_pnl', 0):,.2f}")
            portfolio_table.add_row("Open Positions", str(portfolio_summary.get('open_positions', 0)))
            portfolio_table.add_row("Win Rate", f"{portfolio_summary.get('win_rate', 0):.1f}%")
            portfolio_table.add_row("Risk Level", portfolio_summary.get('risk_level', 'Unknown'))
            
            layout["portfolio"].update(portfolio_table)
        
        # Broker status
        broker_table = Table(title="Broker Status", show_header=True)
        broker_table.add_column("Broker", style="cyan")
        broker_table.add_column("Status", style="white")
        broker_table.add_column("Positions", style="white")
        
        for broker_name, broker in self.portfolio_manager.brokers.items():
            status = "🟢 Connected" if broker.is_connected() else "🔴 Disconnected"
            positions = len(self.portfolio_manager.get_positions_by_broker(broker_name))
            broker_table.add_row(broker_name.title(), status, str(positions))
        
        layout["brokers"].update(broker_table)
        
        # System info
        system_table = Table(title="System Info", show_header=True)
        system_table.add_column("Metric", style="cyan")
        system_table.add_column("Value", style="white")
        
        system_table.add_row("Environment", self.config_manager.environment)
        system_table.add_row("Brokers Connected", str(len([b for b in self.portfolio_manager.brokers.values() if b.is_connected()])))
        system_table.add_row("Last Update", self.last_update.strftime("%H:%M:%S") if self.last_update else "Never")
        system_table.add_row("Status", "🟢 Running" if self.is_running else "🔴 Stopped")
        
        layout["system"].update(system_table)
        
        # Footer
        footer_text = Text("Press Ctrl+C to stop | ", style="dim")
        footer_text.append("Noryon Trading AI v1.0", style="bold")
        layout["footer"].update(Panel(footer_text, border_style="dim"))
    
    async def shutdown(self):
        """Gracefully shutdown the system"""
        logger = logging.getLogger(__name__)
        logger.info("Initiating system shutdown...")
        
        with Progress(
            SpinnerColumn(),
            TextColumn("[progress.description]{task.description}"),
            console=console
        ) as progress:
            
            # Save portfolio state
            task1 = progress.add_task("Saving portfolio state...", total=None)
            await self.portfolio_manager.save_positions()
            progress.update(task1, completed=True)
            
            # Disconnect from brokers
            task2 = progress.add_task("Disconnecting from brokers...", total=None)
            for broker_name, broker in self.portfolio_manager.brokers.items():
                try:
                    await broker.disconnect()
                    console.print(f"[green]✅ Disconnected from {broker_name}[/green]")
                except Exception as e:
                    console.print(f"[yellow]⚠️  Error disconnecting from {broker_name}: {e}[/yellow]")
            
            progress.update(task2, completed=True)
        
        self.is_running = False
        logger.info("System shutdown completed")
        console.print(Panel.fit(
            "[bold blue]👋 Trading AI System Stopped[/bold blue]\n"
            "Thank you for using Noryon Trading AI!",
            border_style="blue"
        ))

async def run_setup_wizard():
    """Run the interactive setup wizard"""
    wizard = SetupWizard()
    success = await wizard.run_setup()
    return success

def main():
    """Main entry point"""
    parser = argparse.ArgumentParser(
        description="Noryon Trading AI System",
        formatter_class=argparse.RawDescriptionHelpFormatter,
        epilog="""
Examples:
  python main.py                    # Run with default settings
  python main.py --setup           # Run setup wizard
  python main.py --env production  # Run in production mode
  python main.py --config ./config # Use custom config directory
        """
    )
    
    parser.add_argument(
        "--setup",
        action="store_true",
        help="Run the interactive setup wizard"
    )
    
    parser.add_argument(
        "--env",
        "--environment",
        default="development",
        choices=["development", "staging", "production"],
        help="Environment to run in (default: development)"
    )
    
    parser.add_argument(
        "--config",
        default="config",
        help="Configuration directory path (default: config)"
    )
    
    parser.add_argument(
        "--log-level",
        choices=["DEBUG", "INFO", "WARNING", "ERROR"],
        help="Override log level"
    )
    
    parser.add_argument(
        "--version",
        action="version",
        version="Noryon Trading AI v1.0.0"
    )
    
    args = parser.parse_args()
    
    # Show banner
    console.print(Panel.fit(
        "[bold blue]Noryon Trading AI System[/bold blue]\n"
        "Universal, Flexible, Broker-Agnostic Trading Platform\n"
        "Version 1.0.0",
        border_style="blue"
    ))
    
    try:
        if args.setup:
            # Run setup wizard
            console.print("\n[bold]Starting setup wizard...[/bold]")
            success = asyncio.run(run_setup_wizard())
            if success:
                console.print("\n[green]Setup completed! You can now run the system.[/green]")
                return 0
            else:
                console.print("\n[red]Setup failed or was cancelled.[/red]")
                return 1
        else:
            # Run the trading system
            system = TradingAISystem(
                config_path=args.config,
                environment=args.env
            )
            
            # Override log level if specified
            if args.log_level:
                logging.getLogger().setLevel(getattr(logging, args.log_level))
            
            success = asyncio.run(system.run())
            return 0 if success else 1
    
    except KeyboardInterrupt:
        console.print("\n[yellow]Operation cancelled by user.[/yellow]")
        return 1
    except Exception as e:
        console.print(f"\n[red]Fatal error: {e}[/red]")
        logging.getLogger(__name__).exception("Fatal error occurred")
        return 1

if __name__ == "__main__":
    sys.exit(main())