{"builder_name": "parquet", "citation": "", "config_name": "default", "dataset_name": "all-daily-news", "dataset_size": **********, "description": "", "download_checksums": {"hf://datasets/paperswithbacktest/All-Daily-News@fb71ad53f7fd825cf86e629599482ec855ee5032/data/train-00000-of-00003.parquet": {"num_bytes": 190067155, "checksum": null}, "hf://datasets/paperswithbacktest/All-Daily-News@fb71ad53f7fd825cf86e629599482ec855ee5032/data/train-00001-of-00003.parquet": {"num_bytes": 189118705, "checksum": null}, "hf://datasets/paperswithbacktest/All-Daily-News@fb71ad53f7fd825cf86e629599482ec855ee5032/data/train-00002-of-00003.parquet": {"num_bytes": 185309914, "checksum": null}}, "download_size": 564495774, "features": {"symbols": {"feature": {"dtype": "string", "_type": "Value"}, "_type": "Sequence"}, "datetime": {"dtype": "string", "_type": "Value"}, "title": {"dtype": "string", "_type": "Value"}, "url": {"dtype": "string", "_type": "Value"}, "authors": {"feature": {"dtype": "string", "_type": "Value"}, "_type": "Sequence"}, "summary": {"dtype": "string", "_type": "Value"}, "source": {"dtype": "string", "_type": "Value"}, "topics": [{"relevance_score": {"dtype": "string", "_type": "Value"}, "topic": {"dtype": "string", "_type": "Value"}}], "sentiment": {"dtype": "float64", "_type": "Value"}, "symbol_sentiment": [{"sentiment_score": {"dtype": "float64", "_type": "Value"}, "symbol": {"dtype": "string", "_type": "Value"}}]}, "homepage": "", "license": "", "size_in_bytes": **********, "splits": {"train": {"name": "train", "num_bytes": **********, "num_examples": 1975630, "shard_lengths": [850544, 841543, 283543], "dataset_name": "all-daily-news"}}, "version": {"version_str": "0.0.0", "major": 0, "minor": 0, "patch": 0}}