#!/usr/bin/env python3
"""
Backtesting Framework for Noryon AI Trading System
Historical performance testing of the 9-model ensemble
"""

import pandas as pd
import numpy as np
from datetime import datetime, timedelta
from typing import Dict, List, Tuple
from dataclasses import dataclass
from rich.console import Console
from rich.panel import Panel
from rich.table import Table

console = Console()

@dataclass
class BacktestResult:
    total_return: float
    sharpe_ratio: float
    max_drawdown: float
    win_rate: float
    total_trades: int
    avg_trade_return: float
    volatility: float

class BacktestingFramework:
    """Comprehensive backtesting framework for AI ensemble"""
    
    def __init__(self):
        self.initial_capital = 100000
        self.commission = 0.001  # 0.1% commission
        self.slippage = 0.0005   # 0.05% slippage
        
    def generate_sample_data(self, days: int = 252) -> pd.DataFrame:
        """Generate sample historical data for backtesting"""
        
        dates = pd.date_range(start=datetime.now() - timedelta(days=days), 
                             end=datetime.now(), freq='D')
        
        # Generate realistic price data with trends and volatility
        np.random.seed(42)
        returns = np.random.normal(0.0008, 0.02, len(dates))  # Daily returns
        
        # Add some trend and momentum
        for i in range(1, len(returns)):
            returns[i] += 0.1 * returns[i-1]  # Momentum effect
        
        prices = [100.0]  # Starting price
        for ret in returns[1:]:
            prices.append(prices[-1] * (1 + ret))
        
        # Create DataFrame
        data = pd.DataFrame({
            'date': dates,
            'open': [p * (1 + np.random.normal(0, 0.005)) for p in prices],
            'high': [p * (1 + abs(np.random.normal(0, 0.01))) for p in prices],
            'low': [p * (1 - abs(np.random.normal(0, 0.01))) for p in prices],
            'close': prices,
            'volume': np.random.randint(1000000, 10000000, len(dates))
        })
        
        return data
    
    def simulate_ensemble_signals(self, data: pd.DataFrame) -> pd.DataFrame:
        """Simulate 9-model ensemble trading signals"""
        
        signals = []
        
        for i, row in data.iterrows():
            if i < 20:  # Need some history for analysis
                signals.append({
                    'date': row['date'],
                    'signal': 'HOLD',
                    'confidence': 0.5,
                    'price_target': row['close'],
                    'stop_loss': row['close'] * 0.95,
                    'position_size': 0.0
                })
                continue
            
            # Simulate ensemble decision based on technical indicators
            recent_prices = data.iloc[i-20:i]['close'].values
            
            # Simple moving average strategy
            sma_20 = np.mean(recent_prices)
            current_price = row['close']
            
            # Simulate model consensus
            models_bullish = 0
            
            # Technical models
            if current_price > sma_20:
                models_bullish += 3  # Technical models agree
            
            # Risk models (more conservative)
            volatility = np.std(recent_prices[-10:]) / np.mean(recent_prices[-10:])
            if volatility < 0.02:  # Low volatility
                models_bullish += 2
            
            # Visual analysis (pattern recognition)
            if recent_prices[-1] > recent_prices[-5]:  # Short-term uptrend
                models_bullish += 2
            
            # Cognitive/sentiment (random for simulation)
            if np.random.random() > 0.4:
                models_bullish += 2
            
            # Generate signal based on consensus
            total_models = 9
            consensus = models_bullish / total_models
            
            if consensus >= 0.7:
                signal = 'BUY'
                confidence = consensus
                position_size = min(consensus * 0.03, 0.05)
            elif consensus <= 0.3:
                signal = 'SELL'
                confidence = 1 - consensus
                position_size = min((1-consensus) * 0.03, 0.05)
            else:
                signal = 'HOLD'
                confidence = 0.5
                position_size = 0.0
            
            signals.append({
                'date': row['date'],
                'signal': signal,
                'confidence': confidence,
                'price_target': current_price * (1.05 if signal == 'BUY' else 0.95),
                'stop_loss': current_price * (0.97 if signal == 'BUY' else 1.03),
                'position_size': position_size
            })
        
        return pd.DataFrame(signals)
    
    def run_backtest(self, price_data: pd.DataFrame, signals: pd.DataFrame) -> Tuple[pd.DataFrame, BacktestResult]:
        """Run comprehensive backtest"""
        
        portfolio = []
        cash = self.initial_capital
        position = 0
        entry_price = 0
        
        trades = []
        
        for i, (price_row, signal_row) in enumerate(zip(price_data.iterrows(), signals.iterrows())):
            price_row = price_row[1]
            signal_row = signal_row[1]
            
            current_price = price_row['close']
            signal = signal_row['signal']
            position_size = signal_row['position_size']
            
            # Calculate current portfolio value
            portfolio_value = cash + (position * current_price if position != 0 else 0)
            
            # Execute trades based on signals
            if signal == 'BUY' and position == 0 and position_size > 0:
                # Enter long position
                trade_amount = portfolio_value * position_size
                shares_to_buy = trade_amount / (current_price * (1 + self.commission + self.slippage))
                
                if shares_to_buy * current_price <= cash:
                    position = shares_to_buy
                    entry_price = current_price * (1 + self.commission + self.slippage)
                    cash -= shares_to_buy * entry_price
                    
                    trades.append({
                        'date': signal_row['date'],
                        'action': 'BUY',
                        'price': entry_price,
                        'shares': shares_to_buy,
                        'value': shares_to_buy * entry_price
                    })
            
            elif signal == 'SELL' and position > 0:
                # Exit long position
                exit_price = current_price * (1 - self.commission - self.slippage)
                trade_value = position * exit_price
                cash += trade_value
                
                trades.append({
                    'date': signal_row['date'],
                    'action': 'SELL',
                    'price': exit_price,
                    'shares': position,
                    'value': trade_value,
                    'pnl': trade_value - (position * entry_price)
                })
                
                position = 0
                entry_price = 0
            
            # Record portfolio state
            portfolio.append({
                'date': signal_row['date'],
                'price': current_price,
                'cash': cash,
                'position': position,
                'portfolio_value': portfolio_value,
                'signal': signal,
                'confidence': signal_row['confidence']
            })
        
        portfolio_df = pd.DataFrame(portfolio)
        trades_df = pd.DataFrame(trades)
        
        # Calculate performance metrics
        result = self.calculate_performance_metrics(portfolio_df, trades_df)
        
        return portfolio_df, result
    
    def calculate_performance_metrics(self, portfolio_df: pd.DataFrame, trades_df: pd.DataFrame) -> BacktestResult:
        """Calculate comprehensive performance metrics"""
        
        # Total return
        initial_value = self.initial_capital
        final_value = portfolio_df['portfolio_value'].iloc[-1]
        total_return = (final_value - initial_value) / initial_value
        
        # Daily returns
        portfolio_df['daily_return'] = portfolio_df['portfolio_value'].pct_change()
        daily_returns = portfolio_df['daily_return'].dropna()
        
        # Sharpe ratio (assuming 2% risk-free rate)
        risk_free_rate = 0.02 / 252  # Daily risk-free rate
        excess_returns = daily_returns - risk_free_rate
        sharpe_ratio = excess_returns.mean() / excess_returns.std() * np.sqrt(252) if excess_returns.std() > 0 else 0
        
        # Maximum drawdown
        portfolio_df['cumulative_max'] = portfolio_df['portfolio_value'].cummax()
        portfolio_df['drawdown'] = (portfolio_df['portfolio_value'] - portfolio_df['cumulative_max']) / portfolio_df['cumulative_max']
        max_drawdown = portfolio_df['drawdown'].min()
        
        # Trade statistics
        if len(trades_df) > 0:
            winning_trades = trades_df[trades_df['pnl'] > 0] if 'pnl' in trades_df.columns else pd.DataFrame()
            win_rate = len(winning_trades) / len(trades_df) if len(trades_df) > 0 else 0
            avg_trade_return = trades_df['pnl'].mean() if 'pnl' in trades_df.columns else 0
        else:
            win_rate = 0
            avg_trade_return = 0
        
        # Volatility
        volatility = daily_returns.std() * np.sqrt(252)
        
        return BacktestResult(
            total_return=total_return,
            sharpe_ratio=sharpe_ratio,
            max_drawdown=max_drawdown,
            win_rate=win_rate,
            total_trades=len(trades_df),
            avg_trade_return=avg_trade_return,
            volatility=volatility
        )
    
    def generate_backtest_report(self, result: BacktestResult) -> Table:
        """Generate comprehensive backtest report"""
        
        table = Table(title="📊 Backtest Performance Report")
        table.add_column("Metric", style="cyan")
        table.add_column("Value", style="green")
        table.add_column("Benchmark", style="yellow")
        table.add_column("Status", style="blue")
        
        # Define benchmarks for good performance
        benchmarks = {
            "Total Return": (0.15, "15% annually"),
            "Sharpe Ratio": (1.0, "> 1.0"),
            "Max Drawdown": (-0.15, "< -15%"),
            "Win Rate": (0.55, "> 55%"),
            "Volatility": (0.20, "< 20%")
        }
        
        metrics = [
            ("Total Return", f"{result.total_return:.2%}", benchmarks["Total Return"]),
            ("Sharpe Ratio", f"{result.sharpe_ratio:.2f}", benchmarks["Sharpe Ratio"]),
            ("Max Drawdown", f"{result.max_drawdown:.2%}", benchmarks["Max Drawdown"]),
            ("Win Rate", f"{result.win_rate:.2%}", benchmarks["Win Rate"]),
            ("Total Trades", f"{result.total_trades}", (50, "> 50")),
            ("Avg Trade Return", f"${result.avg_trade_return:.2f}", (0, "> $0")),
            ("Volatility", f"{result.volatility:.2%}", benchmarks["Volatility"])
        ]
        
        for metric_name, value, (benchmark_val, benchmark_str) in metrics:
            # Determine status
            if metric_name == "Total Return":
                status = "✅ Good" if result.total_return > benchmark_val else "⚠️ Below"
            elif metric_name == "Sharpe Ratio":
                status = "✅ Good" if result.sharpe_ratio > benchmark_val else "⚠️ Below"
            elif metric_name == "Max Drawdown":
                status = "✅ Good" if result.max_drawdown > benchmark_val else "❌ High"
            elif metric_name == "Win Rate":
                status = "✅ Good" if result.win_rate > benchmark_val else "⚠️ Below"
            elif metric_name == "Volatility":
                status = "✅ Good" if result.volatility < benchmark_val else "⚠️ High"
            else:
                status = "✅ Good" if float(value.replace('$', '').replace(',', '')) > benchmark_val else "⚠️ Below"
            
            table.add_row(metric_name, value, benchmark_str, status)
        
        return table

async def run_comprehensive_backtest():
    """Run comprehensive backtest of the 9-model ensemble"""
    
    console.print(Panel(
        "[bold blue]📊 Starting Comprehensive Backtest[/bold blue]\n\n"
        "Testing 9-model ensemble performance:\n"
        "• Historical data simulation (1 year)\n"
        "• Ensemble signal generation\n"
        "• Portfolio performance tracking\n"
        "• Risk-adjusted return analysis",
        title="Backtesting Framework"
    ))
    
    framework = BacktestingFramework()
    
    # Generate sample data
    console.print("[yellow]📈 Generating historical market data...[/yellow]")
    price_data = framework.generate_sample_data(days=252)
    
    # Generate ensemble signals
    console.print("[yellow]🤖 Simulating 9-model ensemble signals...[/yellow]")
    signals = framework.simulate_ensemble_signals(price_data)
    
    # Run backtest
    console.print("[yellow]⚡ Running backtest simulation...[/yellow]")
    portfolio_df, result = framework.run_backtest(price_data, signals)
    
    # Display results
    console.print(framework.generate_backtest_report(result))
    
    # Summary
    console.print(Panel(
        f"[bold green]🎉 Backtest Complete![/bold green]\n\n"
        f"Performance Summary:\n"
        f"• Total Return: {result.total_return:.2%}\n"
        f"• Sharpe Ratio: {result.sharpe_ratio:.2f}\n"
        f"• Max Drawdown: {result.max_drawdown:.2%}\n"
        f"• Win Rate: {result.win_rate:.2%}\n"
        f"• Total Trades: {result.total_trades}\n\n"
        f"🎯 Next Steps:\n"
        f"• Optimize ensemble weights\n"
        f"• Test different market conditions\n"
        f"• Implement walk-forward analysis",
        title="Backtest Summary"
    ))

if __name__ == "__main__":
    import asyncio
    asyncio.run(run_comprehensive_backtest())
