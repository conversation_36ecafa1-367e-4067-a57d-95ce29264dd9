#!/usr/bin/env python3
"""
Noryon LLM-Brain Trading System Monitoring

Comprehensive monitoring system for tracking LLM performance, trading metrics,
system health, and automated alerting for the LLM-Brain trading system.

Features:
- Real-time LLM performance monitoring
- Trading performance analytics
- System health monitoring
- Multi-channel alerting (<PERSON><PERSON>, <PERSON>lack, Discord, Telegram)
- Automated anomaly detection
- Performance optimization recommendations
"""

import asyncio
import json
import logging
import time
from datetime import datetime, timedelta
from typing import Dict, List, Optional, Any, Tuple
from dataclasses import dataclass, asdict
from enum import Enum
import statistics
from collections import defaultdict, deque

import aiohttp
import aioredis
import asyncpg
from prometheus_client import Counter, Histogram, Gauge, start_http_server
import smtplib
from email.mime.text import MIMEText
from email.mime.multipart import MIMEMultipart

class AlertSeverity(Enum):
    """Alert severity levels"""
    LOW = "low"
    MEDIUM = "medium"
    HIGH = "high"
    CRITICAL = "critical"

class AlertType(Enum):
    """Types of alerts"""
    SYSTEM_HEALTH = "system_health"
    LLM_PERFORMANCE = "llm_performance"
    TRADING_PERFORMANCE = "trading_performance"
    RISK_MANAGEMENT = "risk_management"
    COST_OPTIMIZATION = "cost_optimization"
    ANOMALY_DETECTION = "anomaly_detection"

@dataclass
class Alert:
    """Alert data structure"""
    id: str
    timestamp: datetime
    alert_type: AlertType
    severity: AlertSeverity
    title: str
    message: str
    details: Dict[str, Any]
    source: str
    acknowledged: bool = False
    resolved: bool = False

@dataclass
class LLMMetrics:
    """LLM performance metrics"""
    provider: str
    model: str
    total_requests: int
    successful_requests: int
    failed_requests: int
    average_response_time: float
    total_tokens_used: int
    total_cost: float
    decision_accuracy: float
    uptime_percentage: float
    error_rate: float

@dataclass
class TradingMetrics:
    """Trading performance metrics"""
    total_return: float
    daily_return: float
    sharpe_ratio: float
    max_drawdown: float
    win_rate: float
    profit_factor: float
    total_trades: int
    winning_trades: int
    losing_trades: int
    average_trade_duration: float
    largest_win: float
    largest_loss: float

@dataclass
class SystemMetrics:
    """System health metrics"""
    service_name: str
    status: str
    cpu_usage: float
    memory_usage: float
    disk_usage: float
    network_io: float
    response_time: float
    error_count: int
    uptime: float

class PrometheusMetrics:
    """Prometheus metrics collection"""
    
    def __init__(self):
        # LLM Metrics
        self.llm_requests_total = Counter(
            'llm_requests_total', 
            'Total LLM requests', 
            ['provider', 'model', 'status']
        )
        
        self.llm_response_time = Histogram(
            'llm_response_time_seconds', 
            'LLM response time', 
            ['provider', 'model']
        )
        
        self.llm_tokens_used = Counter(
            'llm_tokens_used_total', 
            'Total tokens used', 
            ['provider', 'model']
        )
        
        self.llm_cost = Counter(
            'llm_cost_total', 
            'Total LLM cost', 
            ['provider', 'model']
        )
        
        # Trading Metrics
        self.trading_pnl = Gauge(
            'trading_pnl', 
            'Current P&L', 
            ['strategy', 'asset']
        )
        
        self.trading_positions = Gauge(
            'trading_positions', 
            'Current positions', 
            ['asset', 'side']
        )
        
        self.trading_orders = Counter(
            'trading_orders_total', 
            'Total orders', 
            ['asset', 'side', 'status']
        )
        
        # System Metrics
        self.system_cpu_usage = Gauge(
            'system_cpu_usage_percent', 
            'CPU usage percentage', 
            ['service']
        )
        
        self.system_memory_usage = Gauge(
            'system_memory_usage_percent', 
            'Memory usage percentage', 
            ['service']
        )
        
        self.system_response_time = Histogram(
            'system_response_time_seconds', 
            'System response time', 
            ['service', 'endpoint']
        )

class NotificationManager:
    """Multi-channel notification manager"""
    
    def __init__(self, config: Dict[str, Any]):
        self.config = config
        self.email_config = config.get('email', {})
        self.slack_config = config.get('slack', {})
        self.discord_config = config.get('discord', {})
        self.telegram_config = config.get('telegram', {})
        
    async def send_alert(self, alert: Alert) -> bool:
        """Send alert through all configured channels"""
        tasks = []
        
        if self.email_config.get('enabled', False):
            tasks.append(self._send_email(alert))
            
        if self.slack_config.get('enabled', False):
            tasks.append(self._send_slack(alert))
            
        if self.discord_config.get('enabled', False):
            tasks.append(self._send_discord(alert))
            
        if self.telegram_config.get('enabled', False):
            tasks.append(self._send_telegram(alert))
            
        if tasks:
            results = await asyncio.gather(*tasks, return_exceptions=True)
            return any(isinstance(result, bool) and result for result in results)
            
        return False
        
    async def _send_email(self, alert: Alert) -> bool:
        """Send email notification"""
        try:
            msg = MIMEMultipart()
            msg['From'] = self.email_config['from']
            msg['To'] = ', '.join(self.email_config['to'])
            msg['Subject'] = f"[{alert.severity.value.upper()}] {alert.title}"
            
            body = f"""
            Alert Details:
            
            Type: {alert.alert_type.value}
            Severity: {alert.severity.value}
            Time: {alert.timestamp}
            Source: {alert.source}
            
            Message:
            {alert.message}
            
            Details:
            {json.dumps(alert.details, indent=2)}
            """
            
            msg.attach(MIMEText(body, 'plain'))
            
            server = smtplib.SMTP(self.email_config['smtp_host'], self.email_config['smtp_port'])
            server.starttls()
            server.login(self.email_config['username'], self.email_config['password'])
            server.send_message(msg)
            server.quit()
            
            return True
        except Exception as e:
            logging.error(f"Failed to send email alert: {e}")
            return False
            
    async def _send_slack(self, alert: Alert) -> bool:
        """Send Slack notification"""
        try:
            color_map = {
                AlertSeverity.LOW: "good",
                AlertSeverity.MEDIUM: "warning", 
                AlertSeverity.HIGH: "danger",
                AlertSeverity.CRITICAL: "danger"
            }
            
            payload = {
                "channel": self.slack_config['channel'],
                "username": "Noryon LLM-Brain Monitor",
                "icon_emoji": ":robot_face:",
                "attachments": [{
                    "color": color_map.get(alert.severity, "warning"),
                    "title": alert.title,
                    "text": alert.message,
                    "fields": [
                        {"title": "Type", "value": alert.alert_type.value, "short": True},
                        {"title": "Severity", "value": alert.severity.value, "short": True},
                        {"title": "Source", "value": alert.source, "short": True},
                        {"title": "Time", "value": alert.timestamp.isoformat(), "short": True}
                    ],
                    "timestamp": alert.timestamp.timestamp()
                }]
            }
            
            async with aiohttp.ClientSession() as session:
                async with session.post(self.slack_config['webhook_url'], json=payload) as response:
                    return response.status == 200
                    
        except Exception as e:
            logging.error(f"Failed to send Slack alert: {e}")
            return False
            
    async def _send_discord(self, alert: Alert) -> bool:
        """Send Discord notification"""
        try:
            color_map = {
                AlertSeverity.LOW: 0x00ff00,
                AlertSeverity.MEDIUM: 0xffff00,
                AlertSeverity.HIGH: 0xff8000,
                AlertSeverity.CRITICAL: 0xff0000
            }
            
            payload = {
                "username": "Noryon LLM-Brain Monitor",
                "avatar_url": "https://example.com/bot-avatar.png",
                "embeds": [{
                    "title": alert.title,
                    "description": alert.message,
                    "color": color_map.get(alert.severity, 0xffff00),
                    "fields": [
                        {"name": "Type", "value": alert.alert_type.value, "inline": True},
                        {"name": "Severity", "value": alert.severity.value, "inline": True},
                        {"name": "Source", "value": alert.source, "inline": True}
                    ],
                    "timestamp": alert.timestamp.isoformat()
                }]
            }
            
            async with aiohttp.ClientSession() as session:
                async with session.post(self.discord_config['webhook_url'], json=payload) as response:
                    return response.status == 204
                    
        except Exception as e:
            logging.error(f"Failed to send Discord alert: {e}")
            return False
            
    async def _send_telegram(self, alert: Alert) -> bool:
        """Send Telegram notification"""
        try:
            message = f"""
🤖 *Noryon LLM-Brain Alert*

*{alert.title}*

📊 Type: {alert.alert_type.value}
🚨 Severity: {alert.severity.value}
🕐 Time: {alert.timestamp.strftime('%Y-%m-%d %H:%M:%S')}
🔍 Source: {alert.source}

💬 Message:
{alert.message}
            """
            
            payload = {
                "chat_id": self.telegram_config['chat_id'],
                "text": message,
                "parse_mode": "Markdown"
            }
            
            url = f"https://api.telegram.org/bot{self.telegram_config['bot_token']}/sendMessage"
            
            async with aiohttp.ClientSession() as session:
                async with session.post(url, json=payload) as response:
                    return response.status == 200
                    
        except Exception as e:
            logging.error(f"Failed to send Telegram alert: {e}")
            return False

class AnomalyDetector:
    """Anomaly detection for trading and system metrics"""
    
    def __init__(self, window_size: int = 100):
        self.window_size = window_size
        self.metric_history: Dict[str, deque] = defaultdict(lambda: deque(maxlen=window_size))
        
    def add_metric(self, metric_name: str, value: float):
        """Add metric value to history"""
        self.metric_history[metric_name].append(value)
        
    def detect_anomaly(self, metric_name: str, value: float, threshold: float = 2.0) -> bool:
        """Detect if value is anomalous using z-score"""
        history = self.metric_history[metric_name]
        
        if len(history) < 10:  # Need minimum history
            return False
            
        mean = statistics.mean(history)
        stdev = statistics.stdev(history) if len(history) > 1 else 0
        
        if stdev == 0:
            return False
            
        z_score = abs((value - mean) / stdev)
        return z_score > threshold
        
    def get_trend(self, metric_name: str) -> str:
        """Get trend direction for metric"""
        history = list(self.metric_history[metric_name])
        
        if len(history) < 5:
            return "insufficient_data"
            
        recent = history[-5:]
        older = history[-10:-5] if len(history) >= 10 else history[:-5]
        
        if not older:
            return "insufficient_data"
            
        recent_avg = statistics.mean(recent)
        older_avg = statistics.mean(older)
        
        if recent_avg > older_avg * 1.05:
            return "increasing"
        elif recent_avg < older_avg * 0.95:
            return "decreasing"
        else:
            return "stable"

class LLMBrainMonitor:
    """Main monitoring system for LLM-Brain trading system"""
    
    def __init__(self, config: Dict[str, Any]):
        self.config = config
        self.db_pool: Optional[asyncpg.Pool] = None
        self.redis: Optional[aioredis.Redis] = None
        self.prometheus_metrics = PrometheusMetrics()
        self.notification_manager = NotificationManager(config.get('notifications', {}))
        self.anomaly_detector = AnomalyDetector()
        
        # Monitoring intervals
        self.llm_monitor_interval = config.get('monitoring_intervals', {}).get('llm', 30)
        self.trading_monitor_interval = config.get('monitoring_intervals', {}).get('trading', 60)
        self.system_monitor_interval = config.get('monitoring_intervals', {}).get('system', 15)
        
        # Alert thresholds
        self.alert_thresholds = config.get('alert_thresholds', {})
        
        # Running tasks
        self.monitoring_tasks: List[asyncio.Task] = []
        
    async def initialize(self):
        """Initialize monitoring system"""
        # Initialize database connection
        db_config = self.config['database']
        self.db_pool = await asyncpg.create_pool(
            host=db_config['host'],
            port=db_config['port'],
            user=db_config['user'],
            password=db_config['password'],
            database=db_config['database'],
            min_size=2,
            max_size=10
        )
        
        # Initialize Redis connection
        redis_config = self.config['redis']
        self.redis = await aioredis.from_url(
            f"redis://:{redis_config['password']}@{redis_config['host']}:{redis_config['port']}/{redis_config['db']}"
        )
        
        # Start Prometheus metrics server
        prometheus_port = self.config.get('prometheus_port', 8000)
        start_http_server(prometheus_port)
        logging.info(f"Prometheus metrics server started on port {prometheus_port}")
        
    async def start_monitoring(self):
        """Start all monitoring tasks"""
        self.monitoring_tasks = [
            asyncio.create_task(self._monitor_llm_performance()),
            asyncio.create_task(self._monitor_trading_performance()),
            asyncio.create_task(self._monitor_system_health()),
            asyncio.create_task(self._monitor_anomalies()),
            asyncio.create_task(self._generate_reports())
        ]
        
        logging.info("All monitoring tasks started")
        
    async def stop_monitoring(self):
        """Stop all monitoring tasks"""
        for task in self.monitoring_tasks:
            task.cancel()
            
        await asyncio.gather(*self.monitoring_tasks, return_exceptions=True)
        
        if self.db_pool:
            await self.db_pool.close()
            
        if self.redis:
            await self.redis.close()
            
        logging.info("Monitoring stopped")
        
    async def _monitor_llm_performance(self):
        """Monitor LLM performance metrics"""
        while True:
            try:
                async with self.db_pool.acquire() as conn:
                    # Get LLM performance data
                    query = """
                    SELECT 
                        llm_provider,
                        COUNT(*) as total_requests,
                        COUNT(*) FILTER (WHERE decision->>'status' = 'success') as successful_requests,
                        COUNT(*) FILTER (WHERE decision->>'status' = 'error') as failed_requests,
                        AVG(EXTRACT(EPOCH FROM (updated_at - created_at))) as avg_response_time,
                        SUM((decision->>'tokens_used')::int) as total_tokens,
                        SUM((decision->>'cost')::float) as total_cost
                    FROM trading.decisions 
                    WHERE created_at >= NOW() - INTERVAL '1 hour'
                    GROUP BY llm_provider
                    """
                    
                    rows = await conn.fetch(query)
                    
                    for row in rows:
                        provider = row['llm_provider']
                        total_requests = row['total_requests']
                        successful_requests = row['successful_requests']
                        failed_requests = row['failed_requests']
                        avg_response_time = row['avg_response_time'] or 0
                        total_tokens = row['total_tokens'] or 0
                        total_cost = row['total_cost'] or 0
                        
                        error_rate = failed_requests / total_requests if total_requests > 0 else 0
                        
                        # Update Prometheus metrics
                        self.prometheus_metrics.llm_requests_total.labels(
                            provider=provider, model="unknown", status="success"
                        ).inc(successful_requests)
                        
                        self.prometheus_metrics.llm_requests_total.labels(
                            provider=provider, model="unknown", status="error"
                        ).inc(failed_requests)
                        
                        self.prometheus_metrics.llm_response_time.labels(
                            provider=provider, model="unknown"
                        ).observe(avg_response_time)
                        
                        self.prometheus_metrics.llm_tokens_used.labels(
                            provider=provider, model="unknown"
                        ).inc(total_tokens)
                        
                        self.prometheus_metrics.llm_cost.labels(
                            provider=provider, model="unknown"
                        ).inc(total_cost)
                        
                        # Check for alerts
                        await self._check_llm_alerts(provider, error_rate, avg_response_time, total_cost)
                        
                        # Add to anomaly detection
                        self.anomaly_detector.add_metric(f"llm_{provider}_error_rate", error_rate)
                        self.anomaly_detector.add_metric(f"llm_{provider}_response_time", avg_response_time)
                        
            except Exception as e:
                logging.error(f"Error monitoring LLM performance: {e}")
                
            await asyncio.sleep(self.llm_monitor_interval)
            
    async def _monitor_trading_performance(self):
        """Monitor trading performance metrics"""
        while True:
            try:
                async with self.db_pool.acquire() as conn:
                    # Get trading performance data
                    query = """
                    SELECT 
                        COUNT(*) as total_trades,
                        COUNT(*) FILTER (WHERE (filled_quantity * average_price) > (quantity * price)) as winning_trades,
                        SUM(CASE WHEN filled_quantity > 0 THEN 
                            (filled_quantity * average_price) - (quantity * price) 
                            ELSE 0 END) as total_pnl,
                        AVG(EXTRACT(EPOCH FROM (updated_at - created_at))) as avg_trade_duration
                    FROM trading.orders 
                    WHERE created_at >= NOW() - INTERVAL '24 hours'
                    AND status = 'filled'
                    """
                    
                    row = await conn.fetchrow(query)
                    
                    if row:
                        total_trades = row['total_trades'] or 0
                        winning_trades = row['winning_trades'] or 0
                        total_pnl = row['total_pnl'] or 0
                        avg_trade_duration = row['avg_trade_duration'] or 0
                        
                        win_rate = winning_trades / total_trades if total_trades > 0 else 0
                        
                        # Update Prometheus metrics
                        self.prometheus_metrics.trading_pnl.set(total_pnl)
                        
                        # Check for alerts
                        await self._check_trading_alerts(total_pnl, win_rate, total_trades)
                        
                        # Add to anomaly detection
                        self.anomaly_detector.add_metric("trading_pnl", total_pnl)
                        self.anomaly_detector.add_metric("trading_win_rate", win_rate)
                        
            except Exception as e:
                logging.error(f"Error monitoring trading performance: {e}")
                
            await asyncio.sleep(self.trading_monitor_interval)
            
    async def _monitor_system_health(self):
        """Monitor system health metrics"""
        while True:
            try:
                # This would typically integrate with system monitoring tools
                # For now, we'll simulate some basic health checks
                
                services = ['llm-brain-engine', 'database', 'redis', 'api-server']
                
                for service in services:
                    # Simulate health check (replace with actual implementation)
                    cpu_usage = 50.0  # Would get from actual system
                    memory_usage = 60.0  # Would get from actual system
                    response_time = 0.1  # Would measure actual response time
                    
                    # Update Prometheus metrics
                    self.prometheus_metrics.system_cpu_usage.labels(service=service).set(cpu_usage)
                    self.prometheus_metrics.system_memory_usage.labels(service=service).set(memory_usage)
                    
                    # Check for alerts
                    await self._check_system_alerts(service, cpu_usage, memory_usage, response_time)
                    
            except Exception as e:
                logging.error(f"Error monitoring system health: {e}")
                
            await asyncio.sleep(self.system_monitor_interval)
            
    async def _monitor_anomalies(self):
        """Monitor for anomalies in metrics"""
        while True:
            try:
                # Check for anomalies in key metrics
                metrics_to_check = [
                    "llm_openai_error_rate",
                    "llm_deepseek_error_rate", 
                    "trading_pnl",
                    "trading_win_rate"
                ]
                
                for metric_name in metrics_to_check:
                    if metric_name in self.anomaly_detector.metric_history:
                        history = self.anomaly_detector.metric_history[metric_name]
                        if history:
                            latest_value = history[-1]
                            if self.anomaly_detector.detect_anomaly(metric_name, latest_value):
                                await self._send_anomaly_alert(metric_name, latest_value)
                                
            except Exception as e:
                logging.error(f"Error monitoring anomalies: {e}")
                
            await asyncio.sleep(60)  # Check every minute
            
    async def _generate_reports(self):
        """Generate periodic reports"""
        while True:
            try:
                # Generate daily report at midnight
                now = datetime.now()
                if now.hour == 0 and now.minute < 5:  # Run once around midnight
                    await self._generate_daily_report()
                    
            except Exception as e:
                logging.error(f"Error generating reports: {e}")
                
            await asyncio.sleep(300)  # Check every 5 minutes
            
    async def _check_llm_alerts(self, provider: str, error_rate: float, response_time: float, cost: float):
        """Check LLM metrics for alert conditions"""
        thresholds = self.alert_thresholds.get('llm', {})
        
        # Error rate alert
        if error_rate > thresholds.get('error_rate', 0.1):
            alert = Alert(
                id=f"llm_error_rate_{provider}_{int(time.time())}",
                timestamp=datetime.now(),
                alert_type=AlertType.LLM_PERFORMANCE,
                severity=AlertSeverity.HIGH if error_rate > 0.2 else AlertSeverity.MEDIUM,
                title=f"High Error Rate for {provider}",
                message=f"Error rate for {provider} is {error_rate:.2%}, exceeding threshold of {thresholds.get('error_rate', 0.1):.2%}",
                details={"provider": provider, "error_rate": error_rate, "threshold": thresholds.get('error_rate', 0.1)},
                source="llm_monitor"
            )
            await self.notification_manager.send_alert(alert)
            
        # Response time alert
        if response_time > thresholds.get('response_time', 10.0):
            alert = Alert(
                id=f"llm_response_time_{provider}_{int(time.time())}",
                timestamp=datetime.now(),
                alert_type=AlertType.LLM_PERFORMANCE,
                severity=AlertSeverity.MEDIUM,
                title=f"Slow Response Time for {provider}",
                message=f"Response time for {provider} is {response_time:.2f}s, exceeding threshold of {thresholds.get('response_time', 10.0)}s",
                details={"provider": provider, "response_time": response_time, "threshold": thresholds.get('response_time', 10.0)},
                source="llm_monitor"
            )
            await self.notification_manager.send_alert(alert)
            
        # Cost alert
        if cost > thresholds.get('hourly_cost', 100.0):
            alert = Alert(
                id=f"llm_cost_{provider}_{int(time.time())}",
                timestamp=datetime.now(),
                alert_type=AlertType.COST_OPTIMIZATION,
                severity=AlertSeverity.HIGH,
                title=f"High LLM Costs for {provider}",
                message=f"Hourly cost for {provider} is ${cost:.2f}, exceeding threshold of ${thresholds.get('hourly_cost', 100.0)}",
                details={"provider": provider, "cost": cost, "threshold": thresholds.get('hourly_cost', 100.0)},
                source="llm_monitor"
            )
            await self.notification_manager.send_alert(alert)
            
    async def _check_trading_alerts(self, pnl: float, win_rate: float, total_trades: int):
        """Check trading metrics for alert conditions"""
        thresholds = self.alert_thresholds.get('trading', {})
        
        # Daily loss alert
        if pnl < -thresholds.get('daily_loss_limit', 1000.0):
            alert = Alert(
                id=f"trading_daily_loss_{int(time.time())}",
                timestamp=datetime.now(),
                alert_type=AlertType.RISK_MANAGEMENT,
                severity=AlertSeverity.CRITICAL if pnl < -thresholds.get('daily_loss_limit', 1000.0) * 2 else AlertSeverity.HIGH,
                title="Daily Loss Limit Exceeded",
                message=f"Daily P&L is ${pnl:.2f}, exceeding loss limit of ${-thresholds.get('daily_loss_limit', 1000.0)}",
                details={"pnl": pnl, "threshold": -thresholds.get('daily_loss_limit', 1000.0)},
                source="trading_monitor"
            )
            await self.notification_manager.send_alert(alert)
            
        # Low win rate alert
        if win_rate < thresholds.get('min_win_rate', 0.4) and total_trades > 10:
            alert = Alert(
                id=f"trading_win_rate_{int(time.time())}",
                timestamp=datetime.now(),
                alert_type=AlertType.TRADING_PERFORMANCE,
                severity=AlertSeverity.MEDIUM,
                title="Low Win Rate Detected",
                message=f"Win rate is {win_rate:.2%}, below threshold of {thresholds.get('min_win_rate', 0.4):.2%}",
                details={"win_rate": win_rate, "threshold": thresholds.get('min_win_rate', 0.4), "total_trades": total_trades},
                source="trading_monitor"
            )
            await self.notification_manager.send_alert(alert)
            
    async def _check_system_alerts(self, service: str, cpu_usage: float, memory_usage: float, response_time: float):
        """Check system metrics for alert conditions"""
        thresholds = self.alert_thresholds.get('system', {})
        
        # High CPU usage alert
        if cpu_usage > thresholds.get('cpu_usage', 80.0):
            alert = Alert(
                id=f"system_cpu_{service}_{int(time.time())}",
                timestamp=datetime.now(),
                alert_type=AlertType.SYSTEM_HEALTH,
                severity=AlertSeverity.HIGH if cpu_usage > 90.0 else AlertSeverity.MEDIUM,
                title=f"High CPU Usage - {service}",
                message=f"CPU usage for {service} is {cpu_usage:.1f}%, exceeding threshold of {thresholds.get('cpu_usage', 80.0)}%",
                details={"service": service, "cpu_usage": cpu_usage, "threshold": thresholds.get('cpu_usage', 80.0)},
                source="system_monitor"
            )
            await self.notification_manager.send_alert(alert)
            
        # High memory usage alert
        if memory_usage > thresholds.get('memory_usage', 85.0):
            alert = Alert(
                id=f"system_memory_{service}_{int(time.time())}",
                timestamp=datetime.now(),
                alert_type=AlertType.SYSTEM_HEALTH,
                severity=AlertSeverity.HIGH if memory_usage > 95.0 else AlertSeverity.MEDIUM,
                title=f"High Memory Usage - {service}",
                message=f"Memory usage for {service} is {memory_usage:.1f}%, exceeding threshold of {thresholds.get('memory_usage', 85.0)}%",
                details={"service": service, "memory_usage": memory_usage, "threshold": thresholds.get('memory_usage', 85.0)},
                source="system_monitor"
            )
            await self.notification_manager.send_alert(alert)
            
    async def _send_anomaly_alert(self, metric_name: str, value: float):
        """Send anomaly detection alert"""
        trend = self.anomaly_detector.get_trend(metric_name)
        
        alert = Alert(
            id=f"anomaly_{metric_name}_{int(time.time())}",
            timestamp=datetime.now(),
            alert_type=AlertType.ANOMALY_DETECTION,
            severity=AlertSeverity.MEDIUM,
            title=f"Anomaly Detected in {metric_name}",
            message=f"Unusual value detected for {metric_name}: {value:.4f}. Trend: {trend}",
            details={"metric": metric_name, "value": value, "trend": trend},
            source="anomaly_detector"
        )
        await self.notification_manager.send_alert(alert)
        
    async def _generate_daily_report(self):
        """Generate daily performance report"""
        try:
            async with self.db_pool.acquire() as conn:
                # Get daily trading summary
                trading_query = """
                SELECT 
                    COUNT(*) as total_trades,
                    SUM(CASE WHEN filled_quantity > 0 THEN 
                        (filled_quantity * average_price) - (quantity * price) 
                        ELSE 0 END) as total_pnl,
                    COUNT(*) FILTER (WHERE (filled_quantity * average_price) > (quantity * price)) as winning_trades
                FROM trading.orders 
                WHERE DATE(created_at) = CURRENT_DATE - INTERVAL '1 day'
                AND status = 'filled'
                """
                
                trading_row = await conn.fetchrow(trading_query)
                
                # Get LLM usage summary
                llm_query = """
                SELECT 
                    llm_provider,
                    COUNT(*) as requests,
                    SUM((decision->>'cost')::float) as cost
                FROM trading.decisions 
                WHERE DATE(created_at) = CURRENT_DATE - INTERVAL '1 day'
                GROUP BY llm_provider
                """
                
                llm_rows = await conn.fetch(llm_query)
                
                # Generate report
                report = self._format_daily_report(trading_row, llm_rows)
                
                # Send report via email
                if self.notification_manager.email_config.get('daily_reports', False):
                    await self._send_daily_report_email(report)
                    
        except Exception as e:
            logging.error(f"Error generating daily report: {e}")
            
    def _format_daily_report(self, trading_data, llm_data) -> str:
        """Format daily report"""
        total_trades = trading_data['total_trades'] or 0
        total_pnl = trading_data['total_pnl'] or 0
        winning_trades = trading_data['winning_trades'] or 0
        win_rate = winning_trades / total_trades if total_trades > 0 else 0
        
        report = f"""
# Noryon LLM-Brain Daily Report
## Date: {(datetime.now() - timedelta(days=1)).strftime('%Y-%m-%d')}

### Trading Performance
- Total Trades: {total_trades}
- Total P&L: ${total_pnl:.2f}
- Winning Trades: {winning_trades}
- Win Rate: {win_rate:.2%}

### LLM Usage
        """
        
        total_llm_cost = 0
        for row in llm_data:
            provider = row['llm_provider']
            requests = row['requests']
            cost = row['cost'] or 0
            total_llm_cost += cost
            
            report += f"""
- {provider}: {requests} requests, ${cost:.2f}
            """
            
        report += f"""
- **Total LLM Cost: ${total_llm_cost:.2f}**

### System Health
- All services operational
- No critical alerts in the last 24 hours

---
*Generated by Noryon LLM-Brain Monitoring System*
        """
        
        return report
        
    async def _send_daily_report_email(self, report: str):
        """Send daily report via email"""
        try:
            msg = MIMEMultipart()
            msg['From'] = self.notification_manager.email_config['from']
            msg['To'] = ', '.join(self.notification_manager.email_config['to'])
            msg['Subject'] = f"Noryon LLM-Brain Daily Report - {(datetime.now() - timedelta(days=1)).strftime('%Y-%m-%d')}"
            
            msg.attach(MIMEText(report, 'plain'))
            
            server = smtplib.SMTP(
                self.notification_manager.email_config['smtp_host'], 
                self.notification_manager.email_config['smtp_port']
            )
            server.starttls()
            server.login(
                self.notification_manager.email_config['username'], 
                self.notification_manager.email_config['password']
            )
            server.send_message(msg)
            server.quit()
            
            logging.info("Daily report sent successfully")
            
        except Exception as e:
            logging.error(f"Failed to send daily report: {e}")

async def main():
    """Main entry point for monitoring system"""
    # Load configuration
    config = {
        'database': {
            'host': 'localhost',
            'port': 5432,
            'user': 'noryon_user',
            'password': 'your_password',
            'database': 'noryon_llm_brain'
        },
        'redis': {
            'host': 'localhost',
            'port': 6379,
            'password': 'your_password',
            'db': 0
        },
        'notifications': {
            'email': {
                'enabled': True,
                'smtp_host': 'smtp.gmail.com',
                'smtp_port': 587,
                'username': '<EMAIL>',
                'password': 'your_password',
                'from': '<EMAIL>',
                'to': ['<EMAIL>'],
                'daily_reports': True
            },
            'slack': {
                'enabled': True,
                'webhook_url': 'your_slack_webhook_url',
                'channel': '#trading-alerts'
            }
        },
        'alert_thresholds': {
            'llm': {
                'error_rate': 0.1,
                'response_time': 10.0,
                'hourly_cost': 100.0
            },
            'trading': {
                'daily_loss_limit': 1000.0,
                'min_win_rate': 0.4
            },
            'system': {
                'cpu_usage': 80.0,
                'memory_usage': 85.0
            }
        }
    }
    
    # Initialize and start monitoring
    monitor = LLMBrainMonitor(config)
    
    try:
        await monitor.initialize()
        await monitor.start_monitoring()
        
        # Keep running
        while True:
            await asyncio.sleep(60)
            
    except KeyboardInterrupt:
        logging.info("Shutting down monitoring system...")
    finally:
        await monitor.stop_monitoring()

if __name__ == "__main__":
    logging.basicConfig(
        level=logging.INFO,
        format='%(asctime)s - %(name)s - %(levelname)s - %(message)s'
    )
    
    asyncio.run(main())