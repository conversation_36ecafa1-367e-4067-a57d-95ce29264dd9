# Data directories
data/
!data/processed
!data/raw
!data/real_time

# Model files
models/
qwen3/
mistral/
deepseek r1/

# Cache directories
__pycache__/
*.pyc
*.pyo
*.pyd
.Python
build/
develop-eggs/
dist/
downloads/
eggs/
.eggs/
lib/
lib64/
parts/
sdist/
var/
wheels/
*.egg-info/
.installed.cfg
*.egg

# Virtual environments
venv/
env/
ENV/

# IDE files
.vscode/
.idea/
*.swp
*.swo
*~

# OS files
.DS_Store
Thumbs.db

# Logs
logs/
*.log

# Temporary files
temp/
tmp/

# Backup files
backups/

# Test coverage
.coverage
.pytest_cache/

# Git
.git/
.gitignore

# Documentation
docs/
*.md
!README.md

# Large datasets
*.parquet
*.csv
*.zip
*.tar.gz
*.db

# Jupyter notebooks
*.ipynb
.ipynb_checkpoints/

# Environment files
.env.local
.env.development
.env.test
.env.production

# Node modules (if any)
node_modules/

# Docker files
docker-compose.override.yml

# Training checkpoints
checkpoints/
parameters/

# HuggingFace cache
hf_cache/
cache/