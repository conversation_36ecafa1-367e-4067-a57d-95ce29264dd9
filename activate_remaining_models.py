#!/usr/bin/env python3
"""
Activate and Train Remaining Models
Comprehensive approach to train DeepSeek R1, Qwen 3, and Mistral
"""

import os
import sys
import json
import torch
import asyncio
import subprocess
from pathlib import Path
from datetime import datetime
from rich.console import Console
from rich.panel import Panel
from rich.progress import Progress, SpinnerColumn, TextColumn

console = Console()

class RemainingModelTrainer:
    """Train the remaining models with comprehensive fixes"""
    
    def __init__(self):
        self.project_root = Path(__file__).parent
        self.models_to_train = [
            {
                "name": "DeepSeek R1 Local",
                "path": "deepseek r1",
                "output": "models/deepseek-local-trained",
                "type": "local"
            },
            {
                "name": "Qwen 3",
                "path": "qwen3", 
                "output": "models/qwen3-trained",
                "type": "local"
            },
            {
                "name": "Mistral 3",
                "path": "mistral",
                "output": "models/mistral-trained", 
                "type": "local"
            }
        ]
        
    def fix_model_configs(self):
        """Fix configuration issues for all models"""
        console.print("[yellow]🔧 Fixing model configurations...[/yellow]")
        
        fixes_applied = []
        
        # Fix DeepSeek R1 config
        deepseek_config = self.project_root / "deepseek r1" / "config (1).json"
        if deepseek_config.exists():
            try:
                with open(deepseek_config, 'r', encoding='utf-8') as f:
                    config = json.load(f)
                
                # Ensure proper model type
                config["model_type"] = "llama"
                config["torch_dtype"] = "float16"
                config["use_cache"] = True
                
                with open(deepseek_config, 'w', encoding='utf-8') as f:
                    json.dump(config, f, indent=2, ensure_ascii=False)
                
                fixes_applied.append("DeepSeek R1 config fixed")
                console.print("[green]✅ DeepSeek R1 config fixed[/green]")
                
            except Exception as e:
                console.print(f"[red]❌ DeepSeek config fix failed: {e}[/red]")
        
        # Check Qwen 3 structure
        qwen_path = self.project_root / "qwen3"
        if qwen_path.exists():
            # Look for config files
            config_files = list(qwen_path.glob("*config*.json"))
            if config_files:
                fixes_applied.append("Qwen 3 structure verified")
                console.print("[green]✅ Qwen 3 structure verified[/green]")
            else:
                console.print("[yellow]⚠️ Qwen 3 config files not found[/yellow]")
        
        # Check Mistral structure  
        mistral_path = self.project_root / "mistral"
        if mistral_path.exists():
            config_files = list(mistral_path.glob("*config*.json"))
            if config_files:
                fixes_applied.append("Mistral 3 structure verified")
                console.print("[green]✅ Mistral 3 structure verified[/green]")
            else:
                console.print("[yellow]⚠️ Mistral 3 config files not found[/yellow]")
        
        return fixes_applied
    
    async def train_model_with_transformers(self, model_info):
        """Train model using transformers library with comprehensive error handling"""
        console.print(f"[yellow]🚀 Training {model_info['name']} with Transformers...[/yellow]")
        
        try:
            # Set environment for better compatibility
            os.environ['TOKENIZERS_PARALLELISM'] = 'false'
            os.environ['TRANSFORMERS_CACHE'] = str(self.project_root / "cache")
            
            from transformers import (
                AutoTokenizer, AutoModelForCausalLM,
                TrainingArguments, Trainer,
                DataCollatorForLanguageModeling,
                BitsAndBytesConfig
            )
            from datasets import Dataset
            from peft import LoraConfig, get_peft_model, TaskType
            
            model_path = self.project_root / model_info['path']
            output_path = self.project_root / model_info['output']
            output_path.mkdir(parents=True, exist_ok=True)
            
            console.print(f"Loading model from: {model_path}")
            
            # Load tokenizer with error handling
            try:
                tokenizer = AutoTokenizer.from_pretrained(
                    str(model_path),
                    trust_remote_code=True,
                    local_files_only=True,
                    use_fast=False  # Use slow tokenizer for better compatibility
                )
            except Exception as e:
                console.print(f"[red]❌ Tokenizer loading failed: {e}[/red]")
                return False
            
            # Set padding token
            if tokenizer.pad_token is None:
                tokenizer.pad_token = tokenizer.eos_token
                tokenizer.pad_token_id = tokenizer.eos_token_id
            
            # Configure quantization for memory efficiency
            bnb_config = BitsAndBytesConfig(
                load_in_4bit=True,
                bnb_4bit_use_double_quant=True,
                bnb_4bit_quant_type="nf4",
                bnb_4bit_compute_dtype=torch.float16
            )
            
            # Load model with quantization
            try:
                model = AutoModelForCausalLM.from_pretrained(
                    str(model_path),
                    quantization_config=bnb_config,
                    device_map="auto",
                    trust_remote_code=True,
                    local_files_only=True,
                    torch_dtype=torch.float16,
                    low_cpu_mem_usage=True
                )
            except Exception as e:
                console.print(f"[red]❌ Model loading failed: {e}[/red]")
                return False
            
            console.print("[green]✅ Model loaded successfully[/green]")
            
            # Apply LoRA for efficient training
            lora_config = LoraConfig(
                task_type=TaskType.CAUSAL_LM,
                inference_mode=False,
                r=8,  # Reduced rank for stability
                lora_alpha=16,
                lora_dropout=0.1,
                target_modules=["q_proj", "k_proj", "v_proj", "o_proj"],
                bias="none"
            )
            
            model = get_peft_model(model, lora_config)
            model.print_trainable_parameters()
            
            # Create financial dataset
            training_data = self.create_financial_dataset()
            dataset = Dataset.from_list(training_data)
            
            # Tokenize dataset
            def tokenize_function(examples):
                return tokenizer(
                    examples["text"],
                    truncation=True,
                    padding=False,
                    max_length=1024,  # Reduced for stability
                    return_tensors=None
                )
            
            tokenized_dataset = dataset.map(
                tokenize_function,
                batched=True,
                remove_columns=dataset.column_names,
                num_proc=1  # Single process for stability
            )
            
            # Conservative training arguments
            training_args = TrainingArguments(
                output_dir=str(output_path),
                num_train_epochs=1,  # Single epoch for quick training
                per_device_train_batch_size=1,
                gradient_accumulation_steps=4,
                learning_rate=1e-4,  # Conservative learning rate
                weight_decay=0.01,
                warmup_steps=10,
                logging_steps=5,
                save_steps=50,
                save_strategy="steps",
                eval_strategy="no",
                fp16=True,
                dataloader_pin_memory=False,
                remove_unused_columns=False,
                report_to="none",
                dataloader_num_workers=0,
                max_steps=100,  # Quick training
                lr_scheduler_type="linear",
                save_total_limit=1,
                load_best_model_at_end=False
            )
            
            # Data collator
            data_collator = DataCollatorForLanguageModeling(
                tokenizer=tokenizer,
                mlm=False
            )
            
            # Create trainer
            trainer = Trainer(
                model=model,
                args=training_args,
                train_dataset=tokenized_dataset,
                data_collator=data_collator,
                tokenizer=tokenizer
            )
            
            # Train model
            console.print("[yellow]Starting training...[/yellow]")
            trainer.train()
            
            # Save model
            trainer.save_model()
            tokenizer.save_pretrained(output_path)
            
            # Save training info
            training_info = {
                "model_name": model_info['name'],
                "training_date": datetime.now().isoformat(),
                "status": "completed",
                "output_path": str(output_path)
            }
            
            with open(output_path / "training_info.json", 'w') as f:
                json.dump(training_info, f, indent=2)
            
            console.print(f"[green]✅ {model_info['name']} training completed![/green]")
            
            # Clean up memory
            del model, trainer
            torch.cuda.empty_cache() if torch.cuda.is_available() else None
            
            return True
            
        except Exception as e:
            console.print(f"[red]❌ {model_info['name']} training failed: {e}[/red]")
            return False
    
    def create_financial_dataset(self):
        """Create financial training dataset"""
        examples = [
            {
                "instruction": "Analyze stock performance and provide trading recommendations",
                "input": "AAPL stock at $185, strong earnings, growing iPhone sales, expanding services revenue",
                "output": "AAPL Analysis: Strong fundamentals with iPhone growth and services expansion. Current price $185 offers good entry point. Target: $200-210. Stop loss: $175. Position size: 2-3% of portfolio. Risk: Market volatility, supply chain issues."
            },
            {
                "instruction": "Assess portfolio risk and provide optimization suggestions", 
                "input": "Portfolio: 70% stocks, 20% bonds, 10% cash. High tech concentration, low international exposure",
                "output": "Risk Assessment: High equity allocation (70%) with tech concentration creates volatility risk. Recommendations: 1) Reduce tech to 30% of equity, 2) Add international exposure (15-20%), 3) Consider REITs (5-10%), 4) Maintain current bond allocation for stability."
            },
            {
                "instruction": "Provide market outlook and trading strategy",
                "input": "Fed raising rates, inflation moderating, strong employment, geopolitical tensions",
                "output": "Market Outlook: Mixed signals with rate hikes vs strong employment. Strategy: 1) Focus on quality dividend stocks, 2) Reduce duration risk in bonds, 3) Maintain defensive positions, 4) Monitor Fed policy shifts. Sectors: Healthcare, utilities, consumer staples favored."
            }
        ]
        
        training_data = []
        for example in examples:
            for i in range(100):  # 300 total examples
                formatted_text = f"""### Instruction:
{example['instruction']}

### Input:
{example['input']}

### Response:
{example['output']}"""
                training_data.append({"text": formatted_text})
        
        return training_data
    
    async def test_trained_model(self, model_info):
        """Test the trained model"""
        console.print(f"[yellow]🧪 Testing {model_info['name']}...[/yellow]")
        
        try:
            from transformers import AutoTokenizer, AutoModelForCausalLM
            
            output_path = self.project_root / model_info['output']
            
            # Load trained model
            tokenizer = AutoTokenizer.from_pretrained(str(output_path))
            model = AutoModelForCausalLM.from_pretrained(
                str(output_path),
                torch_dtype=torch.float16,
                device_map="auto"
            )
            
            # Test query
            test_query = "Analyze the current market conditions and provide a trading recommendation."
            inputs = tokenizer(test_query, return_tensors="pt")
            
            with torch.no_grad():
                outputs = model.generate(
                    **inputs,
                    max_length=150,
                    temperature=0.7,
                    do_sample=True,
                    pad_token_id=tokenizer.eos_token_id
                )
            
            response = tokenizer.decode(outputs[0], skip_special_tokens=True)
            
            if len(response) > len(test_query) + 20:
                console.print(f"[green]✅ {model_info['name']} test successful[/green]")
                console.print(f"Sample response: {response[len(test_query):len(test_query)+100]}...")
                return True
            else:
                console.print(f"[red]❌ {model_info['name']} test failed - insufficient response[/red]")
                return False
                
        except Exception as e:
            console.print(f"[red]❌ {model_info['name']} test failed: {e}[/red]")
            return False
    
    async def train_all_remaining_models(self):
        """Train all remaining models"""
        console.print(Panel(
            "[bold blue]🚀 Training Remaining Models[/bold blue]\n\n"
            "Attempting to train:\n"
            "• DeepSeek R1 (Local)\n"
            "• Qwen 3\n" 
            "• Mistral 3\n\n"
            "Using comprehensive error handling and optimization",
            title="Remaining Model Training"
        ))
        
        # Fix configurations first
        fixes = self.fix_model_configs()
        console.print(f"[green]Applied {len(fixes)} configuration fixes[/green]")
        
        results = {}
        
        for model_info in self.models_to_train:
            console.print(f"\n[bold yellow]📋 Training {model_info['name']}...[/bold yellow]")
            
            # Train model
            success = await self.train_model_with_transformers(model_info)
            results[model_info['name']] = success
            
            if success:
                # Test trained model
                test_success = await self.test_trained_model(model_info)
                results[f"{model_info['name']}_test"] = test_success
            
            # Small delay between models
            await asyncio.sleep(2)
        
        return results
    
    def generate_final_report(self, results):
        """Generate final training report"""
        successful_models = [name for name, success in results.items() if success and not name.endswith('_test')]
        failed_models = [name for name, success in results.items() if not success and not name.endswith('_test')]
        
        console.print(Panel(
            f"[bold green]🎉 Remaining Model Training Complete![/bold green]\n\n"
            f"Successful: {len(successful_models)}/{len(self.models_to_train)}\n\n"
            f"✅ Successful Models: {', '.join(successful_models) if successful_models else 'None'}\n" +
            (f"❌ Failed Models: {', '.join(failed_models)}\n" if failed_models else "") +
            f"\n{'🚀 Ready for integration!' if successful_models else '⚠️ May need re-download'}",
            title="Training Results"
        ))
        
        return {
            "successful_models": successful_models,
            "failed_models": failed_models,
            "need_redownload": len(successful_models) == 0
        }

async def main():
    """Main training function"""
    console.print("[bold blue]🔧 Starting Remaining Model Training...[/bold blue]\n")
    
    trainer = RemainingModelTrainer()
    
    try:
        results = await trainer.train_all_remaining_models()
        final_report = trainer.generate_final_report(results)
        
        if final_report["need_redownload"]:
            console.print("\n[bold red]❌ ALL MODELS FAILED - RECOMMEND RE-DOWNLOAD[/bold red]")
        else:
            console.print(f"\n[bold green]✅ {len(final_report['successful_models'])} MODELS SUCCESSFULLY TRAINED[/bold green]")
        
        return final_report
        
    except Exception as e:
        console.print(f"[bold red]❌ CRITICAL ERROR: {e}[/bold red]")
        console.print("\n[bold red]RECOMMEND RE-DOWNLOAD OF ALL MODELS[/bold red]")
        return {"need_redownload": True, "successful_models": [], "failed_models": ["All models"]}

if __name__ == "__main__":
    results = asyncio.run(main())
