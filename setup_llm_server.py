#!/usr/bin/env python3
"""
Set up local LLM server with Qwen3 model
"""

import os
import sys
import subprocess
import time
import requests
from pathlib import Path
import json

# Add project root to path
project_root = Path(__file__).parent
sys.path.insert(0, str(project_root))

class LLMServerSetup:
    """Set up and manage local LLM server"""
    
    def __init__(self):
        self.server_port = 8000
        self.server_url = f"http://localhost:{self.server_port}"
        self.model_name = "Qwen/Qwen2.5-7B-Instruct"
        self.server_process = None
        
    def check_dependencies(self):
        """Check if required dependencies are installed"""
        print("🔍 Checking dependencies...")
        
        try:
            import torch
            print(f"✅ PyTorch: {torch.__version__}")
            
            import transformers
            print(f"✅ Transformers: {transformers.__version__}")
            
            # Check CUDA availability
            if torch.cuda.is_available():
                print(f"✅ CUDA: Available (GPU: {torch.cuda.get_device_name()})")
            else:
                print("⚠️ CUDA: Not available (using CPU)")
            
            return True
            
        except ImportError as e:
            print(f"❌ Missing dependency: {e}")
            return False
    
    def start_simple_server(self):
        """Start a simple LLM server using transformers"""
        print(f"🚀 Starting LLM server on port {self.server_port}...")
        
        server_script = f"""
import torch
from transformers import AutoTokenizer, AutoModelForCausalLM
from flask import Flask, request, jsonify
import threading
import time

app = Flask(__name__)

# Global model and tokenizer
model = None
tokenizer = None

def load_model():
    global model, tokenizer
    print("Loading model...")
    
    tokenizer = AutoTokenizer.from_pretrained("{self.model_name}")
    if tokenizer.pad_token is None:
        tokenizer.pad_token = tokenizer.eos_token
    
    model = AutoModelForCausalLM.from_pretrained(
        "{self.model_name}",
        torch_dtype=torch.float16 if torch.cuda.is_available() else torch.float32,
        device_map="auto" if torch.cuda.is_available() else None,
        trust_remote_code=True
    )
    print("Model loaded successfully!")

@app.route('/health', methods=['GET'])
def health():
    return jsonify({{"status": "healthy", "model_loaded": model is not None}})

@app.route('/generate', methods=['POST'])
def generate():
    if model is None or tokenizer is None:
        return jsonify({{"error": "Model not loaded"}}), 500
    
    try:
        data = request.json
        prompt = data.get('prompt', '')
        max_tokens = data.get('max_tokens', 256)
        temperature = data.get('temperature', 0.7)
        
        # Tokenize input
        inputs = tokenizer(prompt, return_tensors="pt", truncation=True, max_length=2048)
        
        # Move to device
        if torch.cuda.is_available():
            inputs = {{k: v.cuda() for k, v in inputs.items()}}
        
        # Generate
        with torch.no_grad():
            outputs = model.generate(
                **inputs,
                max_length=inputs['input_ids'].shape[1] + max_tokens,
                temperature=temperature,
                do_sample=True,
                pad_token_id=tokenizer.eos_token_id,
                eos_token_id=tokenizer.eos_token_id
            )
        
        # Decode response
        response = tokenizer.decode(outputs[0], skip_special_tokens=True)
        generated_text = response[len(prompt):].strip()
        
        return jsonify({{
            "response": generated_text,
            "prompt": prompt,
            "model": "{self.model_name}"
        }})
        
    except Exception as e:
        return jsonify({{"error": str(e)}}), 500

if __name__ == '__main__':
    # Load model in background
    threading.Thread(target=load_model, daemon=True).start()
    
    # Start server
    app.run(host='0.0.0.0', port={self.server_port}, debug=False)
"""
        
        # Write server script
        server_file = project_root / "llm_server.py"
        with open(server_file, 'w') as f:
            f.write(server_script)
        
        # Start server
        try:
            self.server_process = subprocess.Popen([
                sys.executable, str(server_file)
            ], stdout=subprocess.PIPE, stderr=subprocess.PIPE)
            
            print(f"✅ Server started with PID: {self.server_process.pid}")
            return True
            
        except Exception as e:
            print(f"❌ Failed to start server: {e}")
            return False
    
    def wait_for_server(self, timeout=120):
        """Wait for server to be ready"""
        print("⏳ Waiting for server to be ready...")
        
        start_time = time.time()
        while time.time() - start_time < timeout:
            try:
                response = requests.get(f"{self.server_url}/health", timeout=5)
                if response.status_code == 200:
                    data = response.json()
                    if data.get('model_loaded', False):
                        print("✅ Server is ready!")
                        return True
                    else:
                        print("⏳ Model still loading...")
                        
            except requests.exceptions.RequestException:
                pass
            
            time.sleep(5)
        
        print("❌ Server failed to start within timeout")
        return False
    
    def test_server(self):
        """Test the server with a sample request"""
        print("🧪 Testing server...")
        
        try:
            test_data = {
                "prompt": "What is the current outlook for the stock market?",
                "max_tokens": 100,
                "temperature": 0.7
            }
            
            response = requests.post(
                f"{self.server_url}/generate",
                json=test_data,
                timeout=30
            )
            
            if response.status_code == 200:
                result = response.json()
                print("✅ Server test successful!")
                print(f"Response: {result['response'][:100]}...")
                return True
            else:
                print(f"❌ Server test failed: {response.status_code}")
                return False
                
        except Exception as e:
            print(f"❌ Server test failed: {e}")
            return False
    
    def stop_server(self):
        """Stop the server"""
        if self.server_process:
            print("🛑 Stopping server...")
            self.server_process.terminate()
            self.server_process.wait()
            print("✅ Server stopped")
    
    def setup_config(self):
        """Update LLM configuration to use local server"""
        print("⚙️ Updating LLM configuration...")
        
        config_file = project_root / "config" / "llm_brain_config.yaml"
        
        # Read current config
        if config_file.exists():
            import yaml
            with open(config_file, 'r') as f:
                config = yaml.safe_load(f)
        else:
            config = {}
        
        # Update local provider configuration
        if 'providers' not in config:
            config['providers'] = {}
        
        config['providers']['local'] = {
            'enabled': True,
            'priority': 1,
            'base_url': self.server_url,
            'model_name': self.model_name,
            'max_tokens': 2048,
            'temperature': 0.7,
            'timeout': 30
        }
        
        # Set local as primary provider
        config['primary_provider'] = 'local'
        
        # Save updated config
        config_file.parent.mkdir(parents=True, exist_ok=True)
        with open(config_file, 'w') as f:
            yaml.dump(config, f, default_flow_style=False)
        
        print("✅ Configuration updated")

def main():
    """Main setup function"""
    print("🚀 Setting up Noryon LLM Server")
    print("=" * 50)
    
    setup = LLMServerSetup()
    
    # Check dependencies
    if not setup.check_dependencies():
        print("❌ Please install required dependencies")
        return False
    
    # Start server
    if not setup.start_simple_server():
        return False
    
    # Wait for server to be ready
    if not setup.wait_for_server():
        setup.stop_server()
        return False
    
    # Test server
    if not setup.test_server():
        setup.stop_server()
        return False
    
    # Update configuration
    setup.setup_config()
    
    print("\n🎉 LLM Server Setup Complete!")
    print("=" * 50)
    print(f"Server URL: {setup.server_url}")
    print(f"Health Check: {setup.server_url}/health")
    print(f"Generate Endpoint: {setup.server_url}/generate")
    print("\nTo stop the server, press Ctrl+C or run:")
    print("pkill -f llm_server.py")
    
    return True

if __name__ == "__main__":
    try:
        success = main()
        if success:
            print("\n⏳ Server is running. Press Ctrl+C to stop...")
            # Keep the script running
            while True:
                time.sleep(1)
    except KeyboardInterrupt:
        print("\n🛑 Shutting down...")
        sys.exit(0)
    except Exception as e:
        print(f"\n❌ Error: {e}")
        sys.exit(1)
