#!/usr/bin/env python3
"""
Direct Qwen Model Runner
Runs the Qwen3 model directly in terminal for interactive chat
"""

import os
import sys
from pathlib import Path

def run_qwen_chat():
    """Run Qwen model directly for interactive chat"""
    model_path = "qwen3/Qwen3-8B-Q8_0.gguf"  # Using Q8_0 quantization for highest quality
    
    print("🤖 Qwen3-8B Direct Chat")
    print("=" * 30)
    
    # Check if model exists
    if not os.path.exists(model_path):
        print(f"❌ Model file not found: {model_path}")
        print("\n📋 Available models in qwen3/:")
        qwen3_dir = Path("qwen3")
        if qwen3_dir.exists():
            for file in qwen3_dir.glob("*.gguf"):
                print(f"  📄 {file.name}")
        return False
    
    print(f"✅ Loading model: {model_path}")
    
    try:
        from llama_cpp import Llama
        
        # Load the model
        print("🔄 Loading Qwen3-8B model...")
        llm = Llama(
            model_path=model_path,
            n_ctx=4096,  # Context window
            n_threads=8,  # CPU threads
            verbose=False
        )
        
        print("✅ Model loaded successfully!")
        print("\n💬 Interactive Chat Mode")
        print("📝 Type 'quit', 'exit', or 'q' to stop")
        print("🔄 Type 'clear' to clear conversation history")
        print("-" * 50)
        
        conversation_history = []
        
        while True:
            try:
                # Get user input
                user_input = input("\n👤 You: ").strip()
                
                if user_input.lower() in ['quit', 'exit', 'q']:
                    print("👋 Goodbye!")
                    break
                    
                if user_input.lower() == 'clear':
                    conversation_history = []
                    print("🧹 Conversation history cleared!")
                    continue
                    
                if not user_input:
                    continue
                
                # Add user message to history
                conversation_history.append(f"Human: {user_input}")
                
                # Create a simple, direct prompt
                if len(conversation_history) > 6:  # Keep last 6 exchanges
                    conversation_history = conversation_history[-6:]
                
                # Use a simple format that works better with GGUF models
                prompt = f"Question: {user_input}\nAnswer:"
                
                print("🤔 Qwen is thinking...")
                
                # Generate response with Qwen3 recommended parameters
                response = llm(
                    prompt,
                    max_tokens=512,
                    temperature=0.7,
                    top_p=0.8,
                    top_k=20,
                    min_p=0.0,
                    presence_penalty=1.5,  # Critical for Qwen3 to prevent repetition
                    stop=["Question:", "\n\n", "Human:"],
                    echo=False
                )
                
                ai_response = response['choices'][0]['text'].strip()
                print(f"🤖 Qwen: {ai_response}")
                
                # Add AI response to history
                conversation_history.append(f"Assistant: {ai_response}")
                
            except KeyboardInterrupt:
                print("\n\n👋 Chat interrupted. Goodbye!")
                break
            except Exception as e:
                print(f"❌ Error during chat: {e}")
                continue
                
    except ImportError as e:
        print(f"❌ Missing dependency: {e}")
        print("💡 Install with: pip install llama-cpp-python")
        return False
    except Exception as e:
        print(f"❌ Error loading model: {e}")
        return False
    
    return True

def main():
    """Main function"""
    try:
        success = run_qwen_chat()
        sys.exit(0 if success else 1)
    except KeyboardInterrupt:
        print("\n\n👋 Goodbye!")
        sys.exit(0)

if __name__ == "__main__":
    main()